# Generated by Django 5.2.4 on 2025-08-04 09:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0002_remove_patient_last_visit'),
    ]

    operations = [
        migrations.AddField(
            model_name='patient',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='patient',
            name='marital_status',
            field=models.CharField(blank=True, choices=[('Single', 'Single'), ('Married', 'Married'), ('Divorced', 'Divorced'), ('Widowed', 'Widowed')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='patient',
            name='nationality',
            field=models.CharField(blank=True, default='Zimbabwean', max_length=100),
        ),
    ]
