from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from datetime import datetime, timedelta
from functools import wraps
import csv
import io
from .models import Patient, MedicalRecord, Prescription, Appointment
from .forms import PatientForm, MedicalRecordForm, PrescriptionForm, AppointmentForm

# Import for Excel export
try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

# Import for PDF export
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False


def safe_query(query_func, default=None):
    """
    Safely execute a database query and return default value if it fails.
    This helps handle cases where tables are empty or don't exist yet.
    """
    try:
        return query_func()
    except Exception as e:
        print(f"Database query failed: {e}")
        return default if default is not None else 0


def safe_count(queryset):
    """Safely count queryset results"""
    return safe_query(lambda: queryset.count(), 0)


def safe_filter(queryset, **filters):
    """Safely filter queryset"""
    try:
        return queryset.filter(**filters)
    except Exception as e:
        print(f"Filter failed: {e}")
        return queryset.none()


def handle_empty_data(view_func):
    """
    Decorator to handle views gracefully when there's no data or database errors.
    This ensures the application works for new users with empty databases.
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            return view_func(request, *args, **kwargs)
        except Exception as e:
            # Log the error for debugging
            print(f"View error in {view_func.__name__}: {e}")

            # For AJAX requests, return JSON error
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'error': 'Unable to load data. Please try again.',
                    'debug': str(e) if request.user.is_superuser else None
                })

            # For regular requests, show error message and redirect to dashboard
            messages.error(request, 'Unable to load the requested page. Please try again.')
            return redirect('api:dashboard')

    return wrapper


@login_required
def dashboard_index(request):
    """Main dashboard view with error handling for empty data"""
    # Clear any existing messages to prevent interference with command center
    storage = messages.get_messages(request)
    for message in storage:
        pass  # This consumes and clears all messages
    # Get actual statistics from database
    total_patients = safe_count(Patient.objects.filter(is_active=True))
    todays_appointments = safe_count(Appointment.objects.filter(
        date=timezone.now().date()
    ))
    total_records = safe_count(MedicalRecord.objects.all())
    active_prescriptions = safe_count(Prescription.objects.filter(status='active'))
    
    # Additional stats for consistency
    pending_appointments = safe_count(Appointment.objects.filter(
        status='scheduled',
        date__gte=timezone.now().date()
    ))
    new_records_this_week = safe_count(MedicalRecord.objects.filter(
        date__gte=timezone.now().date() - timedelta(days=7)
    ))
    expiring_prescriptions = safe_count(Prescription.objects.filter(
        status='active',
        end_date__lte=timezone.now().date() + timedelta(days=7)
    ))

    # Get recent activities from actual database
    recent_activities = []

    # Get recent patient registrations with error handling
    try:
        recent_patients = Patient.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:3]

        for patient in recent_patients:
            recent_activities.append({
                'icon': 'user-plus',
                'title': 'New Patient Registered',
                'description': f'{patient.full_name} has been registered in the system',
                'timestamp': patient.created_at
            })
    except Exception:
        # If no patients or error, continue without recent patient activities
        pass

    # Get recent completed appointments with error handling
    try:
        recent_appointments = Appointment.objects.filter(
            status='completed',
            created_at__gte=timezone.now() - timedelta(days=7)
        ).select_related('patient').order_by('-created_at')[:3]

        for appointment in recent_appointments:
            recent_activities.append({
                'icon': 'calendar-check',
                'title': 'Appointment Completed',
                'description': f'Consultation completed for {appointment.patient.full_name}',
                'timestamp': appointment.created_at
            })
    except Exception:
        # If no appointments or error, continue without recent appointment activities
        pass

    # Get recent medical records with error handling
    try:
        recent_records = MedicalRecord.objects.filter(
            date__gte=timezone.now().date() - timedelta(days=7)
        ).select_related('patient').order_by('-date')[:3]

        for record in recent_records:
            recent_activities.append({
                'icon': 'file-medical',
                'title': 'Medical Record Added',
                'description': f'New medical record for {record.patient.full_name}',
                'timestamp': record.date
            })
    except Exception:
        # If no medical records or error, continue without recent record activities
        pass

    # Sort activities by timestamp and limit to 5
    try:
        recent_activities.sort(key=lambda x: x['timestamp'], reverse=True)
        recent_activities = recent_activities[:5]
    except Exception:
        # If sorting fails, provide empty list
        recent_activities = []

    # Get upcoming appointments
    upcoming_appointments = Appointment.objects.filter(
        status='scheduled',
        date__gte=timezone.now().date()
    ).order_by('date', 'time')[:5]

    context = {
        'total_patients': total_patients,
        'todays_appointments': todays_appointments,
        'total_records': total_records,
        'active_prescriptions': active_prescriptions,
        'pending_appointments': pending_appointments,
        'new_records': new_records_this_week,
        'expiring_prescriptions': expiring_prescriptions,
        'recent_activities': recent_activities,
        'upcoming_appointments': upcoming_appointments,
    }

    return render(request, 'dashboard/index.html', context)


@login_required
def analytics_dashboard(request):
    """Analytics dashboard view with safe queries"""
    # Get analytics data with error handling
    total_patients = safe_count(Patient.objects.filter(is_active=True))
    monthly_visits = safe_count(MedicalRecord.objects.filter(
        date__gte=timezone.now() - timedelta(days=30)
    ))
    active_prescriptions = safe_count(Prescription.objects.filter(status='active'))

    # Get top diagnoses from actual medical records with error handling
    top_diagnoses = safe_query(
        lambda: [item['diagnosis'] for item in MedicalRecord.objects.filter(
            diagnosis__isnull=False
        ).exclude(diagnosis='').values('diagnosis').annotate(
            count=Count('diagnosis')
        ).order_by('-count')[:5]],
        ['Hypertension', 'Diabetes', 'Malaria', 'Upper Respiratory Infection', 'Gastritis']
    )

    # Get top facilities from actual medical records with error handling
    top_facilities = safe_query(
        lambda: [item['facility_name'] for item in MedicalRecord.objects.filter(
            facility_name__isnull=False
        ).exclude(facility_name='').values('facility_name').annotate(
            count=Count('facility_name')
        ).order_by('-count')[:4]],
        ['Harare Central Hospital', 'Parirenyatwa Hospital', 'Chitungwiza Hospital', 'Mpilo Hospital']
    )

    context = {
        'total_patients': total_patients,
        'monthly_visits': monthly_visits,
        'active_prescriptions': active_prescriptions,
        'top_diagnoses': top_diagnoses,
        'top_facilities': top_facilities,
    }

    return render(request, 'dashboard/analytics.html', context)


@login_required
def patients_list(request):
    """List all patients with search and filtering"""
    patients = Patient.objects.filter(is_active=True).order_by('-created_at')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        patients = patients.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(zimhealth_id__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(phone_number__icontains=search_query)
        )

    # Filtering
    gender_filter = request.GET.get('gender')
    if gender_filter:
        patients = patients.filter(gender=gender_filter)

    blood_type_filter = request.GET.get('blood_type')
    if blood_type_filter:
        patients = patients.filter(blood_type=blood_type_filter)

    # Pagination
    paginator = Paginator(patients, 20)  # Show 20 patients per page
    page_number = request.GET.get('page')
    patients = paginator.get_page(page_number)

    context = {
        'patients': patients,
        'search_query': search_query,
        'gender_filter': gender_filter,
        'blood_type_filter': blood_type_filter,
    }

    return render(request, 'api/patients.html', context)


@login_required
def patient_detail(request, zimhealth_id):
    """Patient detail view"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    # Get recent medical records
    recent_records = patient.medical_records.order_by('-date')[:5]

    # Get upcoming appointments
    upcoming_appointments = patient.appointments.filter(
        status='scheduled',
        date__gte=timezone.now().date()
    ).order_by('date', 'time')[:5]

    # Get active prescriptions count
    active_prescriptions_count = Prescription.objects.filter(
        medical_record__patient=patient,
        status='active'
    ).count()

    context = {
        'patient': patient,
        'recent_records': recent_records,
        'upcoming_appointments': upcoming_appointments,
        'active_prescriptions_count': active_prescriptions_count,
    }

    return render(request, 'api/patient_detail.html', context)


@login_required
def medical_records_list(request):
    """List all medical records with search and filtering"""
    medical_records = MedicalRecord.objects.select_related('patient').order_by('-date')

    # Calculate statistics
    total_records = medical_records.count()
    recent_records = medical_records.filter(
        date__gte=timezone.now().date() - timedelta(days=7)
    ).count()

    # Critical cases - records with specific critical diagnoses or notes
    critical_records = 0
    try:
        critical_keywords = ['emergency', 'critical', 'severe', 'urgent', 'acute']
        critical_records = medical_records.filter(
            Q(diagnosis__icontains='emergency') |
            Q(diagnosis__icontains='critical') |
            Q(diagnosis__icontains='severe') |
            Q(notes__icontains='severe') |
            Q(notes__icontains='critical') |
            Q(notes__icontains='emergency') |
            Q(notes__icontains='urgent')
        ).count()
    except Exception as e:
        # Handle any database errors gracefully
        critical_records = 0

    # Pending records - records without complete diagnosis or treatment
    pending_records = 0
    try:
        pending_records = medical_records.filter(
            Q(diagnosis__isnull=True) |
            Q(diagnosis='') |
            Q(treatment__isnull=True) |
            Q(treatment='')
        ).count()
    except Exception as e:
        # Handle any database errors gracefully
        pending_records = 0

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        try:
            medical_records = medical_records.filter(
                Q(patient__first_name__icontains=search_query) |
                Q(patient__last_name__icontains=search_query) |
                Q(patient__zimhealth_id__icontains=search_query) |
                Q(diagnosis__icontains=search_query) |
                Q(doctor_name__icontains=search_query) |
                Q(facility_name__icontains=search_query) |
                Q(notes__icontains=search_query)
            )
        except Exception as e:
            # If search fails, return all records
            pass

    # Date filtering
    from_date = request.GET.get('from_date')
    if from_date:
        try:
            medical_records = medical_records.filter(date__gte=from_date)
        except Exception as e:
            # Invalid date format, ignore filter
            pass

    to_date = request.GET.get('to_date')
    if to_date:
        try:
            medical_records = medical_records.filter(date__lte=to_date)
        except Exception as e:
            # Invalid date format, ignore filter
            pass

    # Facility filtering
    facility_filter = request.GET.get('facility')
    if facility_filter:
        try:
            medical_records = medical_records.filter(facility_name__icontains=facility_filter)
        except Exception as e:
            # Filter error, ignore
            pass

    # Pagination
    try:
        paginator = Paginator(medical_records, 10)  # Show 10 records per page
        page_number = request.GET.get('page', 1)
        medical_records = paginator.get_page(page_number)
    except Exception as e:
        # If pagination fails, show first 10 records
        medical_records = medical_records[:10]

    context = {
        'medical_records': medical_records,
        'search_query': search_query,
        'from_date': from_date,
        'to_date': to_date,
        'facility_filter': facility_filter,
        'total_records': total_records,
        'recent_records': recent_records,
        'critical_records': critical_records,
        'pending_records': pending_records,
    }

    return render(request, 'api/medical_records.html', context)


@login_required
def appointments_list(request):
    """List all appointments"""
    appointments = Appointment.objects.select_related('patient').order_by('-date', '-time')

    # Get statistics
    todays_appointments = appointments.filter(date=timezone.now().date()).count()
    pending_appointments = appointments.filter(status='scheduled').count()
    completed_appointments = appointments.filter(status='completed').count()
    cancelled_appointments = appointments.filter(status='cancelled').count()

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        appointments = appointments.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(doctor_name__icontains=search_query) |
            Q(facility_name__icontains=search_query)
        )

    # Status filtering
    status_filter = request.GET.get('status')
    if status_filter:
        appointments = appointments.filter(status=status_filter)

    # Type filtering
    type_filter = request.GET.get('type')
    if type_filter:
        appointments = appointments.filter(appointment_type=type_filter)

    # Date filtering
    date_filter = request.GET.get('date')
    if date_filter:
        appointments = appointments.filter(date=date_filter)

    # Pagination
    paginator = Paginator(appointments, 15)  # Show 15 appointments per page
    page_number = request.GET.get('page')
    appointments = paginator.get_page(page_number)

    context = {
        'appointments': appointments,
        'todays_appointments': todays_appointments,
        'pending_appointments': pending_appointments,
        'completed_appointments': completed_appointments,
        'cancelled_appointments': cancelled_appointments,
        'search_query': search_query,
        'status_filter': status_filter,
        'type_filter': type_filter,
        'date_filter': date_filter,
    }

    return render(request, 'api/appointments.html', context)


@login_required
def prescriptions_list(request):
    """List all prescriptions"""
    prescriptions = Prescription.objects.select_related(
        'medical_record__patient'
    ).order_by('-created_at')

    # Get statistics
    active_prescriptions = prescriptions.filter(status='active').count()
    expiring_prescriptions = prescriptions.filter(
        status='active',
        end_date__lte=timezone.now().date() + timedelta(days=7)
    ).count()
    completed_prescriptions = prescriptions.filter(status='completed').count()
    discontinued_prescriptions = prescriptions.filter(status='discontinued').count()

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        prescriptions = prescriptions.filter(
            Q(medication__icontains=search_query) |
            Q(medical_record__patient__first_name__icontains=search_query) |
            Q(medical_record__patient__last_name__icontains=search_query) |
            Q(medical_record__doctor_name__icontains=search_query)
        )

    # Status filtering
    status_filter = request.GET.get('status')
    if status_filter:
        prescriptions = prescriptions.filter(status=status_filter)

    # Frequency filtering
    frequency_filter = request.GET.get('frequency')
    if frequency_filter:
        prescriptions = prescriptions.filter(frequency=frequency_filter)

    # Date filtering
    start_date = request.GET.get('start_date')
    if start_date:
        prescriptions = prescriptions.filter(start_date__gte=start_date)

    # Pagination
    paginator = Paginator(prescriptions, 10)  # Show 10 prescriptions per page
    page_number = request.GET.get('page')
    prescriptions = paginator.get_page(page_number)

    context = {
        'prescriptions': prescriptions,
        'active_prescriptions': active_prescriptions,
        'expiring_prescriptions': expiring_prescriptions,
        'completed_prescriptions': completed_prescriptions,
        'discontinued_prescriptions': discontinued_prescriptions,
        'search_query': search_query,
        'status_filter': status_filter,
        'frequency_filter': frequency_filter,
        'start_date': start_date,
    }

    return render(request, 'api/prescriptions.html', context)


@login_required
def patient_create(request):
    """Create a new patient"""
    if request.method == 'POST':
        form = PatientForm(request.POST)
        if form.is_valid():
            # Save patient (allergies are now handled in the form's save method)
            patient = form.save()

            # Check if this is an AJAX request
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                # Return JSON response for AJAX
                return JsonResponse({
                    'success': True,
                    'patient': {
                        'zimhealth_id': patient.zimhealth_id,
                        'full_name': patient.full_name,
                        'initials': f"{patient.first_name[0]}{patient.last_name[0]}".upper(),
                        'date_of_birth': patient.date_of_birth.strftime('%Y-%m-%d'),
                        'gender': patient.get_gender_display(),
                        'blood_type': patient.blood_type or 'Unknown',
                        'phone_number': patient.phone_number,
                        'emergency_contact': patient.emergency_contact,
                        'qr_code_url': patient.qr_code.url if patient.qr_code else None,
                        'registration_date': patient.registration_date.strftime('%B %d, %Y at %I:%M %p')
                    },
                    'message': f'Patient {patient.full_name} has been successfully registered!'
                })

            # For non-AJAX requests, redirect with success message and show popup
            messages.success(request, f'Patient {patient.full_name} has been successfully registered!')
            return redirect('api:patient_create_success', zimhealth_id=patient.zimhealth_id)
        else:
            # Form has errors
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'errors': form.errors,
                    'message': 'Please correct the errors below and try again.'
                })
    else:
        form = PatientForm()

    context = {
        'form': form,
        'title': 'Add New Patient'
    }
    return render(request, 'api/patient_form.html', context)


@login_required
def patient_create_success(request, zimhealth_id):
    """Success page after patient creation with QR code popup"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)
    
    context = {
        'patient': patient,
        'show_success_popup': True,
        'title': 'Patient Registration Successful'
    }
    return render(request, 'api/patient_create_success.html', context)


@login_required
def patient_delete(request, zimhealth_id):
    """Delete a patient via AJAX popup"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        patient_name = patient.full_name
        patient.delete()

        # Return JSON response for AJAX
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'Patient {patient_name} has been successfully deleted.'
            })

        # Fallback for non-AJAX requests
        messages.success(request, f'Patient {patient_name} has been successfully deleted.')
        return redirect('api:patients')

    # This shouldn't be reached with the popup implementation
    return redirect('api:patients')


@login_required
def patient_edit(request, zimhealth_id):
    """Edit an existing patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = PatientForm(request.POST, instance=patient)
        if form.is_valid():
            patient = form.save()
            messages.success(request, f'Patient {patient.full_name} has been successfully updated.')
            return redirect('api:patient_detail', zimhealth_id=patient.zimhealth_id)
    else:
        form = PatientForm(instance=patient)

    context = {
        'form': form,
        'patient': patient,
        'title': f'Edit Patient - {patient.full_name}',
        'common_allergies': ['Penicillin', 'Peanuts', 'Shellfish', 'Latex', 'Dust', 'Pollen', 'Eggs', 'Milk']
    }
    return render(request, 'api/patient_form.html', context)


@login_required
def medical_record_create(request, zimhealth_id=None):
    """Create a new medical record"""
    patient = None
    if zimhealth_id:
        patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = MedicalRecordForm(request.POST)
        if form.is_valid():
            try:
                medical_record = form.save(commit=False)
                medical_record.created_by = request.user
                medical_record.save()

                messages.success(request, f'Medical record for {medical_record.patient.full_name} has been created successfully.')

                # Check if user wants to add prescription
                if 'save_and_add_prescription' in request.POST:
                    return redirect('api:prescription_create_for_record', medical_record_id=medical_record.id)

                return redirect('api:medical_record_detail', record_id=medical_record.id)
            except Exception as e:
                messages.error(request, f'Error creating medical record: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = MedicalRecordForm()
        if patient:
            form.fields['patient'].initial = patient

    context = {
        'form': form,
        'patient': patient,
        'title': 'New Medical Record',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/medical_record_form.html', context)


# Duplicate function removed - using the one at line 2231


@login_required
def prescription_create(request, medical_record_id=None):
    """Create a new prescription"""
    medical_record = None
    if medical_record_id:
        medical_record = get_object_or_404(MedicalRecord, id=medical_record_id)

    if request.method == 'POST':
        form = PrescriptionForm(request.POST)
        if form.is_valid():
            prescription = form.save(commit=False)
            prescription.prescribed_by = request.user
            prescription.save()

            messages.success(request, f'Prescription for {prescription.medical_record.patient.full_name} has been created successfully.')
            return redirect('api:prescription_detail', prescription_id=prescription.id)
    else:
        form = PrescriptionForm()
        if medical_record:
            form.fields['medical_record'].initial = medical_record

    context = {
        'form': form,
        'medical_record': medical_record,
        'title': 'New Prescription',
        'medical_records': MedicalRecord.objects.select_related('patient').order_by('-date')[:50]
    }
    return render(request, 'api/prescription_form.html', context)


@login_required
def appointment_create(request, zimhealth_id=None):
    """Create a new appointment"""
    patient = None
    if zimhealth_id:
        patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = AppointmentForm(request.POST)
        if form.is_valid():
            appointment = form.save(commit=False)
            appointment.created_by = request.user
            appointment.save()

            messages.success(request, f'Appointment for {appointment.patient.full_name} has been scheduled successfully.')
            return redirect('api:appointments')
    else:
        form = AppointmentForm()
        if patient:
            form.fields['patient'].initial = patient

    context = {
        'form': form,
        'patient': patient,
        'title': 'New Appointment',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/appointment_form.html', context)


@login_required
@require_POST
def appointment_update_status(request, appointment_id):
    """Update appointment status via AJAX"""
    appointment = get_object_or_404(Appointment, id=appointment_id)
    new_status = request.POST.get('status')

    if new_status in ['completed', 'cancelled', 'no_show']:
        appointment.status = new_status
        if new_status == 'cancelled':
            appointment.cancelled_at = timezone.now()
            appointment.cancelled_by = request.user
            appointment.cancellation_reason = request.POST.get('reason', '')
        appointment.save()

        messages.success(request, f'Appointment status updated to {new_status}.')
        return JsonResponse({'success': True, 'status': new_status})

    return JsonResponse({'success': False, 'error': 'Invalid status'})


@login_required
@require_POST
def prescription_update_status(request, prescription_id):
    """Update prescription status via AJAX"""
    prescription = get_object_or_404(Prescription, id=prescription_id)
    new_status = request.POST.get('status')

    if new_status in ['active', 'completed', 'discontinued', 'on_hold']:
        prescription.status = new_status
        prescription.save()

        messages.success(request, f'Prescription status updated to {new_status}.')
        return JsonResponse({'success': True, 'status': new_status})

    return JsonResponse({'success': False, 'error': 'Invalid status'})


@login_required
def patient_qr_code(request, zimhealth_id):
    """Display patient QR code"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    context = {
        'patient': patient,
    }
    return render(request, 'api/patient_qr_code.html', context)


@login_required
def patient_id_card(request, zimhealth_id):
    """Generate printable ID card for patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    context = {
        'patient': patient,
    }
    return render(request, 'api/patient_id_card.html', context)


@login_required
def check_data_consistency(request):
    """Check and fix data consistency issues"""
    issues_found = []
    issues_fixed = []

    try:
        # Check for patients without QR codes
        patients_without_qr = Patient.objects.filter(qr_code__isnull=True).count()
        if patients_without_qr > 0:
            issues_found.append(f"{patients_without_qr} patients without QR codes")

            # Fix: Generate QR codes for patients without them
            for patient in Patient.objects.filter(qr_code__isnull=True):
                patient.generate_qr_code()
                patient.save()
            issues_fixed.append(f"Generated QR codes for {patients_without_qr} patients")

        # Check for orphaned medical records
        orphaned_records = MedicalRecord.objects.filter(patient__isnull=True).count()
        if orphaned_records > 0:
            issues_found.append(f"{orphaned_records} orphaned medical records")
            # Note: Don't auto-delete, just report

        # Check for orphaned prescriptions
        orphaned_prescriptions = Prescription.objects.filter(medical_record__isnull=True).count()
        if orphaned_prescriptions > 0:
            issues_found.append(f"{orphaned_prescriptions} orphaned prescriptions")

        # Check for orphaned appointments
        orphaned_appointments = Appointment.objects.filter(patient__isnull=True).count()
        if orphaned_appointments > 0:
            issues_found.append(f"{orphaned_appointments} orphaned appointments")

        # Update patient last visit dates
        patients_updated = 0
        for patient in Patient.objects.all():
            last_record = patient.medical_records.order_by('-date').first()
            if last_record and patient.last_visit != last_record.date:
                patient.last_visit = last_record.date
                patient.save(update_fields=['last_visit'])
                patients_updated += 1

        if patients_updated > 0:
            issues_fixed.append(f"Updated last visit dates for {patients_updated} patients")

        return JsonResponse({
            'success': True,
            'issues_found': issues_found,
            'issues_fixed': issues_fixed,
            'total_issues': len(issues_found),
            'total_fixes': len(issues_fixed)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
def search_patients_ajax(request):
    """AJAX endpoint for patient search"""
    query = request.GET.get('q', '')
    patients = []

    if query and len(query) >= 2:
        patient_results = Patient.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(zimhealth_id__icontains=query) |
            Q(national_id__icontains=query)
        ).filter(is_active=True)[:10]

        patients = [
            {
                'id': p.pk,                    # Use primary key (zimhealth_id) for form selection
                'zimhealth_id': p.zimhealth_id,
                'full_name': p.full_name,
                'national_id': p.national_id or '',
                'phone_number': p.phone_number,
                'age': p.age
            }
            for p in patient_results
        ]

    return JsonResponse({'patients': patients})


@login_required
def medical_record_detail(request, record_id):
    """Detailed view of a medical record"""
    medical_record = get_object_or_404(MedicalRecord, id=record_id)
    prescriptions = medical_record.prescriptions.all()

    context = {
        'medical_record': medical_record,
        'prescriptions': prescriptions,
    }
    return render(request, 'api/medical_record_detail.html', context)


@login_required
def appointment_detail(request, appointment_id):
    """Detailed view of an appointment"""
    appointment = get_object_or_404(Appointment, id=appointment_id)

    context = {
        'appointment': appointment,
    }
    return render(request, 'api/appointment_detail.html', context)


@login_required
def prescription_detail(request, prescription_id):
    """Detailed view of a prescription"""
    prescription = get_object_or_404(Prescription, id=prescription_id)

    context = {
        'prescription': prescription,
    }
    return render(request, 'api/prescription_detail.html', context)


@login_required
def patient_medical_history(request, zimhealth_id):
    """Complete medical history for a patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    # Get all medical records
    medical_records = patient.medical_records.order_by('-date')

    # Get all prescriptions
    prescriptions = Prescription.objects.filter(
        medical_record__patient=patient
    ).order_by('-created_at')

    # Get all appointments
    appointments = patient.appointments.order_by('-date', '-time')

    # Pagination for medical records
    paginator = Paginator(medical_records, 10)
    page_number = request.GET.get('page')
    medical_records = paginator.get_page(page_number)

    context = {
        'patient': patient,
        'medical_records': medical_records,
        'prescriptions': prescriptions[:10],  # Latest 10 prescriptions
        'appointments': appointments[:10],    # Latest 10 appointments
        'total_records': patient.medical_records.count(),
        'total_prescriptions': prescriptions.count(),
        'total_appointments': appointments.count(),
    }
    return render(request, 'api/patient_medical_history.html', context)


@login_required
def dashboard_stats_ajax(request):
    """AJAX endpoint for dashboard statistics"""
    today = timezone.now().date()

    stats = {
        'total_patients': Patient.objects.filter(is_active=True).count(),
        'todays_appointments': Appointment.objects.filter(
            date=today, status='scheduled'
        ).count(),
        'pending_appointments': Appointment.objects.filter(
            status='scheduled', date__gte=today
        ).count(),
        'active_prescriptions': Prescription.objects.filter(status='active').count(),
        'new_records_today': MedicalRecord.objects.filter(date__date=today).count(),
        'expiring_prescriptions': Prescription.objects.filter(
            status='active',
            end_date__lte=today + timedelta(days=7)
        ).count(),
    }

    return JsonResponse(stats)


# Missing AJAX Endpoints Implementation

@login_required
def quick_patient_search_ajax(request):
    """AJAX endpoint for quick patient search with minimal data"""
    query = request.GET.get('q', '')
    patients = []

    if query and len(query) >= 2:
        patient_results = Patient.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(zimhealth_id__icontains=query)
        ).filter(is_active=True)[:5]

        patients = [
            {
                'zimhealth_id': p.zimhealth_id,
                'full_name': p.full_name,
                'age': p.age
            }
            for p in patient_results
        ]

    return JsonResponse({'patients': patients})


@login_required
def patient_autocomplete_ajax(request):
    """AJAX endpoint for patient autocomplete in forms"""
    query = request.GET.get('term', '')
    suggestions = []

    if query and len(query) >= 2:
        patients = Patient.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(zimhealth_id__icontains=query)
        ).filter(is_active=True)[:10]

        suggestions = [
            {
                'id': p.zimhealth_id,
                'label': f"{p.full_name} ({p.zimhealth_id})",
                'value': p.full_name
            }
            for p in patients
        ]

    return JsonResponse(suggestions, safe=False)


@login_required
def patients_filter_ajax(request):
    """AJAX endpoint for real-time patient filtering with all parameters"""
    patients = Patient.objects.filter(is_active=True).select_related('user')

    # Search functionality
    search_query = request.GET.get('search', '').strip()
    if search_query:
        patients = patients.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(zimhealth_id__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(phone_number__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    # Gender filter
    gender = request.GET.get('gender')
    if gender and gender != '':
        patients = patients.filter(gender=gender.upper())

    # Blood type filter
    blood_type = request.GET.get('blood_type')
    if blood_type and blood_type != '':
        patients = patients.filter(blood_type=blood_type)

    # Date range filtering (registration date)
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            patients = patients.filter(registration_date__gte=from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            # Include the entire day
            patients = patients.filter(registration_date__lte=to_date + timedelta(days=1))
        except ValueError:
            pass

    # Age range filtering
    age_min = request.GET.get('age_min')
    if age_min:
        try:
            min_age = int(age_min)
            max_birth_date = timezone.now().date() - timedelta(days=min_age*365)
            patients = patients.filter(date_of_birth__lte=max_birth_date)
        except (ValueError, TypeError):
            pass

    age_max = request.GET.get('age_max')
    if age_max:
        try:
            max_age = int(age_max)
            min_birth_date = timezone.now().date() - timedelta(days=max_age*365)
            patients = patients.filter(date_of_birth__gte=min_birth_date)
        except (ValueError, TypeError):
            pass

    # Order by most recent first
    patients = patients.order_by('-created_at')

    # Get total count before pagination
    total_count = patients.count()

    # Pagination
    page = request.GET.get('page', 1)
    per_page = 20

    try:
        page = int(page)
    except (ValueError, TypeError):
        page = 1

    start = (page - 1) * per_page
    end = start + per_page
    patients_page = patients[start:end]

    # Prepare response data with complete patient information
    patient_data = []
    for p in patients_page:
        # Calculate last visit date
        last_visit = None
        try:
            last_record = p.medical_records.order_by('-date').first()
            if last_record:
                last_visit = last_record.date.strftime('%b %d, %Y')
        except:
            pass

        patient_data.append({
            'zimhealth_id': p.zimhealth_id,
            'full_name': p.full_name,
            'first_name': p.first_name,
            'last_name': p.last_name,
            'national_id': p.national_id or '',
            'phone_number': p.phone_number,
            'email': p.email or '',
            'address': p.address or '',
            'gender': p.get_gender_display(),
            'gender_code': p.gender,
            'age': p.age,
            'blood_type': p.blood_type or '',
            'registration_date': p.registration_date.strftime('%b %d, %Y'),
            'last_visit': last_visit,
            'is_active': p.is_active,
            'detail_url': f'/api/patients/{p.zimhealth_id}/',
            'edit_url': f'/api/patients/{p.zimhealth_id}/edit/',
            'medical_record_url': f'/api/patients/{p.zimhealth_id}/medical-records/new/',
            'appointment_url': f'/api/patients/{p.zimhealth_id}/appointments/new/'
        })

    # Calculate pagination info
    total_pages = (total_count + per_page - 1) // per_page
    has_next = page < total_pages
    has_previous = page > 1

    return JsonResponse({
        'success': True,
        'patients': patient_data,
        'pagination': {
            'current_page': page,
            'total_pages': total_pages,
            'total_count': total_count,
            'per_page': per_page,
            'has_next': has_next,
            'has_previous': has_previous,
            'start_index': start + 1 if patient_data else 0,
            'end_index': start + len(patient_data)
        },
        'filters_applied': {
            'search': search_query,
            'gender': gender,
            'blood_type': blood_type,
            'date_from': date_from,
            'date_to': date_to,
            'age_min': age_min,
            'age_max': age_max
        }
    })


@login_required
def dashboard_refresh_stats_ajax(request):
    """AJAX endpoint for refreshing dashboard statistics"""
    today = timezone.now().date()

    # Get comprehensive stats
    stats = {
        'total_patients': Patient.objects.filter(is_active=True).count(),
        'todays_appointments': Appointment.objects.filter(
            date=today, status='scheduled'
        ).count(),
        'pending_appointments': Appointment.objects.filter(
            status='scheduled', date__gte=today
        ).count(),
        'active_prescriptions': Prescription.objects.filter(status='active').count(),
        'new_records_today': MedicalRecord.objects.filter(date__date=today).count(),
        'expiring_prescriptions': Prescription.objects.filter(
            status='active',
            end_date__lte=today + timedelta(days=7)
        ).count(),
        'completed_appointments_today': Appointment.objects.filter(
            date=today, status='completed'
        ).count(),
        'cancelled_appointments_today': Appointment.objects.filter(
            date=today, status='cancelled'
        ).count(),
        'last_updated': timezone.now().strftime('%H:%M:%S')
    }

    return JsonResponse(stats)


@login_required
def dashboard_recent_activities_ajax(request):
    """AJAX endpoint for loading recent activities"""
    activities = []

    # Get recent patient registrations
    recent_patients = Patient.objects.filter(
        created_at__gte=timezone.now() - timedelta(days=7)
    ).order_by('-created_at')[:5]

    for patient in recent_patients:
        activities.append({
            'icon': 'user-plus',
            'title': 'New Patient Registered',
            'description': f'{patient.full_name} has been registered in the system',
            'timestamp': patient.created_at.isoformat(),
            'url': f'/api/patients/{patient.zimhealth_id}/'
        })

    # Get recent appointments
    recent_appointments = Appointment.objects.filter(
        created_at__gte=timezone.now() - timedelta(days=7),
        status='completed'
    ).select_related('patient').order_by('-created_at')[:5]

    for appointment in recent_appointments:
        activities.append({
            'icon': 'calendar-check',
            'title': 'Appointment Completed',
            'description': f'Consultation completed for {appointment.patient.full_name}',
            'timestamp': appointment.created_at.isoformat(),
            'url': f'/api/appointments/{appointment.id}/'
        })

    # Get recent medical records
    recent_records = MedicalRecord.objects.filter(
        date__gte=timezone.now().date() - timedelta(days=7)
    ).select_related('patient').order_by('-date')[:5]

    for record in recent_records:
        activities.append({
            'icon': 'file-medical',
            'title': 'Medical Record Added',
            'description': f'New medical record for {record.patient.full_name}',
            'timestamp': record.date.isoformat(),
            'url': f'/api/medical-records/{record.id}/'
        })

    # Sort activities by timestamp
    activities.sort(key=lambda x: x['timestamp'], reverse=True)

    return JsonResponse({'activities': activities[:10]})


@login_required
def dashboard_system_status_ajax(request):
    """AJAX endpoint for system status monitoring"""
    try:
        # Check database connectivity
        db_status = 'healthy'
        patient_count = Patient.objects.count()

        # Check recent activity
        recent_activity = MedicalRecord.objects.filter(
            date__gte=timezone.now().date() - timedelta(days=1)
        ).exists()

        # System metrics
        status = {
            'database': {
                'status': db_status,
                'patient_count': patient_count,
                'last_backup': 'System backup completed 2 hours ago'
            },
            'services': {
                'api_status': 'operational',
                'qr_scanner': 'operational',
                'notifications': 'operational'
            },
            'recent_activity': recent_activity,
            'uptime': '99.9%',
            'last_check': timezone.now().strftime('%H:%M:%S')
        }

        return JsonResponse(status)
    except Exception as e:
        return JsonResponse({
            'error': 'System check failed',
            'database': {'status': 'error'},
            'services': {'api_status': 'error'},
            'last_check': timezone.now().strftime('%H:%M:%S')
        })


def extract_zimhealth_id_from_qr(qr_data):
    """Extract ZimHealth ID from QR code data"""
    import re

    if not qr_data:
        return None

    # Direct ZimHealth ID format: ZH-YYYY-XXXXXX
    if re.match(r'^ZH-\d{4}-\d{6}$', qr_data):
        return qr_data

    # Multi-line format with ZimHealth-ID prefix
    lines = qr_data.split('\n')
    for line in lines:
        if line.startswith('ZimHealth-ID:'):
            zimhealth_id = line.replace('ZimHealth-ID:', '').strip()
            if re.match(r'^ZH-\d{4}-\d{6}$', zimhealth_id):
                return zimhealth_id

    # Try to find ZH-YYYY-XXXXXX pattern anywhere in the string
    match = re.search(r'ZH-\d{4}-\d{6}', qr_data)
    if match:
        return match.group(0)

    return None


@login_required
def scan_qr_code_ajax(request):
    """AJAX endpoint for QR code scanning"""
    if request.method == 'POST':
        qr_data = request.POST.get('qr_data', '')

        if not qr_data:
            return JsonResponse({'success': False, 'error': 'No QR data provided'})

        try:
            # Extract ZimHealth ID from QR data
            zimhealth_id = extract_zimhealth_id_from_qr(qr_data)

            if not zimhealth_id:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid QR code format. Could not extract ZimHealth ID.'
                })

            # Find patient by extracted ZimHealth ID
            patient = Patient.objects.get(zimhealth_id=zimhealth_id, is_active=True)

            return JsonResponse({
                'success': True,
                'patient': {
                    'id': patient.pk,              # Add patient ID for form selection
                    'zimhealth_id': patient.zimhealth_id,
                    'full_name': patient.full_name,
                    'age': patient.age,
                    'gender': patient.get_gender_display(),
                    'phone_number': patient.phone_number,
                    'blood_type': patient.blood_type or 'Unknown'
                },
                'redirect_url': f'/api/patients/{patient.zimhealth_id}/'
            })
        except Patient.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Patient not found with this QR code'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': 'Invalid QR code format'
            })

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
def patient_qr_data_ajax(request, zimhealth_id):
    """AJAX endpoint for patient QR data retrieval"""
    try:
        patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

        qr_data = {
            'zimhealth_id': patient.zimhealth_id,
            'full_name': patient.full_name,
            'date_of_birth': patient.date_of_birth.isoformat(),
            'gender': patient.gender,
            'blood_type': patient.blood_type or '',
            'emergency_contact': patient.emergency_contact_phone or '',
            'allergies': patient.allergies or '',
            'generated_at': timezone.now().isoformat()
        }

        return JsonResponse({
            'success': True,
            'qr_data': qr_data,
            'qr_string': patient.zimhealth_id  # Simple QR code with just ID
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Failed to generate QR data'
        })


@login_required
def notifications_list(request):
    """Main notifications view"""
    # For now, create mock notifications until notification model is implemented
    notifications = [
        {
            'id': 1,
            'title': 'New Patient Registration',
            'message': 'A new patient has been registered in the system',
            'type': 'info',
            'created_at': timezone.now() - timedelta(minutes=30),
            'is_read': False
        },
        {
            'id': 2,
            'title': 'Appointment Reminder',
            'message': 'You have 5 appointments scheduled for today',
            'type': 'warning',
            'created_at': timezone.now() - timedelta(hours=2),
            'is_read': False
        },
        {
            'id': 3,
            'title': 'System Update',
            'message': 'System maintenance completed successfully',
            'type': 'success',
            'created_at': timezone.now() - timedelta(hours=4),
            'is_read': True
        }
    ]

    context = {
        'notifications': notifications,
        'unread_count': sum(1 for n in notifications if not n['is_read'])
    }

    return render(request, 'api/notifications.html', context)


@login_required
def unread_notifications_count_ajax(request):
    """AJAX endpoint for unread notification count"""
    # Mock implementation until notification model is created
    unread_count = 3  # This should be replaced with actual database query

    return JsonResponse({'unread_count': unread_count})


@login_required
@require_POST
def mark_notification_read_ajax(request, notification_id):
    """AJAX endpoint for marking notification as read"""
    # Mock implementation until notification model is created
    return JsonResponse({
        'success': True,
        'message': 'Notification marked as read'
    })


@login_required
def patient_demographics_ajax(request):
    """AJAX endpoint for patient demographics analytics"""
    # Gender distribution
    gender_stats = Patient.objects.filter(is_active=True).values('gender').annotate(
        count=Count('gender')
    ).order_by('gender')

    # Age distribution
    today = timezone.now().date()
    age_ranges = {
        '0-18': 0,
        '19-35': 0,
        '36-50': 0,
        '51-65': 0,
        '65+': 0
    }

    patients = Patient.objects.filter(is_active=True)
    for patient in patients:
        age = patient.age
        if age <= 18:
            age_ranges['0-18'] += 1
        elif age <= 35:
            age_ranges['19-35'] += 1
        elif age <= 50:
            age_ranges['36-50'] += 1
        elif age <= 65:
            age_ranges['51-65'] += 1
        else:
            age_ranges['65+'] += 1

    # Blood type distribution
    blood_type_stats = Patient.objects.filter(
        is_active=True,
        blood_type__isnull=False
    ).exclude(blood_type='').values('blood_type').annotate(
        count=Count('blood_type')
    ).order_by('blood_type')

    return JsonResponse({
        'gender_distribution': list(gender_stats),
        'age_distribution': age_ranges,
        'blood_type_distribution': list(blood_type_stats),
        'total_patients': patients.count()
    })


@login_required
def appointment_trends_ajax(request):
    """AJAX endpoint for appointment trends analytics"""
    # Last 30 days appointment trends
    thirty_days_ago = timezone.now().date() - timedelta(days=30)

    # Daily appointment counts
    daily_appointments = []
    for i in range(30):
        date = thirty_days_ago + timedelta(days=i)
        count = Appointment.objects.filter(date=date).count()
        daily_appointments.append({
            'date': date.isoformat(),
            'count': count
        })

    # Appointment status distribution
    status_stats = Appointment.objects.filter(
        date__gte=thirty_days_ago
    ).values('status').annotate(
        count=Count('status')
    ).order_by('status')

    # Appointment type distribution
    type_stats = Appointment.objects.filter(
        date__gte=thirty_days_ago
    ).values('appointment_type').annotate(
        count=Count('appointment_type')
    ).order_by('appointment_type')

    return JsonResponse({
        'daily_trends': daily_appointments,
        'status_distribution': list(status_stats),
        'type_distribution': list(type_stats),
        'total_appointments': Appointment.objects.filter(date__gte=thirty_days_ago).count()
    })


@login_required
@require_POST
def patients_bulk_action_ajax(request):
    """AJAX endpoint for bulk patient operations"""
    action = request.POST.get('action')
    patient_ids = request.POST.getlist('patient_ids[]')

    if not action or not patient_ids:
        return JsonResponse({
            'success': False,
            'error': 'Missing action or patient IDs'
        })

    try:
        patients = Patient.objects.filter(zimhealth_id__in=patient_ids)

        if action == 'deactivate':
            patients.update(is_active=False)
            message = f'{patients.count()} patients deactivated successfully'
        elif action == 'activate':
            patients.update(is_active=True)
            message = f'{patients.count()} patients activated successfully'
        elif action == 'export':
            # This would typically generate a CSV/Excel file
            message = f'Export initiated for {patients.count()} patients'
        else:
            return JsonResponse({
                'success': False,
                'error': 'Invalid action'
            })

        return JsonResponse({
            'success': True,
            'message': message,
            'affected_count': patients.count()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Bulk operation failed'
        })


@login_required
@require_POST
def update_appointment_status_ajax(request, appointment_id):
    """AJAX endpoint for updating appointment status"""
    try:
        appointment = get_object_or_404(Appointment, id=appointment_id)
        new_status = request.POST.get('status')

        if new_status not in ['scheduled', 'completed', 'cancelled', 'no_show']:
            return JsonResponse({
                'success': False,
                'error': 'Invalid status'
            })

        appointment.status = new_status
        if new_status == 'cancelled':
            appointment.cancelled_at = timezone.now()
            appointment.cancellation_reason = request.POST.get('reason', '')

        appointment.save()

        return JsonResponse({
            'success': True,
            'message': f'Appointment status updated to {new_status}',
            'new_status': new_status
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Failed to update appointment status'
        })


@login_required
@require_POST
def update_prescription_status_ajax(request, prescription_id):
    """AJAX endpoint for updating prescription status"""
    try:
        prescription = get_object_or_404(Prescription, id=prescription_id)
        new_status = request.POST.get('status')

        if new_status not in ['active', 'completed', 'discontinued', 'on_hold']:
            return JsonResponse({
                'success': False,
                'error': 'Invalid status'
            })

        prescription.status = new_status
        prescription.save()

        return JsonResponse({
            'success': True,
            'message': f'Prescription status updated to {new_status}',
            'new_status': new_status
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Failed to update prescription status'
        })


# Convenience endpoints for creating records for specific patients

@login_required
def medical_record_create_for_patient(request, zimhealth_id):
    """Create medical record for specific patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = MedicalRecordForm(request.POST)
        if form.is_valid():
            medical_record = form.save(commit=False)
            medical_record.patient = patient
            medical_record.save()

            messages.success(request, f'Medical record created for {patient.full_name}')
            return redirect('api:patient_detail', zimhealth_id=patient.zimhealth_id)
    else:
        form = MedicalRecordForm()
        form.fields['patient'].initial = patient
        form.fields['patient'].widget.attrs['readonly'] = True

    context = {
        'form': form,
        'patient': patient,
        'title': f'New Medical Record - {patient.full_name}',
        'patients': [patient]  # Only show this patient
    }
    return render(request, 'api/medical_record_form.html', context)


@login_required
def appointment_create_for_patient(request, zimhealth_id):
    """Create appointment for specific patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = AppointmentForm(request.POST)
        if form.is_valid():
            appointment = form.save(commit=False)
            appointment.patient = patient
            appointment.save()

            messages.success(request, f'Appointment scheduled for {patient.full_name}')
            return redirect('api:patient_detail', zimhealth_id=patient.zimhealth_id)
    else:
        form = AppointmentForm()
        form.fields['patient'].initial = patient
        form.fields['patient'].widget.attrs['readonly'] = True

    context = {
        'form': form,
        'patient': patient,
        'title': f'New Appointment - {patient.full_name}',
        'patients': [patient]  # Only show this patient
    }
    return render(request, 'api/appointment_form.html', context)


@login_required
def prescription_create_for_record(request, record_id):
    """Create prescription for specific medical record"""
    medical_record = get_object_or_404(MedicalRecord, id=record_id)

    if request.method == 'POST':
        form = PrescriptionForm(request.POST)
        if form.is_valid():
            prescription = form.save(commit=False)
            prescription.medical_record = medical_record
            prescription.prescribed_by = request.user
            prescription.save()

            messages.success(request, f'Prescription created for {medical_record.patient.full_name}')
            return redirect('api:medical_record_detail', record_id=medical_record.id)
    else:
        form = PrescriptionForm()
        form.fields['medical_record'].initial = medical_record
        form.fields['medical_record'].widget.attrs['readonly'] = True

    context = {
        'form': form,
        'medical_record': medical_record,
        'title': f'New Prescription - {medical_record.patient.full_name}',
        'medical_records': [medical_record]  # Only show this record
    }
    return render(request, 'api/prescription_form.html', context)


# Advanced dashboard endpoints

@login_required
def dashboard_critical_alerts_ajax(request):
    """AJAX endpoint for critical alerts"""
    alerts = []

    # Check for expiring prescriptions
    expiring_prescriptions = Prescription.objects.filter(
        status='active',
        end_date__lte=timezone.now().date() + timedelta(days=3)
    ).select_related('medical_record__patient')

    for prescription in expiring_prescriptions:
        alerts.append({
            'type': 'prescription_expiring',
            'severity': 'high',
            'title': 'Prescription Expiring Soon',
            'message': f'{prescription.medication} for {prescription.medical_record.patient.full_name} expires on {prescription.end_date}',
            'patient_id': prescription.medical_record.patient.zimhealth_id,
            'created_at': timezone.now().isoformat()
        })

    # Check for missed appointments
    missed_appointments = Appointment.objects.filter(
        date__lt=timezone.now().date(),
        status='scheduled'
    ).select_related('patient')

    for appointment in missed_appointments:
        alerts.append({
            'type': 'missed_appointment',
            'severity': 'medium',
            'title': 'Missed Appointment',
            'message': f'{appointment.patient.full_name} missed appointment on {appointment.date}',
            'patient_id': appointment.patient.zimhealth_id,
            'created_at': timezone.now().isoformat()
        })

    # Check for patients without recent visits
    inactive_patients = Patient.objects.filter(
        is_active=True,
        medical_records__isnull=True
    ) | Patient.objects.filter(
        is_active=True,
        medical_records__date__lt=timezone.now().date() - timedelta(days=365)
    ).distinct()

    for patient in inactive_patients[:5]:  # Limit to 5
        alerts.append({
            'type': 'inactive_patient',
            'severity': 'low',
            'title': 'Inactive Patient',
            'message': f'{patient.full_name} has no recent medical records',
            'patient_id': patient.zimhealth_id,
            'created_at': timezone.now().isoformat()
        })

    return JsonResponse({
        'alerts': alerts[:10],  # Limit to 10 most critical
        'total_count': len(alerts)
    })


@login_required
def dashboard_alert_details_ajax(request):
    """AJAX endpoint for alert details"""
    alert_type = request.GET.get('type')

    if alert_type == 'prescription_expiring':
        prescriptions = Prescription.objects.filter(
            status='active',
            end_date__lte=timezone.now().date() + timedelta(days=7)
        ).select_related('medical_record__patient')

        details = [{
            'patient_name': p.medical_record.patient.full_name,
            'medication': p.medication,
            'expiry_date': p.end_date.isoformat(),
            'days_remaining': (p.end_date - timezone.now().date()).days
        } for p in prescriptions]

        return JsonResponse({
            'type': alert_type,
            'title': 'Expiring Prescriptions',
            'details': details
        })

    return JsonResponse({'error': 'Invalid alert type'})


@login_required
def medical_records_filter_ajax(request):
    """AJAX endpoint for real-time medical records filtering"""
    medical_records = MedicalRecord.objects.select_related('patient').order_by('-date')

    # Search functionality
    search_query = request.GET.get('search', '').strip()
    if search_query:
        medical_records = medical_records.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(patient__zimhealth_id__icontains=search_query) |
            Q(diagnosis__icontains=search_query) |
            Q(doctor_name__icontains=search_query) |
            Q(facility_name__icontains=search_query) |
            Q(notes__icontains=search_query) |
            Q(treatment__icontains=search_query)
        )

    # Type filtering (based on diagnosis or notes content)
    record_type = request.GET.get('type')
    if record_type and record_type != '':
        type_filters = {
            'consultation': Q(diagnosis__icontains='consultation') | Q(notes__icontains='consultation'),
            'emergency': Q(diagnosis__icontains='emergency') | Q(notes__icontains='emergency'),
            'follow_up': Q(diagnosis__icontains='follow') | Q(notes__icontains='follow'),
            'surgery': Q(diagnosis__icontains='surgery') | Q(notes__icontains='surgery'),
            'screening': Q(diagnosis__icontains='screening') | Q(notes__icontains='screening')
        }
        if record_type in type_filters:
            medical_records = medical_records.filter(type_filters[record_type])

    # Status filtering (based on keywords in diagnosis/notes)
    status = request.GET.get('status')
    if status and status != '':
        status_filters = {
            'active': Q(notes__icontains='active') | Q(diagnosis__icontains='ongoing'),
            'pending': Q(notes__icontains='pending') | Q(diagnosis__icontains='pending'),
            'archived': Q(notes__icontains='archived') | Q(diagnosis__icontains='resolved'),
            'critical': Q(diagnosis__icontains='critical') | Q(diagnosis__icontains='severe') |
                       Q(notes__icontains='critical') | Q(notes__icontains='severe')
        }
        if status in status_filters:
            medical_records = medical_records.filter(status_filters[status])

    # Facility filtering
    facility = request.GET.get('facility')
    if facility and facility != '':
        facility_filters = {
            'central': Q(facility_name__icontains='central') | Q(facility_name__icontains='hospital'),
            'medical': Q(facility_name__icontains='medical') | Q(facility_name__icontains='center'),
            'clinic': Q(facility_name__icontains='clinic') | Q(facility_name__icontains='health')
        }
        if facility in facility_filters:
            medical_records = medical_records.filter(facility_filters[facility])
        else:
            medical_records = medical_records.filter(facility_name__icontains=facility)

    # Date range filtering
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            medical_records = medical_records.filter(date__gte=from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            medical_records = medical_records.filter(date__lte=to_date)
        except ValueError:
            pass

    # Get total count before pagination
    total_count = medical_records.count()

    # Pagination
    page = request.GET.get('page', 1)
    per_page = 10

    try:
        page = int(page)
    except (ValueError, TypeError):
        page = 1

    start = (page - 1) * per_page
    end = start + per_page
    records_page = medical_records[start:end]

    # Prepare response data
    records_data = []
    for record in records_page:
        # Determine record type based on content
        record_type = 'consultation'  # default
        diagnosis_lower = record.diagnosis.lower() if record.diagnosis else ''
        notes_lower = record.notes.lower() if record.notes else ''

        if 'emergency' in diagnosis_lower or 'emergency' in notes_lower:
            record_type = 'emergency'
        elif 'surgery' in diagnosis_lower or 'surgery' in notes_lower:
            record_type = 'surgery'
        elif 'follow' in diagnosis_lower or 'follow' in notes_lower:
            record_type = 'follow_up'
        elif 'screening' in diagnosis_lower or 'screening' in notes_lower:
            record_type = 'screening'

        # Determine status based on content
        status = 'active'  # default
        if 'critical' in diagnosis_lower or 'severe' in diagnosis_lower:
            status = 'critical'
        elif 'pending' in diagnosis_lower or 'pending' in notes_lower:
            status = 'pending'
        elif 'resolved' in diagnosis_lower or 'archived' in notes_lower:
            status = 'archived'

        records_data.append({
            'id': record.id,
            'patient_name': record.patient.full_name,
            'patient_zimhealth_id': record.patient.zimhealth_id,
            'date': record.date.strftime('%b %d, %Y'),
            'time': record.date.strftime('%H:%M'),
            'doctor_name': record.doctor_name,
            'facility_name': record.facility_name,
            'diagnosis': record.diagnosis,
            'treatment': record.treatment,
            'notes': record.notes[:100] + '...' if len(record.notes) > 100 else record.notes,
            'record_type': record_type,
            'status': status,
            'detail_url': f'/api/medical-records/{record.id}/',
            'edit_url': f'/api/medical-records/{record.id}/edit/',
            'patient_url': f'/api/patients/{record.patient.zimhealth_id}/',
            'prescription_url': f'/api/medical-records/{record.id}/prescriptions/new/'
        })

    # Calculate pagination info
    total_pages = (total_count + per_page - 1) // per_page
    has_next = page < total_pages
    has_previous = page > 1

    return JsonResponse({
        'success': True,
        'records': records_data,
        'pagination': {
            'current_page': page,
            'total_pages': total_pages,
            'total_count': total_count,
            'per_page': per_page,
            'has_next': has_next,
            'has_previous': has_previous,
            'start_index': start + 1 if records_data else 0,
            'end_index': start + len(records_data)
        },
        'filters_applied': {
            'search': search_query,
            'type': record_type,
            'status': status,
            'facility': facility,
            'date_from': date_from,
            'date_to': date_to
        }
    })


@login_required
def appointments_filter_ajax(request):
    """AJAX endpoint for real-time appointments filtering"""
    appointments = Appointment.objects.select_related('patient').order_by('-date', '-time')

    # Search functionality
    search_query = request.GET.get('search', '').strip()
    if search_query:
        appointments = appointments.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(patient__zimhealth_id__icontains=search_query) |
            Q(doctor_name__icontains=search_query) |
            Q(facility_name__icontains=search_query) |
            Q(reason__icontains=search_query) |
            Q(department__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    # Status filtering
    status = request.GET.get('status')
    if status and status != '':
        appointments = appointments.filter(status=status)

    # Type filtering
    appointment_type = request.GET.get('type')
    if appointment_type and appointment_type != '':
        appointments = appointments.filter(appointment_type=appointment_type)

    # Priority filtering
    priority = request.GET.get('priority')
    if priority and priority != '':
        appointments = appointments.filter(priority=priority)

    # Date filtering
    date_filter = request.GET.get('date')
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            appointments = appointments.filter(date=filter_date)
        except ValueError:
            pass

    # Date range filtering
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            appointments = appointments.filter(date__gte=from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            appointments = appointments.filter(date__lte=to_date)
        except ValueError:
            pass

    # Doctor filtering
    doctor = request.GET.get('doctor')
    if doctor and doctor != '':
        appointments = appointments.filter(doctor_name__icontains=doctor)

    # Facility filtering
    facility = request.GET.get('facility')
    if facility and facility != '':
        appointments = appointments.filter(facility_name__icontains=facility)

    # Get total count before pagination
    total_count = appointments.count()

    # Pagination
    page = request.GET.get('page', 1)
    per_page = 15

    try:
        page = int(page)
    except (ValueError, TypeError):
        page = 1

    start = (page - 1) * per_page
    end = start + per_page
    appointments_page = appointments[start:end]

    # Prepare response data
    appointments_data = []
    for appointment in appointments_page:
        # Calculate time until appointment
        time_until = None
        if appointment.date >= timezone.now().date():
            if appointment.date == timezone.now().date():
                current_time = timezone.now().time()
                if appointment.time > current_time:
                    time_until = "Today"
                else:
                    time_until = "Past due"
            else:
                days_until = (appointment.date - timezone.now().date()).days
                if days_until == 1:
                    time_until = "Tomorrow"
                else:
                    time_until = f"In {days_until} days"
        else:
            time_until = "Past"

        appointments_data.append({
            'id': appointment.id,
            'patient_name': appointment.patient.full_name,
            'patient_zimhealth_id': appointment.patient.zimhealth_id,
            'doctor_name': appointment.doctor_name,
            'facility_name': appointment.facility_name,
            'department': appointment.department or '',
            'date': appointment.date.strftime('%b %d, %Y'),
            'time': appointment.time.strftime('%H:%M'),
            'appointment_type': appointment.get_appointment_type_display(),
            'appointment_type_code': appointment.appointment_type,
            'priority': appointment.get_priority_display(),
            'priority_code': appointment.priority,
            'status': appointment.get_status_display(),
            'status_code': appointment.status,
            'reason': appointment.reason,
            'notes': appointment.notes[:100] + '...' if len(appointment.notes) > 100 else appointment.notes,
            'estimated_duration': appointment.estimated_duration,
            'time_until': time_until,
            'detail_url': f'/api/appointments/{appointment.id}/',
            'edit_url': f'/api/appointments/{appointment.id}/edit/',
            'patient_url': f'/api/patients/{appointment.patient.zimhealth_id}/',
            'cancel_url': f'/api/appointments/{appointment.id}/update-status/',
            'created_at': appointment.created_at.strftime('%b %d, %Y %H:%M')
        })

    # Calculate pagination info
    total_pages = (total_count + per_page - 1) // per_page
    has_next = page < total_pages
    has_previous = page > 1

    # Calculate statistics for the filtered results
    filtered_stats = {
        'total_count': total_count,
        'today_count': appointments.filter(date=timezone.now().date()).count(),
        'scheduled_count': appointments.filter(status='scheduled').count(),
        'completed_count': appointments.filter(status='completed').count(),
        'cancelled_count': appointments.filter(status='cancelled').count(),
        'no_show_count': appointments.filter(status='no_show').count()
    }

    return JsonResponse({
        'success': True,
        'appointments': appointments_data,
        'pagination': {
            'current_page': page,
            'total_pages': total_pages,
            'total_count': total_count,
            'per_page': per_page,
            'has_next': has_next,
            'has_previous': has_previous,
            'start_index': start + 1 if appointments_data else 0,
            'end_index': start + len(appointments_data)
        },
        'statistics': filtered_stats,
        'filters_applied': {
            'search': search_query,
            'status': status,
            'type': appointment_type,
            'priority': priority,
            'date': date_filter,
            'date_from': date_from,
            'date_to': date_to,
            'doctor': doctor,
            'facility': facility
        }
    })


@login_required
def appointment_edit(request, appointment_id):
    """Edit an existing appointment"""
    appointment = get_object_or_404(Appointment, id=appointment_id)

    if request.method == 'POST':
        form = AppointmentForm(request.POST, instance=appointment)
        if form.is_valid():
            appointment = form.save()
            messages.success(request, f'Appointment for {appointment.patient.full_name} has been updated successfully.')
            return redirect('api:appointment_detail', appointment_id=appointment.id)
    else:
        form = AppointmentForm(instance=appointment)

    context = {
        'form': form,
        'appointment': appointment,
        'title': f'Edit Appointment - {appointment.patient.full_name}',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/appointment_form.html', context)


@login_required
def appointment_delete(request, appointment_id):
    """Delete an appointment via AJAX"""
    appointment = get_object_or_404(Appointment, id=appointment_id)

    if request.method == 'POST':
        patient_name = appointment.patient.full_name
        appointment_date = appointment.date.strftime('%b %d, %Y')
        appointment_time = appointment.time.strftime('%H:%M')

        appointment.delete()

        # Return JSON response for AJAX
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'Appointment for {patient_name} on {appointment_date} at {appointment_time} has been deleted.'
            })

        # Fallback for non-AJAX requests
        messages.success(request, f'Appointment for {patient_name} has been deleted successfully.')
        return redirect('api:appointments')

    # This shouldn't be reached with the AJAX implementation
    return redirect('api:appointments')


@login_required
def appointment_reschedule(request, appointment_id):
    """Reschedule an appointment"""
    appointment = get_object_or_404(Appointment, id=appointment_id)

    if request.method == 'POST':
        new_date = request.POST.get('new_date')
        new_time = request.POST.get('new_time')
        reason = request.POST.get('reason', '')

        if new_date and new_time:
            try:
                appointment.date = datetime.strptime(new_date, '%Y-%m-%d').date()
                appointment.time = datetime.strptime(new_time, '%H:%M').time()
                appointment.status = 'rescheduled'
                appointment.notes = f"Rescheduled: {reason}\n\n{appointment.notes}" if appointment.notes else f"Rescheduled: {reason}"
                appointment.save()

                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': True,
                        'message': f'Appointment rescheduled to {appointment.date.strftime("%b %d, %Y")} at {appointment.time.strftime("%H:%M")}'
                    })

                messages.success(request, 'Appointment has been rescheduled successfully.')
                return redirect('api:appointment_detail', appointment_id=appointment.id)
            except ValueError:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid date or time format'
                    })
                messages.error(request, 'Invalid date or time format.')

    context = {
        'appointment': appointment,
        'title': f'Reschedule Appointment - {appointment.patient.full_name}'
    }
    return render(request, 'api/appointment_reschedule.html', context)


@login_required
def dashboard_performance_metrics_ajax(request):
    """AJAX endpoint for performance metrics"""
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    metrics = {
        'patient_growth': {
            'this_week': Patient.objects.filter(
                created_at__date__gte=week_ago
            ).count(),
            'this_month': Patient.objects.filter(
                created_at__date__gte=month_ago
            ).count(),
            'total': Patient.objects.filter(is_active=True).count()
        },
        'appointment_metrics': {
            'completion_rate': 0,
            'average_daily': 0,
            'this_week': Appointment.objects.filter(
                date__gte=week_ago
            ).count()
        },
        'system_performance': {
            'response_time': '120ms',
            'uptime': '99.9%',
            'active_users': 1,  # This would be calculated from session data
            'database_size': '2.4MB'
        }
    }

    # Calculate appointment completion rate
    total_appointments = Appointment.objects.filter(date__gte=month_ago).count()
    completed_appointments = Appointment.objects.filter(
        date__gte=month_ago,
        status='completed'
    ).count()

    if total_appointments > 0:
        metrics['appointment_metrics']['completion_rate'] = round(
            (completed_appointments / total_appointments) * 100, 1
        )

    # Calculate average daily appointments
    if total_appointments > 0:
        metrics['appointment_metrics']['average_daily'] = round(
            total_appointments / 30, 1
        )

    return JsonResponse(metrics)


@login_required
def medical_record_edit(request, record_id):
    """Edit an existing medical record"""
    medical_record = get_object_or_404(MedicalRecord, id=record_id)

    if request.method == 'POST':
        form = MedicalRecordForm(request.POST, instance=medical_record)
        if form.is_valid():
            medical_record = form.save()
            messages.success(request, f'Medical record for {medical_record.patient.full_name} has been updated.')
            return redirect('api:medical_record_detail', record_id=medical_record.id)
    else:
        form = MedicalRecordForm(instance=medical_record)

    context = {
        'form': form,
        'medical_record': medical_record,
        'title': f'Edit Medical Record - {medical_record.patient.full_name}',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/medical_record_form.html', context)


@login_required
def medical_record_delete(request, record_id):
    """Delete a medical record via AJAX"""
    medical_record = get_object_or_404(MedicalRecord, id=record_id)

    if request.method == 'POST':
        patient_name = medical_record.patient.full_name
        record_date = medical_record.date.strftime('%b %d, %Y')

        # Check if there are associated prescriptions
        prescriptions_count = medical_record.prescriptions.count()

        medical_record.delete()

        # Return JSON response for AJAX
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            message = f'Medical record for {patient_name} from {record_date} has been deleted.'
            if prescriptions_count > 0:
                message += f' {prescriptions_count} associated prescription(s) were also deleted.'

            return JsonResponse({
                'success': True,
                'message': message
            })

        # Fallback for non-AJAX requests
        messages.success(request, f'Medical record for {patient_name} has been deleted successfully.')
        return redirect('api:medical_records')

    # This shouldn't be reached with the AJAX implementation
    return redirect('api:medical_records')


@login_required
def prescription_edit(request, prescription_id):
    """Edit an existing prescription"""
    prescription = get_object_or_404(Prescription, id=prescription_id)

    if request.method == 'POST':
        form = PrescriptionForm(request.POST, instance=prescription)
        if form.is_valid():
            prescription = form.save()
            messages.success(request, f'Prescription for {prescription.medical_record.patient.full_name} has been updated successfully.')
            return redirect('api:prescription_detail', prescription_id=prescription.id)
    else:
        form = PrescriptionForm(instance=prescription)

    context = {
        'form': form,
        'prescription': prescription,
        'title': f'Edit Prescription - {prescription.medical_record.patient.full_name}',
        'medical_records': MedicalRecord.objects.select_related('patient').order_by('-date')
    }
    return render(request, 'api/prescription_form.html', context)


@login_required
def prescription_delete(request, prescription_id):
    """Delete a prescription via AJAX"""
    prescription = get_object_or_404(Prescription, id=prescription_id)

    if request.method == 'POST':
        patient_name = prescription.medical_record.patient.full_name
        medication = prescription.medication

        prescription.delete()

        # Return JSON response for AJAX
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'Prescription ({medication}) for {patient_name} has been deleted.'
            })

        # Fallback for non-AJAX requests
        messages.success(request, f'Prescription for {patient_name} has been deleted successfully.')
        return redirect('api:prescriptions')

    # This shouldn't be reached with the AJAX implementation
    return redirect('api:prescriptions')


@login_required
def prescriptions_filter_ajax(request):
    """AJAX endpoint for real-time prescriptions filtering"""
    prescriptions = Prescription.objects.select_related(
        'medical_record__patient'
    ).order_by('-created_at')

    # Search functionality
    search_query = request.GET.get('search', '').strip()
    if search_query:
        prescriptions = prescriptions.filter(
            Q(medication__icontains=search_query) |
            Q(medical_record__patient__first_name__icontains=search_query) |
            Q(medical_record__patient__last_name__icontains=search_query) |
            Q(medical_record__patient__zimhealth_id__icontains=search_query) |
            Q(dosage__icontains=search_query) |
            Q(frequency__icontains=search_query) |
            Q(instructions__icontains=search_query)
        )

    # Status filtering
    status = request.GET.get('status')
    if status and status != '':
        prescriptions = prescriptions.filter(status=status)

    # Frequency filtering
    frequency = request.GET.get('frequency')
    if frequency and frequency != '':
        prescriptions = prescriptions.filter(frequency__icontains=frequency)

    # Date range filtering
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            prescriptions = prescriptions.filter(start_date__gte=from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            prescriptions = prescriptions.filter(end_date__lte=to_date)
        except ValueError:
            pass

    # Medication type filtering (basic categorization)
    medication_type = request.GET.get('medication_type')
    if medication_type and medication_type != '':
        type_filters = {
            'antibiotic': Q(medication__icontains='antibiotic') | Q(medication__icontains='amoxicillin') |
                         Q(medication__icontains='penicillin') | Q(medication__icontains='azithromycin'),
            'painkiller': Q(medication__icontains='paracetamol') | Q(medication__icontains='ibuprofen') |
                         Q(medication__icontains='aspirin') | Q(medication__icontains='pain'),
            'vitamin': Q(medication__icontains='vitamin') | Q(medication__icontains='supplement'),
            'chronic': Q(medication__icontains='diabetes') | Q(medication__icontains='hypertension') |
                      Q(medication__icontains='metformin') | Q(medication__icontains='insulin')
        }
        if medication_type in type_filters:
            prescriptions = prescriptions.filter(type_filters[medication_type])

    # Get total count before pagination
    total_count = prescriptions.count()

    # Pagination
    page = request.GET.get('page', 1)
    per_page = 12

    try:
        page = int(page)
    except (ValueError, TypeError):
        page = 1

    start = (page - 1) * per_page
    end = start + per_page
    prescriptions_page = prescriptions[start:end]

    # Prepare response data
    prescriptions_data = []
    for prescription in prescriptions_page:
        # Calculate days remaining
        days_remaining = None
        if prescription.end_date:
            days_remaining = (prescription.end_date - timezone.now().date()).days
            if days_remaining < 0:
                days_remaining = 0

        # Determine urgency level
        urgency = 'normal'
        if prescription.status == 'active' and days_remaining is not None:
            if days_remaining <= 3:
                urgency = 'urgent'
            elif days_remaining <= 7:
                urgency = 'warning'

        prescriptions_data.append({
            'id': prescription.id,
            'patient_name': prescription.medical_record.patient.full_name,
            'patient_zimhealth_id': prescription.medical_record.patient.zimhealth_id,
            'medication': prescription.medication,
            'dosage': prescription.dosage,
            'frequency': prescription.frequency,
            'duration': prescription.duration,
            'instructions': prescription.instructions[:100] + '...' if len(prescription.instructions) > 100 else prescription.instructions,
            'status': prescription.get_status_display(),
            'status_code': prescription.status,
            'start_date': prescription.start_date.strftime('%b %d, %Y'),
            'end_date': prescription.end_date.strftime('%b %d, %Y') if prescription.end_date else 'Ongoing',
            'days_remaining': days_remaining,
            'urgency': urgency,
            'prescribed_by': prescription.prescribed_by.get_full_name() if prescription.prescribed_by else 'Unknown',
            'detail_url': f'/api/prescriptions/{prescription.id}/',
            'edit_url': f'/api/prescriptions/{prescription.id}/edit/',
            'medical_record_url': f'/api/medical-records/{prescription.medical_record.id}/',
            'patient_url': f'/api/patients/{prescription.medical_record.patient.zimhealth_id}/',
            'created_at': prescription.created_at.strftime('%b %d, %Y %H:%M')
        })

    # Calculate pagination info
    total_pages = (total_count + per_page - 1) // per_page
    has_next = page < total_pages
    has_previous = page > 1

    # Calculate statistics for the filtered results
    filtered_stats = {
        'total_count': total_count,
        'active_count': prescriptions.filter(status='active').count(),
        'completed_count': prescriptions.filter(status='completed').count(),
        'discontinued_count': prescriptions.filter(status='discontinued').count(),
        'expiring_soon_count': prescriptions.filter(
            status='active',
            end_date__lte=timezone.now().date() + timedelta(days=7)
        ).count() if prescriptions.filter(status='active').exists() else 0
    }

    return JsonResponse({
        'success': True,
        'prescriptions': prescriptions_data,
        'pagination': {
            'current_page': page,
            'total_pages': total_pages,
            'total_count': total_count,
            'per_page': per_page,
            'has_next': has_next,
            'has_previous': has_previous,
            'start_index': start + 1 if prescriptions_data else 0,
            'end_index': start + len(prescriptions_data)
        },
        'statistics': filtered_stats,
        'filters_applied': {
            'search': search_query,
            'status': status,
            'frequency': frequency,
            'medication_type': medication_type,
            'date_from': date_from,
            'date_to': date_to
        }
    })


# ============================================================================
# EXPORT FUNCTIONALITY
# ============================================================================

@login_required
def export_patients_csv(request):
    """Export patients data to CSV format"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="patients_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

    writer = csv.writer(response)

    # Write header
    writer.writerow([
        'ZimHealth ID', 'First Name', 'Last Name', 'National ID', 'Date of Birth',
        'Gender', 'Blood Type', 'Phone Number', 'Email', 'Address',
        'Emergency Contact', 'Emergency Phone', 'Allergies', 'Registration Date'
    ])

    # Write patient data
    patients = Patient.objects.filter(is_active=True).order_by('created_at')
    for patient in patients:
        writer.writerow([
            patient.zimhealth_id,
            patient.first_name,
            patient.last_name,
            patient.national_id or '',
            patient.date_of_birth.strftime('%Y-%m-%d') if patient.date_of_birth else '',
            patient.get_gender_display(),
            patient.blood_type or '',
            patient.phone_number,
            patient.email or '',
            patient.address or '',
            patient.emergency_contact_name or '',
            patient.emergency_contact_phone or '',
            ', '.join(patient.allergies) if patient.allergies else '',
            patient.registration_date.strftime('%Y-%m-%d %H:%M:%S')
        ])

    return response


@login_required
def export_appointments_excel(request):
    """Export appointments data to Excel format"""
    if not EXCEL_AVAILABLE:
        messages.error(request, 'Excel export is not available. Please install openpyxl.')
        return redirect('api:dashboard')

    # Create workbook and worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Appointments Export"

    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")

    # Write headers
    headers = [
        'Appointment ID', 'Patient Name', 'ZimHealth ID', 'Doctor Name', 'Date', 'Time',
        'Appointment Type', 'Priority', 'Status', 'Facility', 'Department', 'Reason',
        'Duration (min)', 'Created Date'
    ]

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

    # Write appointment data
    appointments = Appointment.objects.select_related('patient').order_by('-date', '-time')
    for row, appointment in enumerate(appointments, 2):
        ws.cell(row=row, column=1, value=str(appointment.id))
        ws.cell(row=row, column=2, value=appointment.patient.full_name)
        ws.cell(row=row, column=3, value=appointment.patient.zimhealth_id)
        ws.cell(row=row, column=4, value=appointment.doctor_name)
        ws.cell(row=row, column=5, value=appointment.date.strftime('%Y-%m-%d'))
        ws.cell(row=row, column=6, value=appointment.time.strftime('%H:%M'))
        ws.cell(row=row, column=7, value=appointment.get_appointment_type_display())
        ws.cell(row=row, column=8, value=appointment.get_priority_display())
        ws.cell(row=row, column=9, value=appointment.get_status_display())
        ws.cell(row=row, column=10, value=appointment.facility_name)
        ws.cell(row=row, column=11, value=appointment.department or '')
        ws.cell(row=row, column=12, value=appointment.reason)
        ws.cell(row=row, column=13, value=appointment.estimated_duration)
        ws.cell(row=row, column=14, value=appointment.created_at.strftime('%Y-%m-%d %H:%M:%S'))

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="appointments_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

    wb.save(response)
    return response


@login_required
def export_medical_records_pdf(request):
    """Export medical records data to PDF format"""
    if not PDF_AVAILABLE:
        messages.error(request, 'PDF export is not available. Please install reportlab.')
        return redirect('api:dashboard')

    # Create response
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="medical_records_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.pdf"'

    # Create PDF document
    doc = SimpleDocTemplate(response, pagesize=A4)
    elements = []
    styles = getSampleStyleSheet()

    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    elements.append(Paragraph("ZimHealth-ID Medical Records Export", title_style))
    elements.append(Spacer(1, 20))

    # Export info
    info_style = styles['Normal']
    elements.append(Paragraph(f"Export Date: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}", info_style))
    elements.append(Paragraph(f"Exported by: {request.user.get_full_name() or request.user.username}", info_style))
    elements.append(Spacer(1, 20))

    # Get medical records data
    medical_records = MedicalRecord.objects.select_related('patient').order_by('-date')[:100]  # Limit to 100 records

    if medical_records:
        # Create table data
        table_data = [
            ['Patient Name', 'ZimHealth ID', 'Date', 'Facility', 'Doctor', 'Diagnosis', 'Treatment']
        ]

        for record in medical_records:
            table_data.append([
                record.patient.full_name,
                record.patient.zimhealth_id,
                record.date.strftime('%Y-%m-%d'),
                record.facility_name,
                record.doctor_name,
                record.diagnosis[:50] + '...' if len(record.diagnosis) > 50 else record.diagnosis,
                record.treatment[:50] + '...' if len(record.treatment) > 50 else record.treatment
            ])

        # Create table
        table = Table(table_data, colWidths=[1.2*inch, 1*inch, 0.8*inch, 1*inch, 1*inch, 1.5*inch, 1.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        elements.append(table)
    else:
        elements.append(Paragraph("No medical records found.", styles['Normal']))

    # Build PDF
    doc.build(elements)
    return response


@login_required
def export_analytics_report(request):
    """Export analytics report to PDF format"""
    if not PDF_AVAILABLE:
        messages.error(request, 'PDF export is not available. Please install reportlab.')
        return redirect('api:dashboard')

    # Create response
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="analytics_report_{timezone.now().strftime("%Y%m%d_%H%M%S")}.pdf"'

    # Create PDF document
    doc = SimpleDocTemplate(response, pagesize=A4)
    elements = []
    styles = getSampleStyleSheet()

    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=20,
        spaceAfter=30,
        alignment=1
    )
    elements.append(Paragraph("ZimHealth-ID Analytics Report", title_style))
    elements.append(Spacer(1, 20))

    # Report info
    info_style = styles['Normal']
    elements.append(Paragraph(f"Report Date: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}", info_style))
    elements.append(Paragraph(f"Generated by: {request.user.get_full_name() or request.user.username}", info_style))
    elements.append(Spacer(1, 30))

    # Get analytics data
    total_patients = safe_query(lambda: Patient.objects.filter(is_active=True).count(), 0)
    total_appointments = safe_query(lambda: Appointment.objects.count(), 0)
    total_records = safe_query(lambda: MedicalRecord.objects.count(), 0)
    total_prescriptions = safe_query(lambda: Prescription.objects.count(), 0)

    # Summary statistics
    elements.append(Paragraph("Summary Statistics", styles['Heading2']))
    elements.append(Spacer(1, 10))

    summary_data = [
        ['Metric', 'Count'],
        ['Total Patients', str(total_patients)],
        ['Total Appointments', str(total_appointments)],
        ['Total Medical Records', str(total_records)],
        ['Total Prescriptions', str(total_prescriptions)]
    ]

    summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    elements.append(summary_table)
    elements.append(Spacer(1, 30))

    # Recent activity
    elements.append(Paragraph("Recent Activity (Last 30 Days)", styles['Heading2']))
    elements.append(Spacer(1, 10))

    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_patients = safe_query(lambda: Patient.objects.filter(created_at__gte=thirty_days_ago).count(), 0)
    recent_appointments = safe_query(lambda: Appointment.objects.filter(created_at__gte=thirty_days_ago).count(), 0)
    recent_records = safe_query(lambda: MedicalRecord.objects.filter(created_at__gte=thirty_days_ago).count(), 0)

    activity_data = [
        ['Activity', 'Count (Last 30 Days)'],
        ['New Patients Registered', str(recent_patients)],
        ['Appointments Scheduled', str(recent_appointments)],
        ['Medical Records Created', str(recent_records)]
    ]

    activity_table = Table(activity_data, colWidths=[3*inch, 2*inch])
    activity_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    elements.append(activity_table)

    # Build PDF
    doc.build(elements)
    return response
