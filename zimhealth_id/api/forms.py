from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import date, datetime, timedelta
from .models import Patient, MedicalRecord, Prescription, Appointment


class PatientForm(forms.ModelForm):
    """Form for creating and updating patient records"""

    # Custom field for handling allergies as free text
    allergies = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.Textarea(attrs={
            'rows': 4,
            'class': 'government-search-input-compact w-full resize-none text-sm',
            'placeholder': 'Enter known allergies separated by commas (e.g., Penicillin, Peanuts, Shellfish)',
            'style': 'height: 100%; min-height: 120px;'
        }),
        help_text="Enter allergies separated by commas"
    )

    class Meta:
        model = Patient
        fields = [
            'first_name', 'last_name', 'national_id', 'date_of_birth', 'gender',
            'nationality', 'marital_status', 'phone_number', 'email', 'address',
            'emergency_contact', 'blood_type'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors',
                'placeholder': 'Enter first name'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors',
                'placeholder': 'Enter last name'
            }),
            'national_id': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors',
                'placeholder': 'e.g., 63-123456A12'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors'
            }),
            'gender': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors'
            }),
            'nationality': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors',
                'placeholder': 'Enter nationality'
            }),
            'marital_status': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors',
                'placeholder': '+263 77 123 4567'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors',
                'placeholder': 'Enter email address'
            }),
            'address': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors resize-none',
                'placeholder': 'Enter full address'
            }),
            'emergency_contact': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors',
                'placeholder': '+263 77 123 4567'
            }),
            'blood_type': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # If editing existing patient, populate allergies field
        if self.instance and self.instance.pk and self.instance.allergies:
            patient_allergies = self.instance.allergies if isinstance(self.instance.allergies, list) else []
            if patient_allergies:
                self.fields['allergies'].initial = ', '.join(patient_allergies)

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Parse allergies from comma-separated text
        allergies_text = self.cleaned_data.get('allergies', '')
        if allergies_text:
            # Split by comma, clean each item, and remove duplicates while preserving order
            allergies_list = [allergy.strip() for allergy in allergies_text.split(',') if allergy.strip()]
            # Remove duplicates while preserving order
            seen = set()
            unique_allergies = []
            for allergy in allergies_list:
                if allergy not in seen:
                    seen.add(allergy)
                    unique_allergies.append(allergy)
            instance.allergies = unique_allergies
        else:
            instance.allergies = []

        if commit:
            instance.save()
        return instance

    def clean_date_of_birth(self):
        dob = self.cleaned_data.get('date_of_birth')
        if dob:
            if dob > date.today():
                raise ValidationError("Date of birth cannot be in the future.")
            
            # Check if age is reasonable (not more than 150 years)
            age = date.today().year - dob.year
            if age > 150:
                raise ValidationError("Please enter a valid date of birth.")
        return dob

    def clean_national_id(self):
        national_id = self.cleaned_data.get('national_id')
        if national_id:
            # Check if national ID already exists (excluding current instance)
            existing = Patient.objects.filter(national_id=national_id)
            if self.instance and self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError("A patient with this National ID already exists.")
        return national_id


class MedicalRecordForm(forms.ModelForm):
    """Form for creating and updating medical records"""
    
    class Meta:
        model = MedicalRecord
        fields = [
            'patient', 'date', 'facility_name', 'doctor_name', 'diagnosis', 'treatment', 'notes',
            'temperature', 'blood_pressure_systolic', 'blood_pressure_diastolic', 
            'heart_rate', 'weight', 'height'
        ]
        widgets = {
            'patient': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors'
            }),
            'date': forms.DateTimeInput(attrs={
                'type': 'datetime-local',
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors'
            }),
            'facility_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors',
                'placeholder': 'e.g., Harare Central Hospital'
            }),
            'doctor_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors',
                'placeholder': 'Dr. John Smith'
            }),
            'diagnosis': forms.Textarea(attrs={
                'rows': 4,
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none',
                'placeholder': 'Primary diagnosis and any secondary diagnoses'
            }),
            'treatment': forms.Textarea(attrs={
                'rows': 4,
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none',
                'placeholder': 'Treatment provided or recommended'
            }),
            'notes': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none',
                'placeholder': 'Any additional observations or notes'
            }),
            'temperature': forms.NumberInput(attrs={
                'step': '0.1',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors',
                'placeholder': '36.5'
            }),
            'blood_pressure_systolic': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors',
                'placeholder': '120'
            }),
            'blood_pressure_diastolic': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors',
                'placeholder': '80'
            }),
            'heart_rate': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors',
                'placeholder': '72'
            }),
            'weight': forms.NumberInput(attrs={
                'step': '0.1',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors',
                'placeholder': '70.5'
            }),
            'height': forms.NumberInput(attrs={
                'step': '0.1',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors',
                'placeholder': '175'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default date to now
        if not self.instance.pk:
            self.fields['date'].initial = timezone.now()

    def clean_date(self):
        date_value = self.cleaned_data.get('date')
        if date_value and date_value > timezone.now():
            raise ValidationError("Medical record date cannot be in the future.")
        return date_value

    def clean_temperature(self):
        temp = self.cleaned_data.get('temperature')
        if temp and (temp < 30 or temp > 45):
            raise ValidationError("Please enter a valid temperature (30-45°C).")
        return temp

    def clean_blood_pressure_systolic(self):
        systolic = self.cleaned_data.get('blood_pressure_systolic')
        if systolic and (systolic < 50 or systolic > 300):
            raise ValidationError("Please enter a valid systolic blood pressure (50-300 mmHg).")
        return systolic

    def clean_blood_pressure_diastolic(self):
        diastolic = self.cleaned_data.get('blood_pressure_diastolic')
        if diastolic and (diastolic < 30 or diastolic > 200):
            raise ValidationError("Please enter a valid diastolic blood pressure (30-200 mmHg).")
        return diastolic

    def clean_heart_rate(self):
        hr = self.cleaned_data.get('heart_rate')
        if hr and (hr < 30 or hr > 250):
            raise ValidationError("Please enter a valid heart rate (30-250 bpm).")
        return hr

    def clean_weight(self):
        weight = self.cleaned_data.get('weight')
        if weight and (weight < 0.5 or weight > 500):
            raise ValidationError("Please enter a valid weight (0.5-500 kg).")
        return weight

    def clean_height(self):
        height = self.cleaned_data.get('height')
        if height and (height < 30 or height > 300):
            raise ValidationError("Please enter a valid height (30-300 cm).")
        return height


class PrescriptionForm(forms.ModelForm):
    """Form for creating and updating prescriptions"""
    
    class Meta:
        model = Prescription
        fields = [
            'medical_record', 'medication', 'dosage', 'frequency', 'duration',
            'instructions', 'quantity_prescribed', 'refills_allowed', 'status',
            'start_date', 'end_date'
        ]
        widgets = {
            'medical_record': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors'
            }),
            'medication': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors',
                'placeholder': 'e.g., Paracetamol'
            }),
            'dosage': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors',
                'placeholder': 'e.g., 500mg, 2 tablets'
            }),
            'frequency': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors'
            }),
            'duration': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors',
                'placeholder': 'e.g., 7 days, 2 weeks'
            }),
            'instructions': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors resize-none',
                'placeholder': 'Special instructions for taking the medication'
            }),
            'quantity_prescribed': forms.NumberInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors',
                'placeholder': 'Total quantity'
            }),
            'refills_allowed': forms.NumberInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors',
                'min': '0'
            }),
            'status': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors'
            }),
            'start_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors'
            }),
            'end_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default start date to today
        if not self.instance.pk:
            self.fields['start_date'].initial = timezone.now().date()

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if end_date <= start_date:
                raise ValidationError("End date must be after start date.")

        return cleaned_data


class AppointmentForm(forms.ModelForm):
    """Form for creating and updating appointments"""
    
    class Meta:
        model = Appointment
        fields = [
            'patient', 'doctor_name', 'date', 'time', 'appointment_type', 'priority',
            'facility_name', 'department', 'reason', 'notes', 'estimated_duration'
        ]
        widgets = {
            'patient': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors'
            }),
            'doctor_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors',
                'placeholder': 'Dr. John Smith'
            }),
            'date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors'
            }),
            'time': forms.TimeInput(attrs={
                'type': 'time',
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors'
            }),
            'appointment_type': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors'
            }),
            'priority': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors'
            }),
            'facility_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors',
                'placeholder': 'e.g., Harare Central Hospital'
            }),
            'department': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors',
                'placeholder': 'e.g., Cardiology'
            }),
            'reason': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors resize-none',
                'placeholder': 'Reason for the appointment'
            }),
            'notes': forms.Textarea(attrs={
                'rows': 3,
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors resize-none',
                'placeholder': 'Additional notes'
            }),
            'estimated_duration': forms.NumberInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors',
                'min': '15',
                'step': '15'
            }),
        }

    def clean_date(self):
        appointment_date = self.cleaned_data.get('date')
        if appointment_date and appointment_date < date.today():
            raise ValidationError("Appointment date cannot be in the past.")
        return appointment_date

    def clean(self):
        cleaned_data = super().clean()
        patient = cleaned_data.get('patient')
        doctor_name = cleaned_data.get('doctor_name')
        appointment_date = cleaned_data.get('date')
        appointment_time = cleaned_data.get('time')

        if all([patient, doctor_name, appointment_date, appointment_time]):
            # Check for conflicting appointments
            existing_appointments = Appointment.objects.filter(
                patient=patient,
                date=appointment_date,
                time=appointment_time,
                doctor_name=doctor_name,
                status__in=['scheduled', 'rescheduled']
            )
            
            # Exclude current instance if editing
            if self.instance and self.instance.pk:
                existing_appointments = existing_appointments.exclude(pk=self.instance.pk)
            
            if existing_appointments.exists():
                raise ValidationError(
                    "An appointment already exists for this patient with this doctor at the same date and time."
                )

        return cleaned_data
