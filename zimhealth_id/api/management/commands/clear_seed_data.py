from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from api.models import Patient, MedicalRecord, Prescription, Appointment
from zhid_auth.models import UserProfile

class Command(BaseCommand):
    help = 'Clear all seed data from ZimHealth-ID application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm deletion of all data',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This will delete ALL data. Use --confirm to proceed.'
                )
            )
            return

        self.stdout.write('Clearing seed data...')
        
        # Delete in reverse order of dependencies
        prescription_count = Prescription.objects.count()
        Prescription.objects.all().delete()
        
        appointment_count = Appointment.objects.count()
        Appointment.objects.all().delete()
        
        record_count = MedicalRecord.objects.count()
        MedicalRecord.objects.all().delete()
        
        patient_count = Patient.objects.count()
        Patient.objects.all().delete()
        
        # Delete healthcare provider users (but keep superusers)
        provider_users = User.objects.filter(
            username__startswith='dr.',
            is_superuser=False
        )
        provider_count = provider_users.count()
        provider_users.delete()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully cleared seed data:\n'
                f'- {provider_count} healthcare providers\n'
                f'- {patient_count} patients\n'
                f'- {record_count} medical records\n'
                f'- {prescription_count} prescriptions\n'
                f'- {appointment_count} appointments'
            )
        )