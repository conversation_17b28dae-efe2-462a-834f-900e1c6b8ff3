from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, timedelta
from api.models import Patient, MedicalRecord, Prescription, Appointment
import random


class Command(BaseCommand):
    help = 'Create sample data for ZimHealth-ID system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--patients',
            type=int,
            default=5,
            help='Number of patients to create'
        )

    def handle(self, *args, **options):
        num_patients = options['patients']
        
        self.stdout.write(
            self.style.SUCCESS(f'Creating {num_patients} sample patients...')
        )

        # Sample data
        first_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        last_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        facilities = ['Harare Central Hospital', 'Parirenyatwa Hospital', 'Chitungwiza Hospital', '<PERSON>pilo Hospital', 'United Bulawayo Hospitals']
        doctors = ['Dr<PERSON>', 'Dr<PERSON>', 'Dr<PERSON>', 'Dr. <PERSON>', 'Dr. <PERSON>cube', 'Dr. <PERSON>pofu', 'Dr. Dube']
        medications = ['Paracetamol', 'Amoxicillin', 'Ibuprofen', 'Aspirin', 'Metformin', 'Lisinopril', 'Omeprazole']
        
        # Create patients
        for i in range(num_patients):
            # Create patient
            patient = Patient.objects.create(
                first_name=random.choice(first_names),
                last_name=random.choice(last_names),
                national_id=f"63-{random.randint(100000, 999999)}A{random.randint(10, 99)}",
                date_of_birth=date(
                    random.randint(1950, 2005),
                    random.randint(1, 12),
                    random.randint(1, 28)
                ),
                gender=random.choice(['M', 'F']),
                phone_number=f"+263{random.randint(700000000, 799999999)}",
                address=f"{random.randint(1, 999)} {random.choice(['Main', 'High', 'Church', 'Park'])} Street, {random.choice(['Harare', 'Bulawayo', 'Mutare', 'Gweru'])}",
                emergency_contact=f"+263{random.randint(700000000, 799999999)}",
                blood_type=random.choice(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']),
                allergies=random.sample(['Penicillin', 'Peanuts', 'Shellfish', 'Latex', 'Dust'], random.randint(0, 2))
            )
            
            self.stdout.write(f'Created patient: {patient.full_name} ({patient.zimhealth_id})')
            
            # Create 1-3 medical records for each patient
            for j in range(random.randint(1, 3)):
                record_date = timezone.now() - timedelta(days=random.randint(1, 365))
                
                medical_record = MedicalRecord.objects.create(
                    patient=patient,
                    date=record_date,
                    facility_name=random.choice(facilities),
                    doctor_name=random.choice(doctors),
                    diagnosis=random.choice([
                        'Hypertension',
                        'Diabetes Type 2',
                        'Upper Respiratory Infection',
                        'Malaria',
                        'Gastritis',
                        'Headache',
                        'Back Pain'
                    ]),
                    treatment=random.choice([
                        'Medication prescribed',
                        'Rest and fluids',
                        'Physical therapy recommended',
                        'Follow-up in 2 weeks',
                        'Lifestyle changes advised'
                    ]),
                    notes=f'Patient responded well to treatment. {random.choice(["No complications noted.", "Mild side effects observed.", "Good compliance with medication."])}',
                    temperature=round(random.uniform(36.0, 39.0), 1),
                    blood_pressure_systolic=random.randint(110, 180),
                    blood_pressure_diastolic=random.randint(70, 110),
                    heart_rate=random.randint(60, 100),
                    weight=round(random.uniform(50.0, 120.0), 2),
                    height=round(random.uniform(150.0, 190.0), 2)
                )
                
                # Create 1-2 prescriptions for each medical record
                for k in range(random.randint(1, 2)):
                    Prescription.objects.create(
                        medical_record=medical_record,
                        medication=random.choice(medications),
                        dosage=random.choice(['500mg', '250mg', '100mg', '1 tablet', '2 tablets']),
                        frequency=random.choice(['twice_daily', 'three_times_daily', 'once_daily', 'as_needed']),
                        duration=random.choice(['7 days', '14 days', '30 days', '3 months']),
                        instructions=random.choice([
                            'Take with food',
                            'Take on empty stomach',
                            'Take before bedtime',
                            'Complete the full course'
                        ]),
                        quantity_prescribed=random.randint(10, 90),
                        refills_allowed=random.randint(0, 3),
                        start_date=record_date.date()
                    )
            
            # Create 1-2 future appointments for each patient
            for j in range(random.randint(1, 2)):
                appointment_date = date.today() + timedelta(days=random.randint(1, 90))
                appointment_time = timezone.now().replace(
                    hour=random.randint(8, 17),
                    minute=random.choice([0, 15, 30, 45]),
                    second=0,
                    microsecond=0
                ).time()
                
                Appointment.objects.create(
                    patient=patient,
                    doctor_name=random.choice(doctors),
                    date=appointment_date,
                    time=appointment_time,
                    appointment_type=random.choice(['consultation', 'follow_up', 'check_up', 'screening']),
                    facility_name=random.choice(facilities),
                    department=random.choice(['General Medicine', 'Cardiology', 'Pediatrics', 'Orthopedics']),
                    reason=random.choice([
                        'Follow-up consultation',
                        'Annual check-up',
                        'Blood pressure monitoring',
                        'Medication review',
                        'Routine screening'
                    ]),
                    notes=f'Scheduled {random.choice(["routine", "follow-up", "urgent"])} appointment',
                    estimated_duration=random.choice([30, 45, 60])
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {num_patients} patients with medical records, prescriptions, and appointments!')
        )
