/* Professional Patients Page Styles for ZimHealth-ID */

/* Government-grade patients layout */
.patients-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.patients-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Enhanced patients header styling */
.patients-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

/* Government-Level Professional Search Controls - Angular Design */
.government-search-input {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 2px;           /* More angular - reduced from 4px */
    font-size: 0.875rem;
    color: #374151;
    padding: 16px 20px;
    line-height: 1.6;
    transition: border-color 0.2s ease;
}

.government-search-input:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

/* Compact version for forms - angular corners and reduced size */
.government-search-input-compact {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 2px;           /* More angular - reduced from 4px */
    font-size: 0.875rem;
    color: #374151;
    padding: 6px 10px;            /* Even more compact - reduced from 8px 12px */
    line-height: 1.3;             /* Tighter line height - reduced from 1.4 */
    transition: border-color 0.2s ease;
    height: auto;
    min-height: 32px;             /* Smaller height - reduced from 36px */
}

.government-search-input-compact:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

/* Enhanced padding for textarea elements - Angular Design */
textarea.government-search-input {
    padding: 18px 20px;
    line-height: 1.7;
    min-height: 90px;
    border-radius: 2px;           /* Angular corners */
}

/* Compact textarea version - angular and smaller */
textarea.government-search-input-compact {
    padding: 6px 10px;            /* More compact padding */
    line-height: 1.3;             /* Tighter line height */
    min-height: 50px;             /* Smaller minimum height */
    resize: vertical;
    border-radius: 2px;           /* Angular corners */
}

.government-filter-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.government-filter-button:hover {
    border-color: #9ca3af;
    background: #f9fafb;
}

.government-filter-button:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

.government-qr-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.government-qr-button:hover {
    border-color: #9ca3af;
    background: #f9fafb;
    color: #374151;
}

.government-qr-button:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

/* QR Scanner Button inside Search Bar */
.search-qr-button {
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 0.5rem;
    border-radius: 3px;
}

.search-qr-button:hover {
    color: #374151;
    background: rgba(243, 244, 246, 0.5);
}

.search-qr-button:focus {
    outline: none;
    color: #1f2937;
    background: rgba(243, 244, 246, 0.8);
}

/* Government-Level Professional Patients Table */
.patients-table-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.patients-table-header {
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    padding: 1rem 1.5rem;
}

.patients-table-header h3 {
    color: #111827;
    font-weight: 600;
    font-size: 1rem;
    margin: 0;
}

.patients-table {
    background: #ffffff;
    width: 100%;
    border-collapse: collapse;
}

.patients-table thead {
    background: #f3f4f6;
}

.patients-table th {
    font-weight: 600;
    color: #374151;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    padding: 0.875rem 1.25rem;
    border-bottom: 2px solid #e5e7eb;
    text-align: left;
    border-right: 1px solid #f3f4f6;
}

.patients-table th:last-child {
    border-right: none;
}

.patients-table td {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #e5e7eb;
    border-right: 1px solid #f9fafb;
    color: #374151;
    font-size: 0.875rem;
}

.patients-table td:last-child {
    border-right: none;
}

.patients-table tbody tr {
    background: #ffffff;
}

.patients-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.patients-table tbody tr:hover {
    background: #f3f4f6;
}

/* Government-Level Professional Patient Avatar */
.patient-avatar {
    width: 36px;
    height: 36px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.patient-avatar i {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Government-Level Professional Action Buttons */
.action-button {
    padding: 0.5rem;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #6b7280;
    font-size: 0.75rem;
}

.action-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.action-button.view {
    color: #4b5563;
}

.action-button.view:hover {
    color: #1f2937;
}

.action-button.edit {
    color: #4b5563;
}

.action-button.edit:hover {
    color: #1f2937;
}

.action-button.medical {
    color: #4b5563;
}

.action-button.medical:hover {
    color: #1f2937;
}

.action-button.appointment {
    color: #4b5563;
}

.action-button.appointment:hover {
    color: #1f2937;
}

.action-button.delete {
    color: #dc2626;
}

.action-button.delete:hover {
    color: #b91c1c;
    transform: translateY(-1px);
}

/* Government-Level Professional Blood Type Badge with Color Coding */
.blood-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-weight: 600;
    font-size: 0.75rem;
    display: inline-block;
    font-family: monospace;
    border: 1px solid;
}

/* Blood Type Color Coding - Professional Healthcare Colors */
.blood-type-badge.a-positive {
    background: #fef2f2;
    border-color: #fecaca;
    color: #7f1d1d;
}

.blood-type-badge.a-negative {
    background: #fef1f1;
    border-color: #fca5a5;
    color: #991b1b;
}

.blood-type-badge.b-positive {
    background: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}

.blood-type-badge.b-negative {
    background: #dbeafe;
    border-color: #93c5fd;
    color: #1e3a8a;
}

.blood-type-badge.ab-positive {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.blood-type-badge.ab-negative {
    background: #ecfdf5;
    border-color: #86efac;
    color: #14532d;
}

.blood-type-badge.o-positive {
    background: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

.blood-type-badge.o-negative {
    background: #fefce8;
    border-color: #fde68a;
    color: #a16207;
}

/* Enhanced pagination */
.pagination-button {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.pagination-button:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.08), rgba(14, 165, 233, 0.03));
    border-color: rgba(14, 165, 233, 0.3);
    transform: translateY(-1px);
}

/* Enhanced Professional Add Patient Button */
.add-patient-button {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border: 1px solid #1e3a8a;
    border-radius: 6px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.add-patient-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.add-patient-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-patient-button:hover::before {
    left: 100%;
}

.add-patient-button:active {
    transform: translateY(0);
    box-shadow:
        0 1px 2px rgba(0, 0, 0, 0.1),
        0 1px 1px rgba(0, 0, 0, 0.06);
}

/* QR Scanner Modal */
.qr-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.qr-modal.active {
    opacity: 1;
    visibility: visible;
}

.qr-modal-content {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.qr-modal.active .qr-modal-content {
    transform: scale(1);
}

#qr-video {
    width: 100%;
    border-radius: 12px;
    background: #000;
}

/* Government-Level Responsive Design */
@media (max-width: 768px) {
    .patients-table-card {
        border-radius: 4px;
        margin: 0 0.5rem;
    }

    .patients-table th,
    .patients-table td {
        padding: 0.75rem 0.875rem;
        font-size: 0.8rem;
    }

    .patient-avatar {
        width: 32px;
        height: 32px;
    }

    .action-button {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    .government-filter-button {
        min-width: 100px;
        font-size: 0.75rem;
    }

    .government-search-input {
        font-size: 0.8rem;
        padding: 14px 18px;
    }

    .government-search-input-compact {
        font-size: 0.8rem;
        padding: 5px 8px;             /* Even more compact on mobile */
        min-height: 28px;             /* Smaller on mobile */
        border-radius: 2px;           /* Maintain angular corners */
    }

    textarea.government-search-input {
        padding: 16px 18px;
    }

    textarea.government-search-input-compact {
        padding: 5px 8px;             /* More compact on mobile */
        min-height: 40px;             /* Smaller textarea on mobile */
        border-radius: 2px;           /* Angular corners */
    }
}

@media (max-width: 640px) {
    .flex.items-center.justify-between {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .flex.items-center.space-x-3 {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .government-filter-button {
        min-width: 80px;
    }
}

/* Government-Level Professional Patient Form Card Styling */
.patients-card {
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.patients-card-header {
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    padding: 1.25rem 1.5rem;
    border-radius: 3px 3px 0 0;
}

.patients-card-title {
    font-size: 1rem;
    font-weight: 700;
    color: #111827;
    display: flex;
    align-items: center;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.patients-card-title i {
    margin-right: 0.75rem;
    color: #4b5563;
    font-size: 1rem;
}

.patients-card-subtitle {
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: 500;
    margin: 0.25rem 0 0 0;
    line-height: 1.4;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Beautiful Professional Patient Registration Form Styles */
.form-group {
    margin-bottom: 1.25rem;
    position: relative;
    transition: all 0.3s ease;
}

.form-group:hover {
    transform: translateY(-1px);
}

.form-group:focus-within {
    transform: translateY(-2px);
}

/* Beautiful Professional Form Labels */
.form-label {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.625rem;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    transition: all 0.2s ease;
}

.form-label i {
    color: #6366f1;
    font-size: 0.875rem;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 1px 2px rgba(99, 102, 241, 0.2));
}

.form-label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
    transition: width 0.3s ease;
}

.form-group:focus-within .form-label::after {
    width: 100%;
}

.form-label-sm {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    gap: 0.375rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-label-sm i {
    color: #6366f1;
    font-size: 0.75rem;
    filter: drop-shadow(0 1px 1px rgba(99, 102, 241, 0.2));
}

/* Beautiful Professional Form Inputs */
.form-input {
    width: 100%;
    padding: 0.875rem 1.125rem;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
    box-shadow:
        inset 0 1px 3px rgba(0, 0, 0, 0.06),
        0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
}

.form-input:hover {
    border-color: #d1d5db;
    box-shadow:
        inset 0 1px 3px rgba(0, 0, 0, 0.06),
        0 2px 4px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.form-input:focus {
    outline: none;
    border-color: #6366f1;
    background: #ffffff;
    box-shadow:
        inset 0 1px 3px rgba(0, 0, 0, 0.06),
        0 0 0 3px rgba(99, 102, 241, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.form-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
    font-style: italic;
}

.form-input-sm {
    width: 100%;
    padding: 0.625rem 0.875rem;
    border: 2px solid #e5e7eb;
    border-radius: 5px;
    font-size: 0.75rem;
    font-weight: 500;
    color: #111827;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
    box-shadow:
        inset 0 1px 2px rgba(0, 0, 0, 0.05),
        0 1px 2px rgba(0, 0, 0, 0.04);
}

.form-input-sm:hover {
    border-color: #d1d5db;
    box-shadow:
        inset 0 1px 2px rgba(0, 0, 0, 0.05),
        0 2px 3px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.form-input-sm:focus {
    outline: none;
    border-color: #6366f1;
    background: #ffffff;
    box-shadow:
        inset 0 1px 2px rgba(0, 0, 0, 0.05),
        0 0 0 2px rgba(99, 102, 241, 0.1),
        0 3px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.form-input-sm::placeholder {
    color: #9ca3af;
    font-weight: 400;
    font-style: italic;
}

/* Beautiful Error Styling */
.form-error {
    margin-top: 0.5rem;
    color: #dc2626;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 1px solid #fecaca;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    position: relative;
    animation: slideInError 0.3s ease-out;
}

.form-error::before {
    content: '⚠';
    margin-right: 0.5rem;
    font-weight: bold;
}

@keyframes slideInError {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Government-Level Medical Information Sidebar */
.bg-medical-50 {
    background-color: #f9fafb;
}

.border-medical-200 {
    border-color: #d1d5db;
}

.text-medical-700 {
    color: #374151;
}

.text-medical-600 {
    color: #4b5563;
}

/* Professional Form Layout Optimizations */
.patients-card .p-6 {
    padding: 1.5rem;
}

.patients-card-header {
    padding: 1.25rem 1.5rem;
}

/* Beautiful Professional Checkbox Styling */
.form-group input[type="checkbox"] {
    width: 1.125rem;
    height: 1.125rem;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background: linear-gradient(145deg, #ffffff 0%, #f9fafb 100%);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    position: relative;
}

.form-group input[type="checkbox"]:hover {
    border-color: #6366f1;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.form-group input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-color: #6366f1;
    box-shadow:
        0 2px 4px rgba(99, 102, 241, 0.2),
        inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.form-group input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

.form-group input[type="checkbox"]:focus {
    outline: none;
    box-shadow:
        0 0 0 3px rgba(99, 102, 241, 0.1),
        0 2px 4px rgba(99, 102, 241, 0.2);
}

/* Beautiful Professional Allergy Selection */
.form-group label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    transition: all 0.2s ease;
    cursor: pointer;
}

/* Beautiful Allergy Checkbox Labels */
.form-group label.flex.items-center {
    background: linear-gradient(145deg, #ffffff 0%, #f9fafb 100%);
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    padding: 0.625rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.form-group label.flex.items-center::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.5s ease;
}

.form-group label.flex.items-center:hover {
    border-color: #6366f1;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
        0 2px 4px rgba(99, 102, 241, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.form-group label.flex.items-center:hover::before {
    left: 100%;
}

.form-group label.flex.items-center:has(input:checked) {
    border-color: #6366f1;
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
    box-shadow:
        0 2px 4px rgba(99, 102, 241, 0.15),
        inset 0 1px 2px rgba(99, 102, 241, 0.1);
}

.form-group label.flex.items-center span {
    font-weight: 600;
    color: #374151;
    transition: color 0.2s ease;
}

.form-group label.flex.items-center:has(input:checked) span {
    color: #6366f1;
}

/* Success Notification Styling */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.alert-success {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 2px solid #bbf7d0;
    color: #166534;
}

.success-notification {
    animation: slideInSuccess 0.5s ease-out;
}

@keyframes slideInSuccess {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Beautiful Registration Status Bullets */
.bg-medical-50 ul li {
    transition: all 0.2s ease;
    padding: 0.25rem 0;
}

.bg-medical-50 ul li:hover {
    transform: translateX(2px);
}

.bg-medical-50 ul li span.w-1 {
    transition: all 0.3s ease;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}

.bg-medical-50 ul li:hover span.w-1 {
    transform: scale(1.2);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
}

/* Color-coded bullet points */
.bg-medical-50 ul li:nth-child(1) span.w-1 {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.bg-medical-50 ul li:nth-child(2) span.w-1 {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.bg-medical-50 ul li:nth-child(3) span.w-1 {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Enhanced responsive design for registration form */
@media (max-width: 1024px) {
    .lg\:col-span-3 .lg\:col-span-2 {
        grid-column: span 1;
    }

    .lg\:col-span-1 {
        grid-column: span 1;
        margin-top: 1rem;
    }
}

@media (max-width: 768px) {
    .form-input,
    .form-input-sm {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .patients-card .p-6 {
        padding: 1rem;
    }

    .patients-card-header {
        padding: 1rem;
    }

    .form-group {
        margin-bottom: 0.75rem;
    }
}

/* Delete Confirmation Strip - Themed with Patients Page */
.delete-confirmation-strip {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
    padding: 1rem 0;
    transform: translateY(-100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.delete-confirmation-strip.show {
    transform: translateY(0);
}

.delete-strip-container {
    max-width: 7xl;
    margin: 0 auto;
    padding: 0 1rem;
}

.delete-strip-content {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 2px solid rgba(229, 231, 235, 0.3);
    border-radius: 3px;
    box-shadow:
        0 20px 40px -12px rgba(220, 38, 38, 0.15),
        0 8px 25px -8px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    gap: 1rem;
}

.delete-strip-icon {
    background: #fef2f2;
    border: 2px solid #fecaca;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.delete-strip-icon i {
    color: #dc2626;
    font-size: 1.25rem;
}

.delete-strip-text {
    flex: 1;
}

.delete-strip-title {
    font-size: 1rem;
    font-weight: 700;
    color: #111827;
    margin: 0 0 0.25rem 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.delete-strip-message {
    font-size: 0.875rem;
    color: #374151;
    margin: 0;
    line-height: 1.4;
}

.delete-strip-actions {
    display: flex;
    gap: 0.75rem;
    flex-shrink: 0;
}

.delete-strip-btn {
    padding: 0.5rem 1rem;
    border: 2px solid;
    border-radius: 3px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.delete-strip-btn.delete-btn {
    background: #dc2626;
    border-color: #dc2626;
    color: white;
}

.delete-strip-btn.delete-btn:hover {
    background: #b91c1c;
    border-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.delete-strip-btn.cancel-btn {
    background: #6b7280;
    border-color: #6b7280;
    color: white;
}

.delete-strip-btn.cancel-btn:hover {
    background: #4b5563;
    border-color: #4b5563;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
}

/* Professional Modal Styling */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-container {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.2s ease-out;
}

.modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: between;
}

.modal-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #111827;
    display: flex;
    align-items: center;
    flex: 1;
}

.modal-close {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    border-top: 2px solid #e5e7eb;
    display: flex;
    gap: 0.75rem;
}

.btn-danger {
    background: #dc2626;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
}

.btn-danger:hover {
    background: #b91c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.btn-secondary {
    background: #6b7280;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
}

.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
}

/* Success Modal Styling */
.success-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.success-modal-container {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 2px solid #bbf7d0;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 100%;
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s ease-out;
}

.success-modal-header {
    padding: 2rem 2rem 1rem 2rem;
    text-align: center;
}

.success-modal-body {
    padding: 0 2rem 2rem 2rem;
}

.success-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.success-modal-close:hover {
    background: rgba(255, 255, 255, 0.5);
    color: #374151;
}

/* Simple Notification System */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
}

.success-notification {
    background: #10b981;
    color: white;
    border: 2px solid #059669;
}

.error-notification {
    background: #dc2626;
    color: white;
    border: 2px solid #b91c1c;
}

/* AJAX Pagination Styles */
.pagination-container {
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
}

.pagination-btn {
    background: #ffffff;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
}

.pagination-btn:hover:not(.disabled) {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.pagination-btn.disabled {
    background: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Blood Type Badge Enhancements */
.blood-type-badge.unknown {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.blood-type-badge.a-positive {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.blood-type-badge.a-negative {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #ef4444;
}

.blood-type-badge.b-positive {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #3b82f6;
}

.blood-type-badge.b-negative {
    background: #e0e7ff;
    color: #3730a3;
    border: 1px solid #6366f1;
}

.blood-type-badge.ab-positive {
    background: #f3e8ff;
    color: #7c2d12;
    border: 1px solid #a855f7;
}

.blood-type-badge.ab-negative {
    background: #ecfdf5;
    color: #065f46;
    border: 1px solid #10b981;
}

.blood-type-badge.o-positive {
    background: #fef2f2;
    color: #7f1d1d;
    border: 1px solid #dc2626;
}

.blood-type-badge.o-negative {
    background: #f0f9ff;
    color: #0c4a6e;
    border: 1px solid #0ea5e9;
}

/* Notification Styles for AJAX Operations */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success-notification {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.notification.error-notification {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #991b1b;
}

.notification-content {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 500;
}

/* QR Scan Result Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

@keyframes qrScanGlow {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
    }
    50% {
        box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
    }
}

.qr-scan-indicator {
    animation: pulse 2s infinite;
}

/* Enhanced QR scan highlighting */
.patients-table tbody tr.qr-scanned {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.08)) !important;
    border: 2px solid rgba(34, 197, 94, 0.5) !important;
    animation: qrScanGlow 2s ease-in-out infinite;
}

/* QR Scanner Status Styles */
.qr-scanner-status {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    margin-top: 8px;
}

.qr-scanner-status.info {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.qr-scanner-status.success {
    background: rgba(34, 197, 94, 0.1);
    color: #15803d;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.qr-scanner-status.warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.qr-scanner-status.error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Enhanced Appointment Form Buttons */
.appointment-cancel-btn {
    min-width: 120px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.025em;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.appointment-cancel-btn:hover {
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.appointment-submit-btn {
    min-width: 180px;
    border: none;
    border-radius: 2px;
    font-size: 0.875rem;
    font-weight: 600;
    letter-spacing: 0.025em;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.appointment-submit-btn:hover {
    transform: translateY(-1px);
}

.appointment-submit-btn:active {
    transform: translateY(0);
}

.appointment-submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Button Loading State */
.appointment-submit-btn.loading {
    pointer-events: none;
}

.appointment-submit-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: button-spin 1s linear infinite;
}

@keyframes button-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .qr-scan-indicator,
    .patients-table tbody tr.qr-scanned {
        animation: none;
    }
}
