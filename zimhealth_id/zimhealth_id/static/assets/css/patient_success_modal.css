/* ZimHealth-ID Patient Success Modal - Professional Theme with ID Card */

/* Modal Overlay */
.patient-success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.patient-success-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Main Modal Container */
.patient-success-modal {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    transform: scale(0.95) translateY(20px);
    transition: transform 0.3s ease;
    position: relative;
}

.patient-success-modal.show {
    transform: scale(1) translateY(0);
}

/* Header Section */
.modal-header {
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    padding: 1rem 1.5rem;
    position: relative;
}

.modal-header-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.success-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
}

.success-checkmark {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    flex-shrink: 0;
}

.success-text {
    flex: 1;
}

.modal-title {
    color: #111827;
    font-weight: 600;
    font-size: 1rem;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modal-subtitle {
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: 500;
    margin: 0.25rem 0 0 0;
    line-height: 1.4;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: #ffffff;
    border: 1px solid #d1d5db;
    color: #6b7280;
    width: 32px;
    height: 32px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

/* Modal Body */
.modal-body {
    padding: 1.5rem;
}

/* ID Card Container */
.id-card-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
}

/* ID Card with QR Code Inside */
.id-card {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
    border-radius: 12px;
    width: 400px;
    height: 250px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
    border: 2px solid #1e3a8a;
    /* Ensure colors display correctly */
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    color-adjust: exact;
}

.id-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: cardGlow 3s ease-in-out infinite;
}

@keyframes cardGlow {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
}

/* Card Header */
.card-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(30, 64, 175, 0.2);
    position: relative;
    z-index: 2;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    color-adjust: exact;
}

.card-logo {
    font-size: 18px;
    font-weight: bold;
    color: #1e40af;
}

.card-type {
    font-size: 12px;
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Card Body */
.card-body {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    height: calc(100% - 50px);
    position: relative;
    z-index: 2;
}

/* Patient Info Section */
.patient-info-section {
    flex: 1;
}

.patient-name {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.patient-id {
    font-size: 16px;
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 10px;
    border-radius: 6px;
    display: inline-block;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    color-adjust: exact;
}

.patient-details {
    font-size: 12px;
    line-height: 1.6;
}

.detail-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    opacity: 0.9;
}

.detail-row i {
    width: 14px;
    text-align: center;
}

/* QR Code Section - Inside the card */
.qr-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}

.qr-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    color-adjust: exact;
}

.qr-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 4px;
}

.qr-placeholder {
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 10px;
    text-align: center;
}

.qr-placeholder i {
    font-size: 24px;
    margin-bottom: 4px;
}

.qr-label {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Action Buttons */
.modal-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
    margin-top: 1.5rem;
}

.action-btn {
    padding: 0.5rem 0.75rem;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    min-width: 120px;
    justify-content: center;
    border: 1px solid;
}

/* Primary Button */
.btn-primary {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border-color: #1e3a8a;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover::before {
    left: 100%;
}

/* Secondary Button */
.btn-secondary {
    background: #ffffff;
    border-color: #d1d5db;
    color: #374151;
}

.btn-secondary:hover {
    border-color: #9ca3af;
    background: #f9fafb;
    transform: translateY(-1px);
}

/* Success Button */
.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    border-color: #047857;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.btn-success::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-success:hover::before {
    left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .patient-success-modal {
        margin: 20px;
        max-width: none;
    }
    
    .modal-header {
        padding: 0.75rem 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .id-card {
        width: 100%;
        max-width: 400px;
        height: 220px;
    }
    
    .card-body {
        flex-direction: column;
        gap: 10px;
        padding: 12px;
    }
    
    .qr-section {
        margin-left: 0;
        align-self: center;
    }
    
    .modal-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .action-btn {
        width: 100%;
    }
    
    .success-checkmark {
        width: 32px;
        height: 32px;
        font-size: 16px;
    }
    
    .modal-title {
        font-size: 0.875rem;
    }
}

/* Print Styles - Clean minimal print view */
@media print {
    .patient-success-overlay {
        position: static;
        background: none;
        backdrop-filter: none;
        padding: 0;
    }
    
    .patient-success-modal {
        box-shadow: none;
        border: none;
        transform: none;
        max-width: none;
        width: 100%;
        max-height: none;
    }
    
    .modal-header,
    .close-btn,
    .modal-actions {
        display: none;
    }
    
    .modal-body {
        padding: 0;
    }
    
    .id-card-container {
        page-break-inside: avoid;
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
    }
    
    .id-card {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%) !important;
    }
    
    .card-header {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
        background: rgba(255, 255, 255, 0.95) !important;
    }
    
    .patient-id {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
        background: rgba(255, 255, 255, 0.2) !important;
    }
    
    .qr-container {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
        background: rgba(255, 255, 255, 0.95) !important;
    }
    
    @page {
        margin: 0.5in;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
        color-adjust: exact;
    }
}

/* Focus and Accessibility */
.patient-success-modal:focus {
    outline: 3px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
}

.action-btn:focus {
    outline: 3px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
}

/* Animation Enhancement */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(30px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.patient-success-modal.animate-in {
    animation: modalSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}