/* Professional Dashboard Styles for ZimHealth-ID */

/* Enhanced navigation styling */
.main-navigation {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    box-shadow:
        0 4px 20px rgba(14, 165, 233, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 50;
}

.main-navigation::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #0ea5e9, #22c55e, #0ea5e9);
    background-size: 200% 100%;
    animation: shimmerNav 6s ease-in-out infinite;
}

@keyframes shimmerNav {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Subtle professional logo styling */
.nav-logo {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(22, 197, 94, 0.05));
    border: 1px solid rgba(14, 165, 233, 0.2);
    border-radius: 4px;
    padding: 0.375rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nav-logo:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(22, 197, 94, 0.08));
    border-color: rgba(14, 165, 233, 0.3);
    transform: translateY(-1px);
}

.nav-logo i {
    color: #0ea5e9;
    opacity: 0.8;
}

/* Professional angular navigation links */
.nav-link {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #0ea5e9, #22c55e);
    transition: width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nav-link:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.06), rgba(14, 165, 233, 0.02));
    border-color: rgba(14, 165, 233, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.12);
}

.nav-link:hover::before {
    width: 100%;
}

.nav-link.active {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.08), rgba(14, 165, 233, 0.04));
    border-color: rgba(14, 165, 233, 0.5);
    color: #0ea5e9;
    font-weight: 600;
}

.nav-link.active::before {
    width: 100%;
}

/* Government-grade dashboard layout */
.dashboard-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.dashboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Sleek professional dashboard header */
.dashboard-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

/* Sleek professional header content layout */
.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.header-left {
    flex: 1;
    text-align: left;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Clean professional header typography */
.header-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    letter-spacing: -0.025em;
}

.header-subtitle {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.125rem;
    font-weight: 400;
    opacity: 0.8;
}

/* Minimal professional user information styling */
.user-info {
    text-align: right;
    padding: 0.5rem 0.75rem;
    background: rgba(248, 250, 252, 0.6);
    border: 1px solid rgba(229, 231, 235, 0.2);
    border-radius: 4px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(14, 165, 233, 0.2);
}

.user-name {
    font-size: 0.8rem;
    font-weight: 500;
    color: #1f2937;
    margin: 0;
}

.user-timestamp {
    font-size: 0.7rem;
    color: #6b7280;
    margin-top: 0.125rem;
    font-weight: 400;
    opacity: 0.8;
}

/* Enhanced user avatar */
.user-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #22c55e 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 
        0 6px 20px rgba(14, 165, 233, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.user-avatar::before {
    content: '';
    position: absolute;
    inset: 2px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border-radius: 10px;
}

.user-avatar i {
    color: #0ea5e9;
    font-size: 1.125rem;
    position: relative;
    z-index: 1;
}

.user-avatar:hover {
    transform: translateY(-1px);
    box-shadow: 
        0 8px 25px rgba(14, 165, 233, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Modern statistics cards with custom border radius and perfect alignment */
.stat-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0 0 16px 16px;
    box-shadow:
        0 8px 25px rgba(14, 165, 233, 0.08),
        0 4px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    /* Ensure consistent height and alignment */
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--card-color, #0ea5e9), transparent);
    opacity: 0.7;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 
        0 15px 35px rgba(14, 165, 233, 0.15),
        0 8px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stat-card.patients { --card-color: #0ea5e9; }
.stat-card.appointments { --card-color: #22c55e; }
.stat-card.records { --card-color: #3b82f6; }
.stat-card.prescriptions { --card-color: #8b5cf6; }

/* Enhanced stat icons */
.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.stat-icon.medical {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05));
    border: 1px solid rgba(14, 165, 233, 0.2);
}

.stat-icon.health {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05));
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.stat-icon.blue {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.05));
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.stat-icon.purple {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(139, 92, 246, 0.05));
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.stat-icon.orange {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.05));
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.stat-icon.red {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05));
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.stat-icon.indigo {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(99, 102, 241, 0.05));
    border: 1px solid rgba(99, 102, 241, 0.2);
}

.stat-icon.yellow {
    background: linear-gradient(135deg, rgba(234, 179, 8, 0.15), rgba(234, 179, 8, 0.05));
    border: 1px solid rgba(234, 179, 8, 0.2);
}

.stat-icon.teal {
    background: linear-gradient(135deg, rgba(20, 184, 166, 0.15), rgba(20, 184, 166, 0.05));
    border: 1px solid rgba(20, 184, 166, 0.2);
}

.stat-icon.orange {
    background: linear-gradient(135deg, rgba(234, 88, 12, 0.15), rgba(234, 88, 12, 0.05));
    border: 1px solid rgba(234, 88, 12, 0.2);
}

.stat-card:hover .stat-icon {
    transform: scale(1.05);
}

/* Perfect alignment for stat card content */
.stat-card .flex.items-center {
    /* Main content area with icon and text */
    align-items: flex-start;
    margin-bottom: 1rem;
}

.stat-card .flex.items-center .ml-4 {
    /* Text content area */
    flex: 1;
    min-width: 0; /* Prevent text overflow */
}

.stat-card .flex.items-center .ml-4 p:first-child {
    /* Title text styling */
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.2;
    margin: 0 0 0.5rem 0;
}

.stat-card .flex.items-center .ml-4 p:last-child {
    /* Main number styling */
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    line-height: 1.1;
    margin: 0;
    letter-spacing: -0.025em;
}

.stat-card .mt-4 {
    /* Bottom statistics area */
    margin-top: auto;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(229, 231, 235, 0.3);
}

.stat-card .mt-4 .flex.items-center {
    /* Bottom stats alignment */
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 0;
}

.stat-card .mt-4 .flex.items-center span {
    /* Bottom stats text */
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.2;
}

/* Professional content cards */
.content-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    box-shadow: 
        0 8px 25px rgba(14, 165, 233, 0.08),
        0 4px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
}

.content-card:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 30px rgba(14, 165, 233, 0.12),
        0 6px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Enhanced card headers */
.card-header {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.3), transparent);
}

/* Enhanced unified quick action buttons */
.quick-actions-ribbon {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    box-shadow:
        0 12px 30px rgba(14, 165, 233, 0.1),
        0 6px 16px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.quick-actions-ribbon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #0ea5e9, #22c55e, #3b82f6, #8b5cf6, #0ea5e9);
    background-size: 300% 100%;
    animation: gradientShift 8s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.quick-action {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.85));
    border: 1px solid rgba(229, 231, 235, 0.3);
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    /* Base layout for Healthcare Command Center cards */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 120px;
    padding: 1rem;
}

.quick-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    transition: left 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.quick-action::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, var(--action-color, #0ea5e9), transparent);
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: 16px;
}

.quick-action:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 12px 30px rgba(14, 165, 233, 0.15),
        0 6px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(14, 165, 233, 0.4);
}

.quick-action:hover::before {
    left: 100%;
}

.quick-action:hover::after {
    opacity: 0.03;
}

.quick-action:active {
    transform: translateY(-1px) scale(0.98);
}

/* Color-specific hover effects */
.quick-action.medical {
    --action-color: #0ea5e9;
}

.quick-action.medical:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.08), rgba(14, 165, 233, 0.03));
    border-color: rgba(14, 165, 233, 0.4);
}

.quick-action.health {
    --action-color: #22c55e;
}

.quick-action.health:hover {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.08), rgba(34, 197, 94, 0.03));
    border-color: rgba(34, 197, 94, 0.4);
}

.quick-action.blue {
    --action-color: #3b82f6;
}

.quick-action.blue:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(59, 130, 246, 0.03));
    border-color: rgba(59, 130, 246, 0.4);
}

.quick-action.purple {
    --action-color: #8b5cf6;
}

.quick-action.purple:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.08), rgba(139, 92, 246, 0.03));
    border-color: rgba(139, 92, 246, 0.4);
}

.quick-action.orange {
    --action-color: #f59e0b;
}

.quick-action.orange:hover {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.08), rgba(245, 158, 11, 0.03));
    border-color: rgba(245, 158, 11, 0.4);
}

.quick-action.red {
    --action-color: #ef4444;
}

.quick-action.red:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.08), rgba(239, 68, 68, 0.03));
    border-color: rgba(239, 68, 68, 0.4);
}

.quick-action.indigo {
    --action-color: #6366f1;
}

.quick-action.indigo:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.08), rgba(99, 102, 241, 0.03));
    border-color: rgba(99, 102, 241, 0.4);
}

.quick-action.yellow {
    --action-color: #eab308;
}

.quick-action.yellow:hover {
    background: linear-gradient(135deg, rgba(234, 179, 8, 0.08), rgba(234, 179, 8, 0.03));
    border-color: rgba(234, 179, 8, 0.4);
}

.quick-action.teal {
    --action-color: #14b8a6;
}

.quick-action.teal:hover {
    background: linear-gradient(135deg, rgba(20, 184, 166, 0.08), rgba(20, 184, 166, 0.03));
    border-color: rgba(20, 184, 166, 0.4);
}

.quick-action.orange {
    --action-color: #ea580c;
}

.quick-action.orange:hover {
    background: linear-gradient(135deg, rgba(234, 88, 12, 0.08), rgba(234, 88, 12, 0.03));
    border-color: rgba(234, 88, 12, 0.4);
}

/* Enhanced Healthcare Command Center Card Styling */

/* Perfect icon centering within stat-icon containers - Override Tailwind */
.quick-action .stat-icon {
    /* Ensure icon container is perfectly centered */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 0.75rem auto !important;
    flex-shrink: 0;
    width: 48px !important;
    height: 48px !important;
    position: relative;
}

/* Ensure icons within stat-icon are perfectly centered */
.quick-action .stat-icon i {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    line-height: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: auto;
    height: auto;
}

/* Enhanced text hierarchy for Healthcare Command Center - Override Tailwind */
.quick-action span.text-sm {
    /* Primary text - main action label */
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    line-height: 1.25 !important;
    margin: 0.25rem 0 0.25rem 0 !important;
    letter-spacing: -0.025em !important;
    text-align: center !important;
    display: block !important;
}

.quick-action span.text-xs {
    /* Secondary text - subtitle */
    font-size: 0.75rem !important;
    font-weight: 400 !important;
    color: #6b7280 !important;
    line-height: 1.2 !important;
    margin: 0 !important;
    opacity: 0.85 !important;
    letter-spacing: 0.025em !important;
    text-align: center !important;
    display: block !important;
}

/* Hover state enhancements for text hierarchy - Override Tailwind */
.quick-action:hover span.text-sm {
    color: #000000 !important;
    font-weight: 700 !important;
}

.quick-action:hover span.text-xs {
    color: #4b5563 !important;
    opacity: 1 !important;
}

/* Additional FontAwesome icon centering fixes */
.quick-action .stat-icon .fas,
.quick-action .stat-icon .far,
.quick-action .stat-icon .fab {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    transform: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Ensure the stat-icon container itself is properly positioned */
.quick-action .stat-icon {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Enhanced activity timeline styling */
.activity-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    box-shadow:
        0 12px 30px rgba(14, 165, 233, 0.08),
        0 6px 16px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.activity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #0ea5e9, #22c55e, #3b82f6, #8b5cf6);
    background-size: 300% 100%;
    animation: activityGradient 8s ease-in-out infinite;
}

@keyframes activityGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.activity-header {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.7));
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    position: relative;
    padding: 1.5rem;
}

.activity-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.3), transparent);
}

.activity-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.activity-title-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}

.activity-title-icon i {
    color: white;
    font-size: 1rem;
}

.activity-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    font-weight: 500;
}

.activity-content {
    padding: 1.5rem;
}

.activity-item {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.6), rgba(255, 255, 255, 0.4));
    border: 1px solid rgba(229, 231, 235, 0.3);
    position: relative;
    overflow: hidden;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #0ea5e9, #22c55e);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.activity-item:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border-color: rgba(14, 165, 233, 0.3);
    transform: translateX(6px);
    box-shadow: 0 6px 20px rgba(14, 165, 233, 0.1);
}

.activity-item:hover::before {
    opacity: 1;
}

.activity-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05));
    border: 1px solid rgba(14, 165, 233, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.activity-icon::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(34, 197, 94, 0.2));
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.activity-item:hover .activity-icon {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.25), rgba(14, 165, 233, 0.1));
    border-color: rgba(14, 165, 233, 0.4);
}

.activity-item:hover .activity-icon::before {
    opacity: 1;
}

.activity-details {
    flex: 1;
    min-width: 0;
}

.activity-title-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.4;
}

.activity-description {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.25rem;
    line-height: 1.4;
}

.activity-timestamp {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-top: 0.5rem;
    font-weight: 500;
}

.activity-empty {
    text-align: center;
    padding: 3rem 1.5rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.6));
    border-radius: 12px;
    border: 2px dashed rgba(229, 231, 235, 0.5);
}

.activity-empty-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.15), rgba(156, 163, 175, 0.05));
    border: 1px solid rgba(156, 163, 175, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.activity-empty-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    margin: 0 0 0.5rem;
}

.activity-empty-subtitle {
    font-size: 0.8rem;
    color: #9ca3af;
    margin: 0;
}

/* Appointment cards */
.appointment-card {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
    border: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.appointment-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border-color: rgba(14, 165, 233, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.1);
}

/* Status badges */
.status-badge {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05));
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #15803d;
    font-weight: 600;
    border-radius: 8px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    transition: all 0.3s ease;
}

.status-badge:hover {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(34, 197, 94, 0.1));
    transform: scale(1.05);
}

/* Enhanced responsive design */
@media (max-width: 768px) {
    .stat-card {
        border-radius: 0 0 12px 12px;
    }

    .content-card {
        border-radius: 12px;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    /* Mobile navigation adjustments */
    .nav-logo {
        padding: 0.25rem;
        border-radius: 3px;
    }

    .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 1px;
    }

    /* Mobile header adjustments */
    .header-content {
        flex-direction: row;
        gap: 0.5rem;
        padding: 0.75rem 0;
    }

    .header-left {
        text-align: left;
    }

    .header-title {
        font-size: 1.25rem;
    }

    .header-subtitle {
        font-size: 0.75rem;
    }

    .header-right {
        gap: 0.5rem;
    }

    .user-info {
        padding: 0.375rem 0.5rem;
    }
}

@media (max-width: 640px) {
    .nav-link span {
        display: none;
    }

    .nav-link {
        padding: 0.5rem;
    }

    .header-left {
        display: none;
    }

    .quick-actions-ribbon {
        border-radius: 16px;
        margin: 0 0.5rem;
    }

    /* Mobile optimizations for Healthcare Command Center cards */
    .quick-action {
        min-height: 100px !important;
        padding: 0.75rem !important;
    }

    .quick-action .stat-icon {
        width: 40px !important;
        height: 40px !important;
        margin-bottom: 0.5rem !important;
    }

    .quick-action span.text-sm {
        font-size: 0.8125rem !important;
    }

    .quick-action span.text-xs {
        font-size: 0.6875rem !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
