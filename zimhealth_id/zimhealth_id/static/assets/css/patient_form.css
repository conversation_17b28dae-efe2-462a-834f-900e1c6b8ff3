/* Professional Patient Form Styles for ZimHealth-ID */

/* Government-grade patient form layout */
.patient-form-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.patient-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Professional patient form header */
.patient-form-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

.patient-form-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #0ea5e9, #22c55e, #0ea5e9);
    background-size: 200% 100%;
    animation: shimmerPatient 6s ease-in-out infinite;
}

@keyframes shimmerPatient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Patient form status indicator */
.patient-form-status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(14, 165, 233, 0.05));
    border: 1px solid rgba(14, 165, 233, 0.2);
    border-radius: 8px;
    padding: 0.5rem 1rem;
}

/* Patient form layout */
.patient-form-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Patient form cards */
.patient-form-card,
.patient-form-tips-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(229, 231, 235, 0.3);
    border-radius: 16px;
    box-shadow:
        0 20px 40px -12px rgba(14, 165, 233, 0.15),
        0 8px 25px -8px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.patient-form-card:hover,
.patient-form-tips-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(14, 165, 233, 0.25),
        0 12px 30px -8px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Patient form section headers */
.patient-form-section-header {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
}

.patient-form-section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    margin: 0;
}

.patient-form-section-title i {
    margin-right: 0.75rem;
    color: #0ea5e9;
    font-size: 1.25rem;
}

.patient-form-section-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0.5rem 0 0 0;
    line-height: 1.4;
}

/* Patient form grid layout */
.patient-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Patient form groups */
.patient-form-group {
    display: flex;
    flex-direction: column;
}

.patient-form-group-full {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
}

/* Patient form labels */
.patient-form-label {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

/* Patient form inputs */
.patient-form-input,
.patient-form-select,
.patient-form-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
}

.patient-form-input:focus,
.patient-form-select:focus,
.patient-form-textarea:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
    background: rgba(255, 255, 255, 1);
}

.patient-form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Patient form errors */
.patient-form-error {
    margin-top: 0.5rem;
    color: #dc2626;
    font-size: 0.875rem;
}

/* Patient allergy section */
.patient-allergy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.patient-allergy-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
    border: 1px solid rgba(229, 231, 235, 0.3);
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.patient-allergy-item:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(14, 165, 233, 0.02));
    border-color: rgba(14, 165, 233, 0.2);
}

.patient-checkbox {
    margin-right: 0.5rem;
    border-radius: 4px;
    border: 1px solid #d1d5db;
    color: #dc2626;
}

.patient-checkbox:focus {
    ring: 2px;
    ring-color: #dc2626;
}

.patient-checkbox-label {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
}

/* Patient form actions */
.patient-form-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(229, 231, 235, 0.3);
    margin-top: 1.5rem;
}

@media (min-width: 640px) {
    .patient-form-actions {
        flex-direction: row;
    }
}

.patient-action-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.patient-action-button.primary {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.4);
}

.patient-action-button.primary:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 50%, #075985 100%);
    box-shadow: 0 6px 20px rgba(14, 165, 233, 0.5);
    transform: translateY(-2px);
}

.patient-action-button.secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 50%, #374151 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.4);
}

.patient-action-button.secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 50%, #1f2937 100%);
    box-shadow: 0 6px 20px rgba(107, 114, 128, 0.5);
    transform: translateY(-2px);
}

/* Patient tips styling */
.patient-tip-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
    border: 1px solid rgba(229, 231, 235, 0.3);
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.patient-tip-item:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(14, 165, 233, 0.02));
    border-color: rgba(14, 165, 233, 0.2);
    transform: translateY(-2px);
}

.patient-tip-item i {
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.patient-tip-title {
    font-weight: 600;
    color: #111827;
    font-size: 0.875rem;
    margin: 0 0 0.25rem 0;
}

.patient-tip-desc {
    color: #6b7280;
    font-size: 0.75rem;
    margin: 0;
    line-height: 1.4;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .patient-form-container {
        padding: 0;
    }
    
    .patient-form-header {
        border-radius: 0;
    }
    
    .patient-form-card,
    .patient-form-tips-card {
        border-radius: 12px;
        margin: 0 0.5rem;
    }
    
    .patient-form-section-header {
        padding: 1rem;
    }
    
    .patient-form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .patient-allergy-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.5rem;
    }
    
    .patient-allergy-item {
        padding: 0.5rem;
    }
    
    .patient-tip-item {
        padding: 0.75rem;
    }
}
