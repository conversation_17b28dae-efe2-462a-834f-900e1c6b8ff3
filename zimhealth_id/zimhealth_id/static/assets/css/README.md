# CSS Assets Directory

This directory contains CSS stylesheets for the ZimHealth-ID application.

## Purpose
- Store custom CSS files for styling the application
- Organize theme-specific stylesheets
- Include responsive design CSS files
- Store CSS frameworks and libraries

## File Organization
```
css/
├── main.css          # Main application stylesheet
├── zhid_auth.css          # Authentication pages styling
├── dashboard.css     # Dashboard specific styles
├── components/       # Component-specific CSS files
└── vendor/          # Third-party CSS libraries
```

## Usage
CSS files in this directory are automatically served by Django's static file handling system and can be included in templates using:

```html
{% load static %}
<link rel="stylesheet" type="text/css" href="{% static 'assets/css/filename.css' %}">
```

## Development Notes
- Follow BEM methodology for CSS class naming
- Use CSS variables for consistent theming
- Ensure responsive design compatibility
- Minimize and optimize CSS files for production
