/* Professional Prescription Detail Page Styles for ZimHealth-ID */

/* Single-page layout - no scrolling */
.prescription-detail-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Sophisticated background pattern */
.prescription-detail-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Use consistent prescriptions-header styling from other pages */
.prescription-detail-container .prescriptions-header {
    flex-shrink: 0;
    position: relative;
    z-index: 10;
}

/* Main content area - fills remaining space with proper padding */
.prescription-detail-container > .max-w-7xl {
    flex: 1;
    overflow: hidden;
    max-width: none !important;
    width: 100% !important;
    position: relative;
    z-index: 5;
}

/* Grid layout optimized for single page */
.prescription-content-grid {
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 1.5rem;
    overflow: hidden;
}

.prescription-main-content {
    overflow-y: auto;
    max-height: 100%;
    padding-right: 0.5rem;
}

.prescription-sidebar {
    overflow-y: auto;
    max-height: 100%;
    padding-right: 0.5rem;
}

/* Enhanced patients-table-card styling for prescription details */
.prescription-detail-container .patients-table-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    box-shadow: 
        0 8px 25px rgba(14, 165, 233, 0.08),
        0 4px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.prescription-detail-container .patients-table-card:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 30px rgba(14, 165, 233, 0.12),
        0 6px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Enhanced card headers */
.prescription-detail-container .patients-table-header {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.7));
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    padding: 1rem 1.5rem !important;
    position: relative;
}

.prescription-detail-container .patients-table-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.3), transparent);
}

/* Enhanced table styling */
.prescription-detail-container .patients-table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.prescription-detail-container .patients-table thead {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.8));
}

.prescription-detail-container .patients-table th {
    padding: 0.75rem 1rem !important;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border-bottom: 2px solid rgba(229, 231, 235, 0.5);
}

.prescription-detail-container .patients-table td {
    padding: 0.75rem 1rem !important;
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    color: #374151;
    font-size: 0.875rem;
    vertical-align: middle;
    line-height: 1.4;
}

.prescription-detail-container .patients-table tbody tr {
    transition: all 0.2s ease;
}

.prescription-detail-container .patients-table tbody tr:hover {
    background: rgba(248, 250, 252, 0.6);
}

.prescription-detail-container .patients-table tbody tr:last-child td {
    border-bottom: none;
}

/* Medication header styling */
.medication-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(229, 231, 235, 0.3);
}

.medication-name {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, #1f2937, #374151);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced status badges */
.prescription-detail-container .status-badge,
.prescription-detail-container .inline-flex.items-center {
    padding: 0.375rem 0.875rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.8125rem;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    border: 1px solid;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.prescription-detail-container .bg-green-100 {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05)) !important;
    border-color: rgba(34, 197, 94, 0.3) !important;
    color: #15803d !important;
}

.prescription-detail-container .bg-blue-100 {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.05)) !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
    color: #1d4ed8 !important;
}

.prescription-detail-container .bg-red-100 {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05)) !important;
    border-color: rgba(239, 68, 68, 0.3) !important;
    color: #dc2626 !important;
}

.prescription-detail-container .bg-yellow-100 {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.05)) !important;
    border-color: rgba(245, 158, 11, 0.3) !important;
    color: #d97706 !important;
}

/* Enhanced buttons */
.prescription-detail-container .government-filter-button {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.85));
    border: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 8px;
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.prescription-detail-container .government-filter-button:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.08), rgba(14, 165, 233, 0.03));
    border-color: rgba(14, 165, 233, 0.4);
    color: #0ea5e9;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(14, 165, 233, 0.15);
}

/* Colored action buttons */
.prescription-detail-container .bg-green-600 {
    background: linear-gradient(135deg, #16a34a, #15803d) !important;
    border-color: #15803d !important;
}

.prescription-detail-container .bg-green-600:hover {
    background: linear-gradient(135deg, #15803d, #166534) !important;
    transform: translateY(-1px);
}

.prescription-detail-container .bg-yellow-600 {
    background: linear-gradient(135deg, #d97706, #b45309) !important;
    border-color: #b45309 !important;
}

.prescription-detail-container .bg-yellow-600:hover {
    background: linear-gradient(135deg, #b45309, #92400e) !important;
    transform: translateY(-1px);
}

.prescription-detail-container .bg-red-600 {
    background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
    border-color: #b91c1c !important;
}

.prescription-detail-container .bg-red-600:hover {
    background: linear-gradient(135deg, #b91c1c, #991b1b) !important;
    transform: translateY(-1px);
}

/* Enhanced links in tables */
.prescription-detail-container .patients-table a {
    color: #0ea5e9;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 1px solid transparent;
}

.prescription-detail-container .patients-table a:hover {
    color: #0284c7;
    border-bottom-color: #0284c7;
}

/* Results count styling */
.prescription-detail-container .results-count {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 0;
}

/* Status indicator in header */
.prescription-detail-container .status-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Responsive design for single-page layout */
@media (max-width: 1024px) {
    .prescription-content-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .prescription-main-content,
    .prescription-sidebar {
        max-height: none;
        overflow-y: visible;
    }
    
    .prescription-detail-container {
        max-height: none;
        overflow: visible;
    }
    
    .prescription-detail-container .patients-table-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .prescription-detail-container > .max-w-7xl {
        padding: 0.75rem !important;
    }
    
    .prescription-detail-container .patients-table-card {
        margin-bottom: 0.75rem;
        border-radius: 8px;
    }
    
    .prescription-detail-container .patients-table-header {
        padding: 0.75rem 1rem !important;
    }
    
    .medication-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .medication-name {
        font-size: 1.5rem;
    }
    
    .prescription-detail-container .patients-table th,
    .prescription-detail-container .patients-table td {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.8125rem;
    }
}

@media (max-width: 640px) {
    .prescription-detail-container .patients-table th,
    .prescription-detail-container .patients-table td {
        padding: 0.375rem 0.5rem !important;
        font-size: 0.75rem;
    }
    
    .medication-name {
        font-size: 1.25rem;
    }
    
    .prescription-detail-container .government-filter-button {
        padding: 0.5rem 1rem;
        font-size: 0.8125rem;
    }
}

/* Print styles */
@media print {
    .prescription-detail-container {
        background: white !important;
        max-height: none !important;
        overflow: visible !important;
    }
    
    .prescription-detail-container::before {
        display: none !important;
    }
    
    .prescriptions-header {
        background: white !important;
        border-bottom: 2px solid #000 !important;
        box-shadow: none !important;
    }
    
    .prescription-detail-container .patients-table-card {
        background: white !important;
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }
    
    .prescription-detail-container .patients-table-header {
        background: #f5f5f5 !important;
        border-bottom: 1px solid #000 !important;
    }
    
    .government-filter-button,
    .add-patient-button,
    .results-count {
        display: none !important;
    }
    
    .prescription-content-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Custom scrollbar for overflow areas */
.prescription-main-content::-webkit-scrollbar,
.prescription-sidebar::-webkit-scrollbar {
    width: 6px;
}

.prescription-main-content::-webkit-scrollbar-track,
.prescription-sidebar::-webkit-scrollbar-track {
    background: rgba(229, 231, 235, 0.2);
    border-radius: 3px;
}

.prescription-main-content::-webkit-scrollbar-thumb,
.prescription-sidebar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(139, 92, 246, 0.4), rgba(139, 92, 246, 0.6));
    border-radius: 3px;
}

.prescription-main-content::-webkit-scrollbar-thumb:hover,
.prescription-sidebar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(139, 92, 246, 0.6), rgba(139, 92, 246, 0.8));
}

/* Additional optimizations for single-page layout */
.prescription-detail-container .max-w-7xl {
    max-width: none !important;
}

/* Ensure content padding matches header exactly */
.prescription-detail-container > .max-w-7xl {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
}

@media (min-width: 640px) {
    .prescription-detail-container > .max-w-7xl {
        padding-left: 1.5rem !important;
        padding-right: 1.5rem !important;
    }
}

@media (min-width: 1024px) {
    .prescription-detail-container > .max-w-7xl {
        padding-left: 2rem !important;
        padding-right: 2rem !important;
    }
}