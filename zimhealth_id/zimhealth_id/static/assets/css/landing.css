/* Landing Page Custom Styles for ZimHealth-ID */

/* Smooth scrolling for the entire page */
html {
    scroll-behavior: smooth;
}

/* Performance-optimized animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 40px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translate3d(-40px, 0, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translate3d(40px, 0, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 60px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale3d(1, 1, 1);
    }
    50% {
        transform: scale3d(1.05, 1.05, 1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translate3d(0, 0, 0);
    }
    50% {
        transform: translate3d(0, -12px, 0);
    }
}

@keyframes gentleFloat {
    0%, 100% {
        transform: translate3d(0, 0, 0);
    }
    50% {
        transform: translate3d(0, -8px, 0);
    }
}

@keyframes parallaxMove {
    from {
        transform: translate3d(0, 0, 0);
    }
    to {
        transform: translate3d(0, -50px, 0);
    }
}

@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Animation classes with performance optimization */
.animate-fade-in-up {
    animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    will-change: transform, opacity;
}

.animate-slide-in-up {
    animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    will-change: transform, opacity;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    will-change: transform, opacity;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    will-change: transform, opacity;
}

.animate-pulse-slow {
    animation: pulse 4s ease-in-out infinite;
}

.animate-float {
    animation: float 8s ease-in-out infinite;
}

.animate-gentle-float {
    animation: gentleFloat 6s ease-in-out infinite;
}

/* Staggered animation delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }

/* Initial state for animated elements */
.animate-on-scroll {
    opacity: 0;
    transform: translate3d(0, 40px, 0);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}

/* Gradient text effect */
.gradient-text {
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced button hover effects */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateZ(0);
    will-change: transform;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn-primary:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:active {
    transform: translateY(0) scale(0.98);
}

/* Enhanced secondary button */
.btn-secondary {
    position: relative;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateZ(0);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(14, 165, 233, 0.2);
}

/* Enhanced card hover effects */
.feature-card {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateZ(0);
    will-change: transform, box-shadow;
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(14, 165, 233, 0.1);
}

.feature-card .feature-icon {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Navbar backdrop blur enhancement */
.navbar-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #0ea5e9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0284c7;
}

/* Enhanced hero section with parallax */
.hero-pattern {
    background-image:
        radial-gradient(circle at 25px 25px, rgba(14, 165, 233, 0.08) 2px, transparent 0),
        radial-gradient(circle at 75px 75px, rgba(34, 197, 94, 0.08) 2px, transparent 0);
    background-size: 100px 100px;
    position: relative;
    overflow: hidden;
}

.hero-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.02) 0%, rgba(34, 197, 94, 0.02) 100%);
    animation: parallaxMove 20s ease-in-out infinite alternate;
}

/* Parallax background elements */
.parallax-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.05) 0%, transparent 50%);
    animation: parallaxMove 15s ease-in-out infinite alternate-reverse;
}

/* Enhanced statistics counter animation */
.stat-number {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    display: inline-block;
}

.stat-number:hover {
    transform: scale(1.1);
    color: #0ea5e9;
}

.stat-number::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #0ea5e9, #22c55e);
    transition: all 0.3s ease;
    transform: translateX(-50%);
    border-radius: 2px;
}

.stat-number:hover::after {
    width: 100%;
}

/* Counter animation state */
.stat-number.counting {
    color: #0ea5e9;
    transform: scale(1.05);
}

/* Enhanced form focus states and micro-interactions */
.form-input {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.form-input:focus {
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.15);
    transform: translateY(-1px);
    border-color: #0ea5e9;
}

.form-input:hover {
    border-color: #0ea5e9;
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.1);
}

/* Form validation states */
.form-input.valid {
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-input.invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Input label animations */
.form-label {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.form-input:focus + .form-label,
.form-input:not(:placeholder-shown) + .form-label {
    transform: translateY(-8px) scale(0.9);
    color: #0ea5e9;
}

/* Loading animation for buttons */
.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .hero-pattern {
        background-size: 50px 50px;
    }
    
    .feature-card:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gradient-text {
        background: none;
        -webkit-text-fill-color: initial;
        color: #0ea5e9;
    }
}

/* Enhanced reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    html {
        scroll-behavior: auto;
    }

    .animate-on-scroll {
        opacity: 1 !important;
        transform: none !important;
    }

    .parallax-bg,
    .hero-pattern::before {
        animation: none !important;
    }
}

/* Typography animations */
.text-gradient {
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 3s ease-in-out infinite;
    background-size: 200% 100%;
}

.headline-animate {
    overflow: hidden;
    position: relative;
}

.headline-animate::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(90deg, transparent, #f8fafc, transparent);
    transform: translateX(-100%);
    animation: shimmer 2s ease-in-out;
}

/* Floating elements enhanced */
.floating-element {
    animation: gentleFloat 8s ease-in-out infinite;
    will-change: transform;
}

.floating-element:nth-child(2) {
    animation-delay: -2s;
}

.floating-element:nth-child(3) {
    animation-delay: -4s;
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here in the future */
}
