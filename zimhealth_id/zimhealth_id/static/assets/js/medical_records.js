/**
 * Professional Medical Records Page JavaScript for ZimHealth-ID
 * Handles search, filtering, QR scanning, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize medical records page functionality
    initMedicalRecordsSearch();
    initQRCodeScanner();
    initFilterFunctionality();
    initActionButtons();
    initScrollAnimations();
    // Remove initDemoData() as we're using real-time data
});

/**
 * Initialize enhanced medical records search functionality with real-time AJAX
 */
function initMedicalRecordsSearch() {
    const searchInput = document.getElementById('medical-record-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            performRealTimeFilter();
        }, 300));
    }
}

/**
 * Initialize QR Code Scanner functionality
 */
function initQRCodeScanner() {
    const qrButton = document.getElementById('qr-scanner-btn');
    const qrModal = document.getElementById('qr-modal');
    const closeQRBtn = document.getElementById('close-qr-btn');
    
    if (qrButton && qrModal) {
        qrButton.addEventListener('click', openQRScanner);
        closeQRBtn?.addEventListener('click', closeQRScanner);
        qrModal.addEventListener('click', function(e) {
            if (e.target === qrModal) closeQRScanner();
        });
    }
}

/**
 * Open QR Code Scanner
 */
async function openQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        });

        qrVideo.srcObject = stream;
        qrModal.classList.add('active');

        // Update modal content to show scanning status
        updateQRScannerStatus('Scanning for QR codes...', 'info');

        showNotification('QR Scanner activated. Point camera at patient QR code.', 'info');

        // Start QR code detection
        startQRDetection(qrVideo);

    } catch (error) {
        console.error('Error accessing camera:', error);
        showNotification('Camera access denied. Please enable camera permissions.', 'error');
    }
}

/**
 * Start QR Code Detection using jsQR library
 */
function startQRDetection(video) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let isScanning = true;

    function scanFrame() {
        if (!isScanning) return;

        const qrModal = document.getElementById('qr-modal');
        if (!qrModal || !qrModal.classList.contains('active')) {
            isScanning = false;
            return;
        }

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

            // Use jsQR library to detect QR codes
            if (typeof jsQR !== 'undefined') {
                const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });

                if (qrCode) {
                    console.log('QR Code detected:', qrCode.data);
                    updateQRScannerStatus('QR Code detected! Processing...', 'success');
                    isScanning = false;
                    handleQRCodeDetected(qrCode.data);
                    return;
                }

                // Update status to show active scanning
                updateQRScannerStatus('Scanning... Point camera at QR code', 'info');
            } else {
                // Fallback to mock detection if jsQR is not loaded
                console.warn('jsQR library not loaded, using fallback detection');
                updateQRScannerStatus('Demo mode: Simulating QR detection...', 'warning');
                const mockResult = detectQRPatternFallback();
                if (mockResult) {
                    updateQRScannerStatus('Demo QR Code detected!', 'success');
                    isScanning = false;
                    handleQRCodeDetected(mockResult);
                    return;
                }
            }
        }

        // Continue scanning
        requestAnimationFrame(scanFrame);
    }

    // Start scanning when video is ready
    video.addEventListener('loadedmetadata', () => {
        scanFrame();
    });

    // If video is already loaded, start immediately
    if (video.readyState >= video.HAVE_METADATA) {
        scanFrame();
    }

    // Stop scanning when modal is closed
    const qrModal = document.getElementById('qr-modal');
    if (qrModal) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (!qrModal.classList.contains('active')) {
                        isScanning = false;
                    }
                }
            });
        });
        observer.observe(qrModal, { attributes: true });
    }
}

/**
 * Handle QR Code Detection
 */
async function handleQRCodeDetected(qrData) {
    closeQRScanner();

    // Extract ZimHealth ID from QR data
    let zimhealthId = extractZimHealthId(qrData);

    if (!zimhealthId) {
        showNotification('Invalid QR code format. Please scan a valid patient QR code.', 'error');
        return;
    }

    // Show loading notification
    showNotification('Looking up patient medical records...', 'info');

    try {
        // Try to find patient via AJAX
        const response = await fetch(`/api/ajax/scan-qr/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `qr_data=${encodeURIComponent(zimhealthId)}`
        });

        const data = await response.json();

        if (data.success) {
            // Patient found - update search and filter to show only this patient's records
            const searchInput = document.getElementById('medical-record-search');
            if (searchInput) {
                // Set the search input to the ZimHealth ID
                searchInput.value = zimhealthId;

                // Clear other filters to ensure only search is active
                clearAllFilters();

                // Perform real-time filter to show only the scanned patient's records
                performRealTimeFilter();

                // Highlight the patient records after filtering
                setTimeout(() => {
                    highlightPatient(zimhealthId);
                }, 500);
            }

            showNotification(`Found medical records for: ${data.patient.full_name} (${zimhealthId})`, 'success');

        } else {
            showNotification(data.error || 'No medical records found for this patient.', 'error');
        }

    } catch (error) {
        console.error('QR lookup error:', error);
        showNotification('Error looking up patient records. Please try again.', 'error');
    }
}

/**
 * Close QR Code Scanner
 */
function closeQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');
    
    if (qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }
    
    qrModal.classList.remove('active');
}

/**
 * Extract ZimHealth ID from QR code data (inherited from patients page)
 */
function extractZimHealthId(qrData) {
    // Handle different QR code formats
    if (typeof qrData === 'string') {
        // Direct ZimHealth ID format: ZH-YYYY-XXXXXX
        if (qrData.match(/^ZH-\d{4}-\d{6}$/)) {
            return qrData;
        }

        // Multi-line format with ZimHealth-ID prefix
        const lines = qrData.split('\n');
        for (const line of lines) {
            if (line.startsWith('ZimHealth-ID:')) {
                const id = line.replace('ZimHealth-ID:', '').trim();
                if (id.match(/^ZH-\d{4}-\d{6}$/)) {
                    return id;
                }
            }
        }

        // Try to find ZH-YYYY-XXXXXX pattern anywhere in the string
        const match = qrData.match(/ZH-\d{4}-\d{6}/);
        if (match) {
            return match[0];
        }
    }

    return null;
}

/**
 * Clear all filter inputs except search
 */
function clearAllFilters() {
    const typeFilter = document.getElementById('type-filter');
    const statusFilter = document.getElementById('status-filter');
    const facilityFilter = document.getElementById('facility-filter');
    const dateFromFilter = document.getElementById('date-from-filter');
    const dateToFilter = document.getElementById('date-to-filter');

    if (typeFilter) typeFilter.value = '';
    if (statusFilter) statusFilter.value = '';
    if (facilityFilter) facilityFilter.value = '';
    if (dateFromFilter) dateFromFilter.value = '';
    if (dateToFilter) dateToFilter.value = '';
}

/**
 * Update QR scanner status message
 */
function updateQRScannerStatus(message, type = 'info') {
    const statusElement = document.querySelector('#qr-modal .text-sm.text-gray-600');
    if (statusElement) {
        statusElement.textContent = message;

        // Update color based on type
        statusElement.className = `text-sm ${
            type === 'success' ? 'text-green-600' :
            type === 'error' ? 'text-red-600' :
            type === 'warning' ? 'text-yellow-600' :
            'text-gray-600'
        }`;
    }
}

/**
 * Highlight found patient in table
 */
function highlightPatient(patientId) {
    const rows = document.querySelectorAll('.medical-records-table tbody tr');

    rows.forEach(row => {
        const patientIdElement = row.querySelector('.patient-column .text-xs');
        if (patientIdElement && patientIdElement.textContent.trim() === patientId) {
            // Enhanced highlighting for QR scan results
            row.style.background = 'linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.08))';
            row.style.border = '2px solid rgba(34, 197, 94, 0.5)';
            row.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.2)';
            row.style.transform = 'scale(1.02)';
            row.style.transition = 'all 0.3s ease';

            // Scroll to the highlighted record
            row.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Add a QR scan indicator
            const qrIndicator = document.createElement('div');
            qrIndicator.className = 'qr-scan-indicator';
            qrIndicator.innerHTML = '<i class="fas fa-qrcode"></i> Scanned';
            qrIndicator.style.cssText = `
                position: absolute;
                top: -10px;
                right: 10px;
                background: #22c55e;
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 10px;
                font-weight: bold;
                z-index: 10;
                animation: pulse 2s infinite;
            `;

            row.style.position = 'relative';
            row.appendChild(qrIndicator);

            // Remove highlighting after 5 seconds
            setTimeout(() => {
                row.style.background = '';
                row.style.border = '';
                row.style.boxShadow = '';
                row.style.transform = '';
                if (qrIndicator.parentNode) {
                    qrIndicator.remove();
                }
            }, 5000);
        }
    });
}

/**
 * Fallback QR pattern detection for demo purposes
 */
function detectQRPatternFallback() {
    // For demo purposes, simulate finding a QR code after a few seconds
    const now = Date.now();
    const scanStartTime = window.medicalRecordsQrScanStartTime || now;

    if (!window.medicalRecordsQrScanStartTime) {
        window.medicalRecordsQrScanStartTime = now;
    }

    // Simulate detection after 3-5 seconds
    if (now - scanStartTime > 3000 && Math.random() < 0.3) {
        // Return a mock ZimHealth ID from existing patients
        const mockIds = ['ZH-***********', 'ZH-***********', 'ZH-***********'];
        window.medicalRecordsQrScanStartTime = null; // Reset for next scan
        return mockIds[Math.floor(Math.random() * mockIds.length)];
    }

    return null;
}

/**
 * Get CSRF token for AJAX requests
 */
function getCsrfToken() {
    const csrfInput = document.querySelector('[name=csrfmiddlewaretoken]');
    return csrfInput ? csrfInput.value : '';
}

// Enhanced filtering functions inherited from patients page

/**
 * Initialize action buttons
 */
function initActionButtons() {
    const actionButtons = document.querySelectorAll('.medical-record-action-button');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Handle different actions
            const action = this.getAttribute('data-action');
            const recordId = this.getAttribute('data-record-id');
            
            switch(action) {
                case 'view':
                    handleViewRecord(recordId);
                    break;
                case 'edit':
                    handleEditRecord(recordId);
                    break;
                case 'download':
                    handleDownloadRecord(recordId);
                    break;
                case 'prescription':
                    handleAddPrescription(recordId);
                    break;
                case 'print':
                    handlePrintRecord(recordId);
                    break;
            }
        });
    });
}

/**
 * Handle medical record actions
 */
function handleViewRecord(recordId) {
    showNotification('Opening medical record details', 'info');
}

function handleEditRecord(recordId) {
    showNotification('Opening medical record editor', 'info');
}

function handleDownloadRecord(recordId) {
    showNotification('Downloading medical record PDF', 'success');
}

function handleAddPrescription(recordId) {
    showNotification('Opening prescription form', 'info');
}

function handlePrintRecord(recordId) {
    showNotification('Preparing record for printing', 'info');
}

/**
 * Update results count display
 */
function updateResultsCount(count) {
    const countElement = document.querySelector('.results-count');
    if (countElement) {
        countElement.textContent = `${count} medical records`;
    }
}

/**
 * Initialize scroll animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe cards and table elements
    document.querySelectorAll('.stats-card, .medical-records-table-card').forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

/**
 * Initialize demo data if no medical records exist
 */
function initDemoData() {
    const emptyState = document.querySelector('td[colspan="7"]');
    const recordsTBody = document.querySelector('.medical-records-table tbody');
    
    if (emptyState && recordsTBody) {
        // Create demo medical records
        const demoRecords = [
            {
                id: 'MR-001',
                patientName: 'Sarah Johnson',
                patientId: 'ZH-2024-001',
                date: 'Jul 28, 2025',
                time: '14:30',
                doctor: 'Dr. Smith',
                facility: 'Central Hospital',
                type: 'Consultation',
                diagnosis: 'Hypertension, Type 2 Diabetes',
                status: 'active',
                priority: 'normal'
            },
            {
                id: 'MR-002',
                patientName: 'Michael Brown',
                patientId: 'ZH-2024-002',
                date: 'Jul 27, 2025',
                time: '10:15',
                doctor: 'Dr. Wilson',
                facility: 'Medical Center',
                type: 'Follow-up',
                diagnosis: 'Post-surgical recovery monitoring',
                status: 'pending',
                priority: 'high'
            },
            {
                id: 'MR-003',
                patientName: 'Emma Wilson',
                patientId: 'ZH-2024-003',
                date: 'Jul 26, 2025',
                time: '16:45',
                doctor: 'Dr. Martinez',
                facility: 'Health Clinic',
                type: 'Emergency',
                diagnosis: 'Acute respiratory infection',
                status: 'archived',
                priority: 'urgent'
            }
        ];
        
        // Replace empty state with demo records
        recordsTBody.innerHTML = demoRecords.map(record => 
            createMedicalRecordTableRow(record)
        ).join('');
        
        // Update results count
        updateResultsCount(demoRecords.length);
        
        // Re-initialize interactions
        initActionButtons();
    }
}

/**
 * Create medical record table row HTML
 */
function createMedicalRecordTableRow(record) {
    return `
        <tr>
            <!-- Patient Column -->
            <td class="patient-column">
                <div class="flex items-center space-x-3">
                    <div class="record-status-indicator ${record.status}"></div>
                    <div class="medical-record-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="text-sm font-semibold text-gray-900">${record.patientName}</div>
                        <div class="text-xs text-gray-500">${record.patientId}</div>
                    </div>
                </div>
            </td>

            <!-- Date Column -->
            <td class="date-column">
                <div class="text-sm font-medium text-gray-900">${record.date}</div>
                <div class="text-xs text-gray-500">${record.time}</div>
            </td>

            <!-- Healthcare Provider Column -->
            <td class="provider-column">
                <div class="text-sm font-medium text-gray-900">${record.doctor}</div>
                <div class="text-xs text-gray-500 facility-text">${record.facility}</div>
            </td>

            <!-- Type Column -->
            <td class="type-column">
                <span class="record-type-badge">${record.type}</span>
                ${record.priority !== 'normal' ? `<div class="priority-indicator ${record.priority} mt-1 text-xs">${record.priority.toUpperCase()}</div>` : ''}
            </td>

            <!-- Diagnosis Column -->
            <td class="diagnosis-column">
                <div class="text-sm text-gray-900" title="${record.diagnosis}">${record.diagnosis.length > 40 ? record.diagnosis.substring(0, 40) + '...' : record.diagnosis}</div>
            </td>

            <!-- Status Column -->
            <td class="status-column">
                <span class="record-status-badge ${record.status}">${record.status.charAt(0).toUpperCase() + record.status.slice(1)}</span>
            </td>

            <!-- Actions Column -->
            <td class="actions-column">
                <div class="flex items-center justify-center space-x-1">
                    <button class="medical-record-action-button view" data-action="view" data-record-id="${record.id}" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="medical-record-action-button edit" data-action="edit" data-record-id="${record.id}" title="Edit Record">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="medical-record-action-button download" data-action="download" data-record-id="${record.id}" title="Download PDF">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="medical-record-action-button prescription" data-action="prescription" data-record-id="${record.id}" title="Add Prescription">
                        <i class="fas fa-pills"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.medical-records-notification');
    if (existing) existing.remove();
    
    const notification = document.createElement('div');
    const typeConfig = {
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };
    
    const config = typeConfig[type] || typeConfig.info;
    
    notification.className = `medical-records-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID Medical Records</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
`;
document.head.appendChild(style);

/**
 * Initialize filter functionality for all filter controls
 */
function initFilterFunctionality() {
    // Type filter
    const typeFilter = document.getElementById('type-filter');
    if (typeFilter) {
        typeFilter.addEventListener('change', performRealTimeFilter);
    }

    // Status filter
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', performRealTimeFilter);
    }

    // Facility filter
    const facilityFilter = document.getElementById('facility-filter');
    if (facilityFilter) {
        facilityFilter.addEventListener('change', performRealTimeFilter);
    }

    // Date range filters
    const dateFromFilter = document.getElementById('date-from-filter');
    const dateToFilter = document.getElementById('date-to-filter');

    if (dateFromFilter) {
        dateFromFilter.addEventListener('change', performRealTimeFilter);
    }

    if (dateToFilter) {
        dateToFilter.addEventListener('change', performRealTimeFilter);
    }

    // Initialize with current data
    performRealTimeFilter();
}

/**
 * Perform real-time filtering with AJAX
 */
function performRealTimeFilter(page = 1) {
    const searchInput = document.getElementById('medical-record-search');
    const typeFilter = document.getElementById('type-filter');
    const statusFilter = document.getElementById('status-filter');
    const facilityFilter = document.getElementById('facility-filter');
    const dateFromFilter = document.getElementById('date-from-filter');
    const dateToFilter = document.getElementById('date-to-filter');

    // Show loading state
    showLoadingState();

    // Collect filter parameters
    const params = new URLSearchParams({
        search: searchInput ? searchInput.value : '',
        type: typeFilter ? typeFilter.value : '',
        status: statusFilter ? statusFilter.value : '',
        facility: facilityFilter ? facilityFilter.value : '',
        date_from: dateFromFilter ? dateFromFilter.value : '',
        date_to: dateToFilter ? dateToFilter.value : '',
        page: page
    });

    // Remove empty parameters
    for (let [key, value] of [...params.entries()]) {
        if (!value) {
            params.delete(key);
        }
    }

    // Make AJAX request
    fetch(`/api/ajax/medical-records/filter/?${params.toString()}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            updateMedicalRecordsTable(data.records);
            updatePagination(data.pagination);
            updateResultsCount(data.pagination.total_count, data.pagination.start_index, data.pagination.end_index);
            hideLoadingState();
        } else {
            throw new Error(data.error || 'Unknown error occurred');
        }
    })
    .catch(error => {
        console.error('Filter error:', error);
        showErrorState('Failed to load medical records. Please try again.');
        hideLoadingState();
    });
}

/**
 * Update medical records table with new data
 */
function updateMedicalRecordsTable(records) {
    const tbody = document.querySelector('.medical-records-table tbody');
    if (!tbody) return;

    if (records.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="medical-record-avatar mx-auto mb-4">
                            <i class="fas fa-file-medical text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No medical records found</h3>
                        <p class="text-gray-500 mb-4">Try adjusting your search criteria or filters.</p>
                        <a href="/api/medical-records/new/" class="add-record-button">
                            <i class="fas fa-plus mr-2"></i>Add Medical Record
                        </a>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const rows = records.map(record => {
        const statusClass = getRecordStatusClass(record.status);
        const typeClass = getRecordTypeClass(record.record_type);

        return `
            <tr data-record-id="${record.id}" data-patient-id="${record.patient_zimhealth_id}">
                <!-- Patient Column -->
                <td class="patient-column px-4 py-3 border-b align-middle">
                    <div class="flex items-center space-x-3">
                        <div class="record-status-indicator ${statusClass}"></div>
                        <div class="medical-record-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <div class="text-sm font-semibold text-gray-900">${record.patient_name}</div>
                            <div class="text-xs text-gray-500">${record.patient_zimhealth_id}</div>
                        </div>
                    </div>
                </td>

                <!-- Date & Time Column -->
                <td class="date-column px-4 py-3 border-b align-middle">
                    <div class="text-sm font-medium text-gray-900">${record.date}</div>
                    <div class="text-xs text-gray-500">${record.time}</div>
                </td>

                <!-- Healthcare Provider Column -->
                <td class="provider-column px-4 py-3 border-b align-middle">
                    <div class="text-sm font-medium text-gray-900">${record.doctor_name}</div>
                    <div class="text-xs text-gray-500 facility-text">${record.facility_name}</div>
                </td>

                <!-- Record Type Column -->
                <td class="type-column px-4 py-3 border-b align-middle">
                    <span class="record-type-badge">${record.record_type_display || record.record_type}</span>
                </td>

                <!-- Diagnosis Column -->
                <td class="diagnosis-column px-4 py-3 border-b align-middle">
                    <div class="text-sm text-gray-900" title="${record.diagnosis}">
                        ${record.diagnosis && record.diagnosis.length > 40 ? record.diagnosis.substring(0, 40) + '...' : record.diagnosis}
                    </div>
                </td>

                <!-- Status Column -->
                <td class="status-column px-4 py-3 border-b align-middle">
                    <span class="record-status-badge ${statusClass}">${record.status_display || record.status}</span>
                </td>

                <!-- Actions Column -->
                <td class="actions-column px-4 py-3 border-b align-middle">
                    <div class="flex items-center justify-center space-x-1">
                        <a href="${record.detail_url}" class="medical-record-action-button view" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="${record.edit_url}" class="medical-record-action-button edit" title="Edit Record">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="/api/export-medical-records-pdf/?record_id=${record.id}" class="medical-record-action-button download" title="Download PDF">
                            <i class="fas fa-download"></i>
                        </a>
                        <a href="${record.prescription_url}" class="medical-record-action-button prescription" title="Add Prescription">
                            <i class="fas fa-pills"></i>
                        </a>
                        <button onclick="confirmDeleteMedicalRecord('${record.id}', '${record.patient_name}', '${record.date}')" class="medical-record-action-button delete" title="Delete Record">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    tbody.innerHTML = rows;
}

/**
 * Update pagination controls
 */
function updatePagination(pagination) {
    const paginationContainer = document.querySelector('.pagination-container');
    if (!paginationContainer) return;

    if (pagination.total_pages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }

    paginationContainer.style.display = 'block';

    let paginationHTML = '<div class="flex items-center justify-between">';

    // Previous button
    if (pagination.has_previous) {
        paginationHTML += `<button onclick="performRealTimeFilter(${pagination.current_page - 1})" class="pagination-btn">Previous</button>`;
    } else {
        paginationHTML += '<button class="pagination-btn disabled" disabled>Previous</button>';
    }

    // Page info
    paginationHTML += `<span class="text-sm text-gray-600">Page ${pagination.current_page} of ${pagination.total_pages}</span>`;

    // Next button
    if (pagination.has_next) {
        paginationHTML += `<button onclick="performRealTimeFilter(${pagination.current_page + 1})" class="pagination-btn">Next</button>`;
    } else {
        paginationHTML += '<button class="pagination-btn disabled" disabled>Next</button>';
    }

    paginationHTML += '</div>';
    paginationContainer.innerHTML = paginationHTML;
}

/**
 * Update results count display
 */
function updateResultsCount(totalCount, startIndex = 0, endIndex = 0) {
    const resultsCounts = document.querySelectorAll('.results-count');

    let countText;
    if (totalCount === 0) {
        countText = 'No medical records found';
    } else if (totalCount === 1) {
        countText = '1 medical record found';
    } else if (startIndex && endIndex) {
        countText = `Showing ${startIndex}-${endIndex} of ${totalCount} medical records`;
    } else {
        countText = `${totalCount} medical records found`;
    }

    resultsCounts.forEach(element => {
        element.textContent = countText;
    });
}

/**
 * Show loading state
 */
function showLoadingState() {
    const tbody = document.querySelector('.medical-records-table tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                        <p class="text-gray-500">Loading medical records...</p>
                    </div>
                </td>
            </tr>
        `;
    }

    // Update results count
    const resultsCounts = document.querySelectorAll('.results-count');
    resultsCounts.forEach(element => {
        element.textContent = 'Loading medical records...';
    });
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    // Loading state is hidden when table is updated
}

/**
 * Show error state
 */
function showErrorState(message) {
    const tbody = document.querySelector('.medical-records-table tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="text-red-500 mb-4">
                            <i class="fas fa-exclamation-triangle text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Medical Records</h3>
                        <p class="text-gray-500 mb-4">${message}</p>
                        <button onclick="performRealTimeFilter()" class="add-record-button">
                            <i class="fas fa-refresh mr-2"></i>Try Again
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
}

// Helper functions for styling
function getRecordStatusClass(status) {
    const statusClasses = {
        'active': 'active',
        'pending': 'pending',
        'archived': 'archived',
        'critical': 'critical'
    };
    return statusClasses[status] || 'active';
}

function getRecordTypeClass(type) {
    const typeClasses = {
        'consultation': 'consultation',
        'emergency': 'emergency',
        'follow_up': 'follow-up',
        'surgery': 'surgery',
        'screening': 'screening'
    };
    return typeClasses[type] || 'consultation';
}

// Medical record management functions
function confirmDeleteMedicalRecord(recordId, patientName, date) {
    if (!confirm(`Are you sure you want to delete the medical record for ${patientName} from ${date}? This action cannot be undone.`)) {
        return;
    }

    const formData = new FormData();
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch(`/api/medical-records/${recordId}/delete/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            performRealTimeFilter(); // Refresh the table
        } else {
            showNotification('Failed to delete medical record', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to delete medical record', 'error');
    });
}

// Simple notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}-notification`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            ${message}
        </div>
    `;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
