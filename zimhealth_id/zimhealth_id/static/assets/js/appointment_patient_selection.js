/**
 * Appointment Patient Selection with QR Code Scanning
 * Integrates with existing QR scanning functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAppointmentPatientSelection();
    initializeAppointmentFormButtons();
});

/**
 * Initialize appointment patient selection functionality
 */
function initializeAppointmentPatientSelection() {
    const searchInput = document.getElementById('appointment-patient-search');
    const qrScannerBtn = document.getElementById('appointment-qr-scanner-btn');
    const patientSelect = document.getElementById('appointment-patient-select');
    
    if (searchInput) {
        // Real-time patient search
        searchInput.addEventListener('input', debounce(performPatientSearch, 300));
    }
    
    if (qrScannerBtn) {
        // QR scanner button click
        qrScannerBtn.addEventListener('click', openAppointmentQRScanner);
    }
    
    // Initialize QR scanner modal events
    initializeQRScannerModal();
}

/**
 * Perform patient search
 */
async function performPatientSearch() {
    const searchInput = document.getElementById('appointment-patient-search');
    const patientSelect = document.getElementById('appointment-patient-select');
    const query = searchInput.value.trim();
    
    if (query.length < 2) {
        // Reset to default option
        patientSelect.innerHTML = '<option value="">No patient selected - search or scan QR code above</option>';
        return;
    }
    
    try {
        const response = await fetch(`/api/ajax/search-patients/?q=${encodeURIComponent(query)}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': getCsrfToken()
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            updatePatientSelect(data.patients || []);
        } else {
            console.error('Patient search failed:', response.status);
        }
    } catch (error) {
        console.error('Patient search error:', error);
    }
}

/**
 * Update patient select dropdown
 */
function updatePatientSelect(patients) {
    const patientSelect = document.getElementById('appointment-patient-select');
    
    // Clear existing options
    patientSelect.innerHTML = '';
    
    if (patients.length === 0) {
        patientSelect.innerHTML = '<option value="">No patients found - try different search terms</option>';
        return;
    }
    
    // Add default option
    patientSelect.innerHTML = '<option value="">Select a patient from search results</option>';
    
    // Add patient options
    patients.forEach(patient => {
        const option = document.createElement('option');
        option.value = patient.id;
        option.textContent = `${patient.full_name} (${patient.zimhealth_id}) - ${patient.phone_number}`;
        patientSelect.appendChild(option);
    });
}

/**
 * Open QR scanner for appointment patient selection
 */
function openAppointmentQRScanner() {
    const qrModal = document.getElementById('appointment-qr-modal');
    const qrVideo = document.getElementById('appointment-qr-video');
    
    if (!qrModal || !qrVideo) {
        console.error('QR scanner elements not found');
        return;
    }
    
    // Show modal
    qrModal.classList.remove('hidden');
    qrModal.classList.add('flex');
    
    // Start camera
    navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } })
        .then(stream => {
            qrVideo.srcObject = stream;
            qrVideo.play();
            
            // Start QR detection
            startAppointmentQRDetection(qrVideo);
            
            showNotification('QR Scanner activated. Point camera at patient QR code.', 'info');
        })
        .catch(error => {
            console.error('Camera access error:', error);
            showNotification('Camera access denied. Please allow camera access and try again.', 'error');
            closeAppointmentQRScanner();
        });
}

/**
 * Start QR code detection for appointment
 */
function startAppointmentQRDetection(video) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let isScanning = true;
    
    function scanFrame() {
        if (!isScanning) return;
        
        const qrModal = document.getElementById('appointment-qr-modal');
        if (!qrModal || qrModal.classList.contains('hidden')) {
            isScanning = false;
            return;
        }
        
        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            
            // Use jsQR library to detect QR codes
            if (typeof jsQR !== 'undefined') {
                const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });
                
                if (qrCode) {
                    console.log('QR Code detected:', qrCode.data);
                    isScanning = false;
                    handleAppointmentQRCodeDetected(qrCode.data);
                    return;
                }
            } else {
                // Fallback detection
                console.warn('jsQR library not loaded, using fallback');
                const mockResult = simulateQRDetection();
                if (mockResult) {
                    isScanning = false;
                    handleAppointmentQRCodeDetected(mockResult);
                    return;
                }
            }
        }
        
        // Continue scanning
        requestAnimationFrame(scanFrame);
    }
    
    // Start scanning
    video.addEventListener('loadedmetadata', () => {
        scanFrame();
    });
    
    if (video.readyState >= video.HAVE_METADATA) {
        scanFrame();
    }
}

/**
 * Handle QR code detection for appointment
 */
async function handleAppointmentQRCodeDetected(qrData) {
    closeAppointmentQRScanner();
    
    // Extract ZimHealth ID from QR data
    const zimhealthId = extractZimHealthId(qrData);
    
    if (!zimhealthId) {
        showNotification('Invalid QR code format. Please scan a valid patient QR code.', 'error');
        return;
    }
    
    try {
        // Use existing QR scan endpoint
        const response = await fetch(`/api/ajax/scan-qr/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `qr_data=${encodeURIComponent(zimhealthId)}`
        });
        
        if (response.ok) {
            const data = await response.json();
            
            if (data.success) {
                // Update search input and select patient
                const searchInput = document.getElementById('appointment-patient-search');
                const patientSelect = document.getElementById('appointment-patient-select');
                
                if (searchInput) {
                    searchInput.value = data.patient.full_name;
                }
                
                if (patientSelect) {
                    // Add the scanned patient to select and select them
                    patientSelect.innerHTML = `
                        <option value="">Select a patient</option>
                        <option value="${data.patient.id}" selected>
                            ${data.patient.full_name} (${data.patient.zimhealth_id}) - ${data.patient.phone_number || 'No phone'}
                        </option>
                    `;
                }
                
                showNotification(`Patient found: ${data.patient.full_name} (${zimhealthId})`, 'success');
                
                // Highlight the selection
                highlightPatientSelection();
                
            } else {
                showNotification(`Patient not found: ${data.error}`, 'error');
            }
        } else {
            showNotification('Error scanning QR code. Please try again.', 'error');
        }
        
    } catch (error) {
        console.error('QR scan error:', error);
        showNotification('Error processing QR code. Please try again.', 'error');
    }
}

/**
 * Close QR scanner
 */
function closeAppointmentQRScanner() {
    const qrModal = document.getElementById('appointment-qr-modal');
    const qrVideo = document.getElementById('appointment-qr-video');
    
    if (qrModal) {
        qrModal.classList.add('hidden');
        qrModal.classList.remove('flex');
    }
    
    if (qrVideo && qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }
}

/**
 * Initialize QR scanner modal events
 */
function initializeQRScannerModal() {
    const closeBtn = document.getElementById('appointment-close-qr-scanner');
    const qrModal = document.getElementById('appointment-qr-modal');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', closeAppointmentQRScanner);
    }
    
    if (qrModal) {
        // Close on overlay click
        qrModal.addEventListener('click', function(e) {
            if (e.target === qrModal) {
                closeAppointmentQRScanner();
            }
        });
        
        // Close on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !qrModal.classList.contains('hidden')) {
                closeAppointmentQRScanner();
            }
        });
    }
}

/**
 * Highlight patient selection
 */
function highlightPatientSelection() {
    const patientSelect = document.getElementById('appointment-patient-select');
    if (patientSelect) {
        patientSelect.style.background = 'linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05))';
        patientSelect.style.border = '2px solid rgba(34, 197, 94, 0.3)';
        
        setTimeout(() => {
            patientSelect.style.background = '';
            patientSelect.style.border = '';
        }, 3000);
    }
}

/**
 * Extract ZimHealth ID from QR data (reuse existing function)
 */
function extractZimHealthId(qrData) {
    if (!qrData) return null;
    
    // Direct ZimHealth ID format
    if (/^ZH-\d{4}-\d{6}$/.test(qrData)) {
        return qrData;
    }
    
    // Multi-line format
    const lines = qrData.split('\n');
    for (const line of lines) {
        if (line.startsWith('ZimHealth-ID:')) {
            const id = line.replace('ZimHealth-ID:', '').trim();
            if (/^ZH-\d{4}-\d{6}$/.test(id)) {
                return id;
            }
        }
    }
    
    // Pattern search
    const match = qrData.match(/ZH-\d{4}-\d{6}/);
    return match ? match[0] : null;
}

/**
 * Simulate QR detection for demo (fallback)
 */
function simulateQRDetection() {
    const now = Date.now();
    const scanStartTime = window.appointmentQrScanStartTime || now;
    
    if (!window.appointmentQrScanStartTime) {
        window.appointmentQrScanStartTime = now;
    }
    
    // Simulate detection after 3 seconds
    if (now - scanStartTime > 3000 && Math.random() < 0.3) {
        window.appointmentQrScanStartTime = null;
        return 'ZH-***********'; // Mock ZimHealth ID
    }
    
    return null;
}

/**
 * Utility functions
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function getCsrfToken() {
    const csrfInput = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfInput) return csrfInput.value;
    
    const csrfCookie = document.cookie.split(';').find(c => c.trim().startsWith('csrftoken='));
    return csrfCookie ? csrfCookie.split('=')[1] : '';
}

/**
 * Initialize appointment form buttons
 */
function initializeAppointmentFormButtons() {
    const form = document.getElementById('appointment-registration-form');
    const submitBtn = document.querySelector('.appointment-submit-btn');
    const cancelBtn = document.querySelector('.appointment-cancel-btn');

    if (form && submitBtn) {
        // Add form submission handling
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;

            // Update button text
            const buttonText = submitBtn.querySelector('span');
            if (buttonText) {
                buttonText.textContent = 'Scheduling...';
            }

            // If form validation fails, remove loading state
            setTimeout(() => {
                if (!form.checkValidity()) {
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;
                    if (buttonText) {
                        buttonText.textContent = 'Schedule Appointment';
                    }
                }
            }, 100);
        });
    }

    // Add hover effects and accessibility
    if (submitBtn) {
        submitBtn.addEventListener('mouseenter', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        submitBtn.addEventListener('mouseleave', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(0)';
            }
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
        });

        cancelBtn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    }
}

function showNotification(message, type = 'info') {
    // Use global notification function if available
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }

    // Fallback notification
    console.log(`${type.toUpperCase()}: ${message}`);

    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
        type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
        type === 'warning' ? 'bg-yellow-600' :
        'bg-blue-600'
    }`;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}
