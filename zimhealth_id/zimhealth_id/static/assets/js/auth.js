/**
 * Professional Authentication JavaScript for ZimHealth-ID
 * Handles form interactions, validation, and animations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    // Initialize authentication functionality
    initPasswordToggle();
    initFormValidation();
    initFormSubmission();
    
    if (!prefersReducedMotion) {
        initFormAnimations();
        initInputAnimations();
    }
});

/**
 * Enhanced password visibility toggle
 */
function initPasswordToggle() {
    const passwordField = document.getElementById('id_password');
    const toggleButton = document.querySelector('.password-toggle');
    
    if (passwordField && toggleButton) {
        toggleButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            const icon = this.querySelector('i');
            const isPassword = passwordField.type === 'password';
            
            // Toggle password visibility
            passwordField.type = isPassword ? 'text' : 'password';
            
            // Update icon with smooth transition
            icon.style.transform = 'scale(0.8)';
            
            setTimeout(() => {
                if (isPassword) {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
                icon.style.transform = 'scale(1)';
            }, 150);
        });
    }
}

/**
 * Real-time form validation
 */
function initFormValidation() {
    const form = document.querySelector('.auth-form');
    const inputs = form.querySelectorAll('.auth-input');
    
    inputs.forEach(input => {
        // Real-time validation on input
        input.addEventListener('input', function() {
            validateField(this);
        });
        
        // Validation on blur
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        // Clear validation on focus
        input.addEventListener('focus', function() {
            clearFieldValidation(this);
        });
    });
}

/**
 * Validate individual form field
 */
function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldName = field.name;
    
    // Clear previous validation
    clearFieldValidation(field);
    
    // Validation rules
    let isValid = true;
    let errorMessage = '';
    
    if (fieldName === 'username') {
        if (value.length === 0) {
            isValid = false;
            errorMessage = 'Username or email is required';
        } else if (value.includes('@')) {
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid email address';
            }
        } else if (value.length < 3) {
            isValid = false;
            errorMessage = 'Username must be at least 3 characters';
        }
    }
    
    if (fieldName === 'password') {
        if (value.length === 0) {
            isValid = false;
            errorMessage = 'Password is required';
        } else if (value.length < 6) {
            isValid = false;
            errorMessage = 'Password must be at least 6 characters';
        }
    }
    
    // Apply validation state
    if (!isValid) {
        showFieldError(field, errorMessage);
    } else {
        showFieldSuccess(field);
    }
    
    return isValid;
}

/**
 * Show field error state
 */
function showFieldError(field, message) {
    const wrapper = field.closest('.auth-input-group');
    const existingError = wrapper.querySelector('.field-error');
    
    // Add error styling
    field.classList.add('error');
    field.style.borderColor = '#ef4444';
    field.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
    
    // Show error message
    if (!existingError) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error auth-error';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
        wrapper.appendChild(errorDiv);
    }
}

/**
 * Show field success state
 */
function showFieldSuccess(field) {
    const wrapper = field.closest('.auth-input-group');
    
    // Add success styling
    field.classList.remove('error');
    field.classList.add('valid');
    field.style.borderColor = '#22c55e';
    field.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';
}

/**
 * Clear field validation state
 */
function clearFieldValidation(field) {
    const wrapper = field.closest('.auth-input-group');
    const errorDiv = wrapper.querySelector('.field-error');
    
    // Remove styling
    field.classList.remove('error', 'valid');
    field.style.borderColor = '';
    field.style.boxShadow = '';
    
    // Remove error message
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Enhanced form submission with loading states
 */
function initFormSubmission() {
    const form = document.querySelector('.auth-form');
    const submitButton = form.querySelector('.auth-button');
    
    form.addEventListener('submit', function(e) {
        // Validate all fields before submission
        const inputs = form.querySelectorAll('.auth-input');
        let isFormValid = true;
        
        inputs.forEach(input => {
            if (!validateField(input)) {
                isFormValid = false;
            }
        });
        
        if (!isFormValid) {
            e.preventDefault();
            showFormError('Please correct the errors above');
            return;
        }
        
        // Show loading state
        submitButton.classList.add('loading');
        submitButton.disabled = true;
        
        // Add a small delay to show loading state
        setTimeout(() => {
            // Form will submit naturally
        }, 300);
    });
}

/**
 * Show form-level error message
 */
function showFormError(message) {
    const form = document.querySelector('.auth-form');
    const existingError = form.querySelector('.form-error');
    
    if (existingError) {
        existingError.remove();
    }
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'form-error auth-error';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;
    
    // Insert at the top of the form
    form.insertBefore(errorDiv, form.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

/**
 * Initialize form animations
 */
function initFormAnimations() {
    // Stagger input group animations
    const inputGroups = document.querySelectorAll('.auth-input-group');
    inputGroups.forEach((group, index) => {
        group.style.animationDelay = `${0.3 + (index * 0.1)}s`;
    });
}

/**
 * Enhanced input animations and interactions
 */
function initInputAnimations() {
    const inputs = document.querySelectorAll('.auth-input');
    
    inputs.forEach(input => {
        // Floating label effect
        input.addEventListener('focus', function() {
            const label = this.closest('.auth-input-group').querySelector('.auth-label');
            if (label) {
                label.style.transform = 'translateY(-2px)';
                label.style.color = '#0ea5e9';
            }
        });
        
        input.addEventListener('blur', function() {
            const label = this.closest('.auth-input-group').querySelector('.auth-label');
            if (label && !this.value) {
                label.style.transform = '';
                label.style.color = '';
            }
        });
        
        // Ripple effect on focus
        input.addEventListener('focus', function() {
            createRippleEffect(this);
        });
    });
}

/**
 * Create ripple effect for inputs
 */
function createRippleEffect(element) {
    const ripple = document.createElement('div');
    ripple.style.position = 'absolute';
    ripple.style.borderRadius = '50%';
    ripple.style.background = 'rgba(14, 165, 233, 0.3)';
    ripple.style.transform = 'scale(0)';
    ripple.style.animation = 'ripple 0.6s linear';
    ripple.style.left = '50%';
    ripple.style.top = '50%';
    ripple.style.width = '20px';
    ripple.style.height = '20px';
    ripple.style.marginLeft = '-10px';
    ripple.style.marginTop = '-10px';
    ripple.style.pointerEvents = 'none';
    
    const wrapper = element.closest('.auth-input-wrapper');
    wrapper.style.position = 'relative';
    wrapper.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Add ripple animation CSS
 */
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

/**
 * Remember me functionality
 */
function initRememberMe() {
    const rememberCheckbox = document.querySelector('#remember-me');
    const usernameField = document.querySelector('#id_username');
    
    if (rememberCheckbox && usernameField) {
        // Load saved username
        const savedUsername = localStorage.getItem('zimhealth_username');
        if (savedUsername) {
            usernameField.value = savedUsername;
            rememberCheckbox.checked = true;
        }
        
        // Save username on form submission
        const form = document.querySelector('.auth-form');
        form.addEventListener('submit', function() {
            if (rememberCheckbox.checked) {
                localStorage.setItem('zimhealth_username', usernameField.value);
            } else {
                localStorage.removeItem('zimhealth_username');
            }
        });
    }
}

// Initialize remember me functionality
document.addEventListener('DOMContentLoaded', initRememberMe);
