/* Professional Patient QR Code JavaScript for ZimHealth-ID */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize QR code page functionality
    initializeQRCodePage();
});

function initializeQRCodePage() {
    console.log('Patient QR Code page initialized');
    
    // Ensure proper layout on load
    adjustLayoutForSinglePage();
    
    // Handle window resize
    window.addEventListener('resize', adjustLayoutForSinglePage);
    
    // Initialize QR code interactions
    initializeQRCodeInteractions();
}

function adjustLayoutForSinglePage() {
    // Ensure the layout fits within viewport
    const container = document.querySelector('.qr-code-container');
    if (container) {
        const viewportHeight = window.innerHeight;
        const headerHeight = document.querySelector('.qr-header')?.offsetHeight || 0;
        const availableHeight = viewportHeight - headerHeight;
        
        const contentArea = container.querySelector('.max-w-7xl');
        if (contentArea) {
            contentArea.style.maxHeight = `${availableHeight}px`;
        }
    }
}

function initializeQRCodeInteractions() {
    // Add hover effects to QR code
    const qrWrapper = document.querySelector('.qr-code-wrapper');
    if (qrWrapper) {
        qrWrapper.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });
        
        qrWrapper.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
        
        // Add click to enlarge functionality
        qrWrapper.addEventListener('click', function() {
            enlargeQRCode();
        });
    }
}

function enlargeQRCode() {
    const qrImage = document.querySelector('.qr-code-image');
    if (!qrImage || !qrImage.src) return;
    
    // Create modal overlay
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        cursor: pointer;
    `;
    
    // Create enlarged QR code
    const enlargedQR = document.createElement('img');
    enlargedQR.src = qrImage.src;
    enlargedQR.style.cssText = `
        max-width: 80%;
        max-height: 80%;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        background: white;
        padding: 20px;
    `;
    
    modal.appendChild(enlargedQR);
    document.body.appendChild(modal);
    
    // Close on click
    modal.addEventListener('click', function() {
        document.body.removeChild(modal);
    });
    
    // Close on escape
    const escapeHandler = function(e) {
        if (e.key === 'Escape') {
            document.body.removeChild(modal);
            document.removeEventListener('keydown', escapeHandler);
        }
    };
    document.addEventListener('keydown', escapeHandler);
}

function downloadQR() {
    const qrImage = document.querySelector('.qr-code-image');
    if (!qrImage || !qrImage.src) {
        showNotification('No QR code available to download', 'error');
        return;
    }
    
    // Create download link
    const link = document.createElement('a');
    link.href = qrImage.src;
    link.download = `qr-code-${getPatientId()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('QR code downloaded successfully', 'success');
}

function generateQRCode(patientId) {
    if (!patientId) {
        showNotification('Patient ID not found', 'error');
        return;
    }
    
    // Show loading state
    const generateBtn = document.querySelector('button[onclick*="generateQRCode"]');
    if (generateBtn) {
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>Generating...';
    }
    
    // In a real implementation, this would make an API call to generate the QR code
    // For now, we'll simulate the process
    setTimeout(() => {
        showNotification('QR code generation would be implemented here with a QR code generation library.', 'info');
        
        if (generateBtn) {
            generateBtn.disabled = false;
            generateBtn.innerHTML = 'Generate QR Code';
        }
    }, 2000);
}

function getPatientId() {
    // Try to get from QR code ID display
    const qrCodeId = document.querySelector('.qr-code-id');
    if (qrCodeId) {
        return qrCodeId.textContent.trim();
    }
    
    // Try to get from URL
    const urlParts = window.location.pathname.split('/');
    const patientIndex = urlParts.indexOf('patients');
    if (patientIndex !== -1 && urlParts[patientIndex + 1]) {
        return urlParts[patientIndex + 1];
    }
    
    return 'patient';
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `qr-notification qr-notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#22c55e' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        font-size: 14px;
        font-weight: 500;
        max-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after delay
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Enhanced print function
function printQRCode() {
    // Hide action buttons and non-essential elements for printing
    const elementsToHide = document.querySelectorAll('.qr-actions, .qr-btn, .qr-header-right');
    const originalDisplay = [];
    
    elementsToHide.forEach((element, index) => {
        originalDisplay[index] = element.style.display;
        element.style.display = 'none';
    });
    
    // Add print-specific styles
    const printStyles = document.createElement('style');
    printStyles.textContent = `
        @media print {
            .qr-code-container {
                background: white !important;
                max-height: none !important;
                overflow: visible !important;
            }
            .qr-code-container::before {
                display: none !important;
            }
            .qr-header {
                background: white !important;
                border-bottom: 2px solid #000 !important;
                box-shadow: none !important;
            }
            .qr-card {
                background: white !important;
                border: 1px solid #000 !important;
                box-shadow: none !important;
                break-inside: avoid;
            }
            .qr-card-header {
                background: #f5f5f5 !important;
                border-bottom: 1px solid #000 !important;
            }
            .qr-content-grid {
                grid-template-columns: 1fr !important;
                gap: 1rem !important;
            }
            .qr-display-area {
                page-break-inside: avoid;
            }
        }
    `;
    document.head.appendChild(printStyles);
    
    // Print
    window.print();
    
    // Restore elements after printing
    setTimeout(() => {
        elementsToHide.forEach((element, index) => {
            element.style.display = originalDisplay[index];
        });
        document.head.removeChild(printStyles);
    }, 1000);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+P for print
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        printQRCode();
    }
    
    // Escape to go back
    if (e.key === 'Escape') {
        const backButton = document.querySelector('a[href*="patient_detail"]');
        if (backButton) {
            window.location.href = backButton.href;
        }
    }
    
    // Enter to enlarge QR code
    if (e.key === 'Enter' && e.target.closest('.qr-code-wrapper')) {
        enlargeQRCode();
    }
});

// Smooth scrolling for internal links
document.addEventListener('click', function(e) {
    if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});

// Auto-save scroll position
let scrollTimeout;
window.addEventListener('scroll', function() {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
        sessionStorage.setItem('qrCodeScrollPos', window.pageYOffset);
    }, 100);
});

// Restore scroll position
window.addEventListener('load', function() {
    const scrollPos = sessionStorage.getItem('qrCodeScrollPos');
    if (scrollPos) {
        window.scrollTo(0, parseInt(scrollPos));
        sessionStorage.removeItem('qrCodeScrollPos');
    }
});

// Add accessibility improvements
document.addEventListener('DOMContentLoaded', function() {
    // Add ARIA labels to interactive elements
    const qrWrapper = document.querySelector('.qr-code-wrapper');
    if (qrWrapper) {
        qrWrapper.setAttribute('role', 'button');
        qrWrapper.setAttribute('aria-label', 'Click to enlarge QR code');
        qrWrapper.setAttribute('tabindex', '0');
    }
    
    // Add focus styles
    const focusableElements = document.querySelectorAll('.qr-btn, .qr-code-wrapper');
    focusableElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.style.outline = '2px solid #22c55e';
            this.style.outlineOffset = '2px';
        });
        
        element.addEventListener('blur', function() {
            this.style.outline = 'none';
        });
    });
});

// Export functions for global access
window.downloadQR = downloadQR;
window.generateQRCode = generateQRCode;
window.printQRCode = printQRCode;
window.enlargeQRCode = enlargeQRCode;