/**
 * Professional Prescriptions Page JavaScript for ZimHealth-ID
 * Handles search, filtering, QR scanning, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize prescriptions page functionality
    initPrescriptionsSearch();
    initQRCodeScanner();
    initFilterFunctionality();
    initActionButtons();
    initScrollAnimations();
    // Remove initDemoData() as we're using real-time data
});

/**
 * Initialize enhanced prescriptions search functionality with real-time AJAX
 */
function initPrescriptionsSearch() {
    const searchInput = document.getElementById('prescription-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            performRealTimeFilter();
        }, 300));
    }
}

/**
 * Initialize QR Code Scanner functionality
 */
function initQRCodeScanner() {
    const qrButton = document.getElementById('qr-scanner-btn');
    const qrModal = document.getElementById('qr-modal');
    const closeQRBtn = document.getElementById('close-qr-btn');
    
    if (qrButton && qrModal) {
        qrButton.addEventListener('click', openQRScanner);
        closeQRBtn?.addEventListener('click', closeQRScanner);
        qrModal.addEventListener('click', function(e) {
            if (e.target === qrModal) closeQRScanner();
        });
    }
}

/**
 * Open QR Code Scanner
 */
async function openQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        });

        qrVideo.srcObject = stream;
        qrModal.classList.add('active');

        // Update modal content to show scanning status
        updateQRScannerStatus('Scanning for QR codes...', 'info');

        showNotification('QR Scanner activated. Point camera at patient QR code.', 'info');

        // Start QR code detection
        startQRDetection(qrVideo);

    } catch (error) {
        console.error('Error accessing camera:', error);
        showNotification('Camera access denied. Please enable camera permissions.', 'error');
    }
}

/**
 * Start QR Code Detection using jsQR library
 */
function startQRDetection(video) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let isScanning = true;

    function scanFrame() {
        if (!isScanning) return;

        const qrModal = document.getElementById('qr-modal');
        if (!qrModal || !qrModal.classList.contains('active')) {
            isScanning = false;
            return;
        }

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

            // Use jsQR library to detect QR codes
            if (typeof jsQR !== 'undefined') {
                const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });

                if (qrCode) {
                    console.log('QR Code detected:', qrCode.data);
                    updateQRScannerStatus('QR Code detected! Processing...', 'success');
                    isScanning = false;
                    handleQRCodeDetected(qrCode.data);
                    return;
                }

                // Update status to show active scanning
                updateQRScannerStatus('Scanning... Point camera at QR code', 'info');
            } else {
                // Fallback to mock detection if jsQR is not loaded
                console.warn('jsQR library not loaded, using fallback detection');
                updateQRScannerStatus('Demo mode: Simulating QR detection...', 'warning');
                const mockResult = detectQRPatternFallback();
                if (mockResult) {
                    updateQRScannerStatus('Demo QR Code detected!', 'success');
                    isScanning = false;
                    handleQRCodeDetected(mockResult);
                    return;
                }
            }
        }

        // Continue scanning
        requestAnimationFrame(scanFrame);
    }

    // Start scanning when video is ready
    video.addEventListener('loadedmetadata', () => {
        scanFrame();
    });

    // If video is already loaded, start immediately
    if (video.readyState >= video.HAVE_METADATA) {
        scanFrame();
    }

    // Stop scanning when modal is closed
    const qrModal = document.getElementById('qr-modal');
    if (qrModal) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (!qrModal.classList.contains('active')) {
                        isScanning = false;
                    }
                }
            });
        });
        observer.observe(qrModal, { attributes: true });
    }
}

/**
 * Fallback QR pattern detection for demo purposes
 */
function detectQRPatternFallback() {
    const now = Date.now();
    const scanStartTime = window.qrScanStartTime || now;

    if (!window.qrScanStartTime) {
        window.qrScanStartTime = now;
    }

    if (now - scanStartTime > 3000 && Math.random() < 0.3) {
        const mockIds = ['ZH-2025-123456', 'ZH-2025-789012', 'ZH-2025-345678'];
        window.qrScanStartTime = null;
        return mockIds[Math.floor(Math.random() * mockIds.length)];
    }

    return null;
}

/**
 * Close QR Code Scanner
 */
function closeQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');
    
    if (qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }
    
    qrModal.classList.remove('active');
}

/**
 * Handle QR Code Detection
 */
async function handleQRCodeDetected(qrData) {
    closeQRScanner();

    let zimhealthId = extractZimHealthId(qrData);

    if (!zimhealthId) {
        showNotification('Invalid QR code format. Please scan a valid patient QR code.', 'error');
        return;
    }

    showNotification('Looking up patient prescriptions...', 'info');

    try {
        const searchInput = document.getElementById('prescription-search');
        if (searchInput) {
            searchInput.value = zimhealthId;
            performRealTimeFilter();
        }

        showNotification(`QR Code scanned: ${zimhealthId}`, 'success');

    } catch (error) {
        console.error('QR lookup error:', error);
        showNotification('Error looking up prescriptions. Please try again.', 'error');
    }
}

/**
 * Extract ZimHealth ID from QR code data
 */
function extractZimHealthId(qrData) {
    if (typeof qrData === 'string') {
        if (qrData.match(/^ZH-\d{4}-\d{6}$/)) {
            return qrData;
        }

        const lines = qrData.split('\n');
        for (const line of lines) {
            if (line.startsWith('ZimHealth-ID:')) {
                const id = line.replace('ZimHealth-ID:', '').trim();
                if (id.match(/^ZH-\d{4}-\d{6}$/)) {
                    return id;
                }
            }
        }

        const match = qrData.match(/ZH-\d{4}-\d{6}/);
        if (match) {
            return match[0];
        }
    }

    return null;
}

/**
 * Update QR scanner status message
 */
function updateQRScannerStatus(message, type = 'info') {
    const statusElement = document.querySelector('#qr-modal .text-sm.text-gray-600');
    if (statusElement) {
        statusElement.textContent = message;

        statusElement.className = `text-sm ${
            type === 'success' ? 'text-green-600' :
            type === 'error' ? 'text-red-600' :
            type === 'warning' ? 'text-yellow-600' :
            'text-gray-600'
        }`;
    }
}

/**
 * Filter prescriptions based on search term
 */
function filterPrescriptions(searchTerm) {
    const prescriptions = document.querySelectorAll('.prescriptions-table tbody tr:not(.details-row)');
    let visibleCount = 0;
    
    prescriptions.forEach(prescription => {
        const text = prescription.textContent.toLowerCase();
        const isVisible = text.includes(searchTerm);
        
        // Handle details row visibility
        const nextRow = prescription.nextElementSibling;
        const isDetailsRow = nextRow && nextRow.classList.contains('details-row');
        
        if (isVisible) {
            prescription.style.display = '';
            if (isDetailsRow) nextRow.style.display = '';
            visibleCount++;
        } else {
            prescription.style.display = 'none';
            if (isDetailsRow) nextRow.style.display = 'none';
        }
    });
    
    updateResultsCount(visibleCount);
}

/**
 * Initialize filter functionality
 */
function initFilterFunctionality() {
    const statusFilter = document.getElementById('status-filter');
    const frequencyFilter = document.getElementById('frequency-filter');
    const dateFilter = document.getElementById('date-filter');
    
    if (statusFilter) statusFilter.addEventListener('change', applyFilters);
    if (frequencyFilter) frequencyFilter.addEventListener('change', applyFilters);
    if (dateFilter) dateFilter.addEventListener('change', applyFilters);
}

/**
 * Apply filters to prescriptions list
 */
function applyFilters() {
    const statusFilter = document.getElementById('status-filter')?.value || '';
    const frequencyFilter = document.getElementById('frequency-filter')?.value || '';
    const dateFilter = document.getElementById('date-filter')?.value || '';
    const searchTerm = document.getElementById('prescription-search')?.value.toLowerCase() || '';
    
    const prescriptions = document.querySelectorAll('.prescriptions-table tbody tr:not(.details-row)');
    let visibleCount = 0;
    
    prescriptions.forEach(prescription => {
        const prescriptionText = prescription.textContent.toLowerCase();
        const statusText = prescription.querySelector('.prescription-status-badge')?.textContent.toLowerCase() || '';
        const frequencyText = prescription.querySelector('.frequency-badge')?.textContent.toLowerCase() || '';
        const dateText = prescription.querySelector('.prescription-date')?.textContent || '';
        
        const matchesSearch = !searchTerm || prescriptionText.includes(searchTerm);
        const matchesStatus = !statusFilter || statusText.includes(statusFilter.toLowerCase());
        const matchesFrequency = !frequencyFilter || frequencyText.includes(frequencyFilter.toLowerCase());
        const matchesDate = !dateFilter || dateText.includes(dateFilter);
        
        const isVisible = matchesSearch && matchesStatus && matchesFrequency && matchesDate;
        
        // Handle details row visibility
        const nextRow = prescription.nextElementSibling;
        const isDetailsRow = nextRow && nextRow.classList.contains('details-row');
        
        if (isVisible) {
            prescription.style.display = '';
            if (isDetailsRow) nextRow.style.display = '';
            visibleCount++;
        } else {
            prescription.style.display = 'none';
            if (isDetailsRow) nextRow.style.display = 'none';
        }
    });
    
    updateResultsCount(visibleCount);
}

/**
 * Initialize action buttons
 */
function initActionButtons() {
    const actionButtons = document.querySelectorAll('.prescription-action-button');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Handle different actions
            const action = this.getAttribute('data-action');
            const prescriptionId = this.getAttribute('data-prescription-id');
            
            switch(action) {
                case 'view':
                    handleViewPrescription(prescriptionId);
                    break;
                case 'complete':
                    handleCompletePrescription(prescriptionId);
                    break;
                case 'hold':
                    handleHoldPrescription(prescriptionId);
                    break;
                case 'discontinue':
                    handleDiscontinuePrescription(prescriptionId);
                    break;
                case 'print':
                    handlePrintPrescription(prescriptionId);
                    break;
                case 'edit':
                    handleEditPrescription(prescriptionId);
                    break;
            }
        });
    });
}

/**
 * Handle prescription actions
 */
function handleViewPrescription(prescriptionId) {
    showNotification('Opening prescription details', 'info');
}

function handleCompletePrescription(prescriptionId) {
    showNotification('Prescription marked as completed', 'success');
}

function handleHoldPrescription(prescriptionId) {
    showNotification('Prescription put on hold', 'warning');
}

function handleDiscontinuePrescription(prescriptionId) {
    if (confirm('Are you sure you want to discontinue this prescription?')) {
        showNotification('Prescription discontinued', 'warning');
    }
}

function handlePrintPrescription(prescriptionId) {
    showNotification('Preparing prescription for printing', 'info');
}

function handleEditPrescription(prescriptionId) {
    showNotification('Opening prescription editor', 'info');
}

/**
 * Update results count display
 */
function updateResultsCount(count) {
    const countElement = document.querySelector('.results-count');
    if (countElement) {
        countElement.textContent = `${count} prescriptions`;
    }
}

/**
 * Initialize scroll animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe cards and table elements
    document.querySelectorAll('.stats-card, .prescriptions-table-card').forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

/**
 * Initialize demo data if no prescriptions exist
 */
function initDemoData() {
    const emptyState = document.querySelector('td[colspan="7"]');
    const prescriptionsTBody = document.querySelector('.prescriptions-table tbody');
    
    if (emptyState && prescriptionsTBody) {
        // Create demo prescriptions
        const demoPrescriptions = [
            {
                id: 'RX-001',
                medication: 'Metformin 500mg',
                patientName: 'Sarah Johnson',
                patientId: 'ZH-2024-001',
                dosage: '500mg',
                frequency: 'Twice Daily',
                status: 'active',
                doctor: 'Dr. Smith',
                facility: 'Central Hospital',
                startDate: 'Jul 15, 2025',
                instructions: 'Take with meals to reduce stomach upset'
            },
            {
                id: 'RX-002',
                medication: 'Lisinopril 10mg',
                patientName: 'Michael Brown',
                patientId: 'ZH-2024-002',
                dosage: '10mg',
                frequency: 'Once Daily',
                status: 'completed',
                doctor: 'Dr. Wilson',
                facility: 'Medical Center',
                startDate: 'Jul 10, 2025',
                instructions: 'Take in the morning, monitor blood pressure'
            },
            {
                id: 'RX-003',
                medication: 'Amoxicillin 250mg',
                patientName: 'Emma Wilson',
                patientId: 'ZH-2024-003',
                dosage: '250mg',
                frequency: 'Three Times Daily',
                status: 'on_hold',
                doctor: 'Dr. Martinez',
                facility: 'Health Clinic',
                startDate: 'Jul 20, 2025',
                instructions: 'Complete full course even if feeling better'
            }
        ];
        
        // Replace empty state with demo prescriptions
        prescriptionsTBody.innerHTML = demoPrescriptions.map(prescription => 
            createPrescriptionTableRow(prescription)
        ).join('');
        
        // Update results count
        updateResultsCount(demoPrescriptions.length);
        
        // Re-initialize interactions
        initActionButtons();
    }
}

/**
 * Create prescription table row HTML
 */
function createPrescriptionTableRow(prescription) {
    return `
        <tr>
            <!-- Medication Column -->
            <td class="medication-column">
                <div class="flex items-center space-x-3">
                    <div class="prescription-status-indicator ${prescription.status}"></div>
                    <div class="prescription-avatar">
                        <i class="fas fa-pills"></i>
                    </div>
                    <div>
                        <div class="text-sm font-semibold text-gray-900">${prescription.medication}</div>
                        <div class="text-xs text-gray-500">ID: ${prescription.id}</div>
                    </div>
                </div>
            </td>

            <!-- Patient Column -->
            <td class="patient-column">
                <div class="text-sm font-medium text-gray-900">${prescription.patientName}</div>
                <div class="text-xs text-gray-500">${prescription.patientId}</div>
            </td>

            <!-- Dosage Column -->
            <td class="dosage-column">
                <div class="text-sm font-medium text-gray-900">${prescription.dosage}</div>
            </td>

            <!-- Frequency Column -->
            <td class="frequency-column">
                <span class="frequency-badge">${prescription.frequency}</span>
            </td>

            <!-- Status Column -->
            <td class="status-column">
                <span class="prescription-status-badge ${prescription.status}">${prescription.status.charAt(0).toUpperCase() + prescription.status.slice(1).replace('_', ' ')}</span>
            </td>

            <!-- Doctor Column -->
            <td class="doctor-column">
                <div class="text-sm font-medium text-gray-900">${prescription.doctor}</div>
                <div class="text-xs text-gray-500">${prescription.facility}</div>
            </td>

            <!-- Actions Column -->
            <td class="actions-column">
                <div class="flex items-center justify-center space-x-1">
                    <button class="prescription-action-button view" data-action="view" data-prescription-id="${prescription.id}" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${prescription.status === 'active' ? `
                        <button class="prescription-action-button complete" data-action="complete" data-prescription-id="${prescription.id}" title="Mark Complete">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="prescription-action-button hold" data-action="hold" data-prescription-id="${prescription.id}" title="Put on Hold">
                            <i class="fas fa-clock"></i>
                        </button>
                        <button class="prescription-action-button discontinue" data-action="discontinue" data-prescription-id="${prescription.id}" title="Discontinue">
                            <i class="fas fa-ban"></i>
                        </button>
                    ` : ''}
                    <button class="prescription-action-button print" data-action="print" data-prescription-id="${prescription.id}" title="Print">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="prescription-action-button edit" data-action="edit" data-prescription-id="${prescription.id}" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </td>
        </tr>
        ${prescription.instructions ? `
            <tr class="details-row">
                <td colspan="7" class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                    <p class="text-xs text-gray-600"><strong>Instructions:</strong> ${prescription.instructions}</p>
                    <p class="text-xs text-gray-600 mt-1"><strong>Start Date:</strong> ${prescription.startDate}</p>
                </td>
            </tr>
        ` : ''}
    `;
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.prescriptions-notification');
    if (existing) existing.remove();
    
    const notification = document.createElement('div');
    const typeConfig = {
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };
    
    const config = typeConfig[type] || typeConfig.info;
    
    notification.className = `prescriptions-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID Prescriptions</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
`;
document.head.appendChild(style);

/**
 * Initialize filter functionality for all filter controls
 */
function initFilterFunctionality() {
    // Status filter
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', performRealTimeFilter);
    }

    // Frequency filter
    const frequencyFilter = document.getElementById('frequency-filter');
    if (frequencyFilter) {
        frequencyFilter.addEventListener('change', performRealTimeFilter);
    }

    // Medication type filter
    const medicationTypeFilter = document.getElementById('medication-type-filter');
    if (medicationTypeFilter) {
        medicationTypeFilter.addEventListener('change', performRealTimeFilter);
    }

    // Date range filters
    const dateFromFilter = document.getElementById('date-from');
    const dateToFilter = document.getElementById('date-to');

    if (dateFromFilter) {
        dateFromFilter.addEventListener('change', performRealTimeFilter);
    }

    if (dateToFilter) {
        dateToFilter.addEventListener('change', performRealTimeFilter);
    }

    // Initialize with current data
    performRealTimeFilter();
}

/**
 * Perform real-time filtering with AJAX
 */
function performRealTimeFilter(page = 1) {
    const searchInput = document.getElementById('prescription-search');
    const statusFilter = document.getElementById('status-filter');
    const frequencyFilter = document.getElementById('frequency-filter');
    const medicationTypeFilter = document.getElementById('medication-type-filter');
    const dateFromFilter = document.getElementById('date-from');
    const dateToFilter = document.getElementById('date-to');

    // Show loading state
    showLoadingState();

    // Collect filter parameters
    const params = new URLSearchParams({
        search: searchInput ? searchInput.value : '',
        status: statusFilter ? statusFilter.value : '',
        frequency: frequencyFilter ? frequencyFilter.value : '',
        medication_type: medicationTypeFilter ? medicationTypeFilter.value : '',
        date_from: dateFromFilter ? dateFromFilter.value : '',
        date_to: dateToFilter ? dateToFilter.value : '',
        page: page
    });

    // Remove empty parameters
    for (let [key, value] of [...params.entries()]) {
        if (!value) {
            params.delete(key);
        }
    }

    // Make AJAX request
    fetch(`/api/ajax/prescriptions/filter/?${params.toString()}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            updatePrescriptionsTable(data.prescriptions);
            updatePagination(data.pagination);
            updateResultsCount(data.pagination.total_count, data.pagination.start_index, data.pagination.end_index);
            updateStatistics(data.statistics);
            hideLoadingState();
        } else {
            throw new Error(data.error || 'Unknown error occurred');
        }
    })
    .catch(error => {
        console.error('Filter error:', error);
        showErrorState('Failed to load prescriptions. Please try again.');
        hideLoadingState();
    });
}

/**
 * Update pagination controls
 */
function updatePagination(pagination) {
    const paginationContainer = document.querySelector('.pagination-container');
    if (!paginationContainer) return;

    if (pagination.total_pages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }

    paginationContainer.style.display = 'block';

    let paginationHTML = '<div class="flex items-center justify-between">';

    // Previous button
    if (pagination.has_previous) {
        paginationHTML += `<button onclick="performRealTimeFilter(${pagination.current_page - 1})" class="pagination-btn">Previous</button>`;
    } else {
        paginationHTML += '<button class="pagination-btn disabled" disabled>Previous</button>';
    }

    // Page info
    paginationHTML += `<span class="text-sm text-gray-600">Page ${pagination.current_page} of ${pagination.total_pages}</span>`;

    // Next button
    if (pagination.has_next) {
        paginationHTML += `<button onclick="performRealTimeFilter(${pagination.current_page + 1})" class="pagination-btn">Next</button>`;
    } else {
        paginationHTML += '<button class="pagination-btn disabled" disabled>Next</button>';
    }

    paginationHTML += '</div>';
    paginationContainer.innerHTML = paginationHTML;
}

/**
 * Update results count display
 */
function updateResultsCount(totalCount, startIndex = 0, endIndex = 0) {
    const resultsCounts = document.querySelectorAll('.results-count');

    let countText;
    if (totalCount === 0) {
        countText = 'No prescriptions found';
    } else if (totalCount === 1) {
        countText = '1 prescription found';
    } else if (startIndex && endIndex) {
        countText = `Showing ${startIndex}-${endIndex} of ${totalCount} prescriptions`;
    } else {
        countText = `${totalCount} prescriptions found`;
    }

    resultsCounts.forEach(element => {
        element.textContent = countText;
    });
}

/**
 * Update statistics cards
 */
function updateStatistics(stats) {
    // Update statistics cards if they exist
    const activeCard = document.querySelector('.stats-card .stats-icon.active')?.parentElement?.nextElementSibling?.querySelector('p:last-child');
    const completedCard = document.querySelector('.stats-card .stats-icon.completed')?.parentElement?.nextElementSibling?.querySelector('p:last-child');
    const discontinuedCard = document.querySelector('.stats-card .stats-icon.discontinued')?.parentElement?.nextElementSibling?.querySelector('p:last-child');
    const expiringCard = document.querySelector('.stats-card .stats-icon.expiring')?.parentElement?.nextElementSibling?.querySelector('p:last-child');

    if (activeCard) activeCard.textContent = stats.active_count || 0;
    if (completedCard) completedCard.textContent = stats.completed_count || 0;
    if (discontinuedCard) discontinuedCard.textContent = stats.discontinued_count || 0;
    if (expiringCard) expiringCard.textContent = stats.expiring_soon_count || 0;
}

/**
 * Show loading state
 */
function showLoadingState() {
    const tbody = document.querySelector('.prescriptions-table tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mb-4"></div>
                        <p class="text-gray-500">Loading prescriptions...</p>
                    </div>
                </td>
            </tr>
        `;
    }

    // Update results count
    const resultsCounts = document.querySelectorAll('.results-count');
    resultsCounts.forEach(element => {
        element.textContent = 'Loading prescriptions...';
    });
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    // Loading state is hidden when table is updated
}

/**
 * Show error state
 */
function showErrorState(message) {
    const tbody = document.querySelector('.prescriptions-table tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="text-red-500 mb-4">
                            <i class="fas fa-exclamation-triangle text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Prescriptions</h3>
                        <p class="text-gray-500 mb-4">${message}</p>
                        <button onclick="performRealTimeFilter()" class="add-prescription-button">
                            <i class="fas fa-refresh mr-2"></i>Try Again
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
}

// Simple notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}-notification`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            ${message}
        </div>
    `;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

/**
 * Update prescriptions table with new data
 */
function updatePrescriptionsTable(prescriptions) {
    const tbody = document.querySelector('.prescriptions-table tbody');
    if (!tbody) return;

    if (prescriptions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="prescription-avatar mx-auto mb-4">
                            <i class="fas fa-pills text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No prescriptions found</h3>
                        <p class="text-gray-500 mb-4">Try adjusting your search criteria or filters.</p>
                        <a href="/api/prescriptions/new/" class="add-prescription-button">
                            <i class="fas fa-plus mr-2"></i>Add Prescription
                        </a>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const rows = prescriptions.map(prescription => {
        const statusClass = getStatusClass(prescription.status_code);
        const urgencyClass = getUrgencyClass(prescription.urgency);

        return `
            <tr data-prescription-id="${prescription.id}">
                <!-- Patient Column -->
                <td class="patient-column">
                    <div class="flex items-center space-x-3">
                        <div class="status-indicator ${statusClass}"></div>
                        <div class="prescription-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <div class="text-sm font-semibold text-gray-900">${prescription.patient_name}</div>
                            <div class="text-xs text-gray-500">${prescription.patient_zimhealth_id}</div>
                        </div>
                    </div>
                </td>

                <!-- Medication Column -->
                <td class="medication-column">
                    <div class="text-sm font-medium text-gray-900">${prescription.medication}</div>
                    <div class="text-xs text-gray-500">${prescription.dosage}</div>
                    <div class="text-xs text-blue-600">${prescription.frequency}</div>
                </td>

                <!-- Duration Column -->
                <td class="duration-column">
                    <div class="text-sm text-gray-900">${prescription.start_date}</div>
                    <div class="text-xs text-gray-500">to ${prescription.end_date}</div>
                    ${prescription.days_remaining !== null ? `
                        <div class="text-xs ${prescription.urgency === 'urgent' ? 'text-red-600' : prescription.urgency === 'warning' ? 'text-orange-600' : 'text-green-600'}">
                            ${prescription.days_remaining} days left
                        </div>
                    ` : ''}
                </td>

                <!-- Status Column -->
                <td class="status-column">
                    <span class="prescription-status-badge ${statusClass}">${prescription.status}</span>
                    ${prescription.urgency !== 'normal' ? `<div class="text-xs text-orange-600 mt-1">${prescription.urgency}</div>` : ''}
                </td>

                <!-- Prescribed By Column -->
                <td class="prescribed-by-column">
                    <div class="text-sm text-gray-900">${prescription.prescribed_by}</div>
                    <div class="text-xs text-gray-500">${prescription.created_at}</div>
                </td>

                <!-- Actions Column -->
                <td class="actions-column">
                    <div class="flex items-center space-x-2">
                        <a href="${prescription.detail_url}" class="action-button view" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="${prescription.edit_url}" class="action-button edit" title="Edit Prescription">
                            <i class="fas fa-edit"></i>
                        </a>
                        ${prescription.status_code === 'active' ? `
                            <button onclick="updatePrescriptionStatus('${prescription.id}', 'completed')" class="action-button complete" title="Mark Complete">
                                <i class="fas fa-check"></i>
                            </button>
                            <button onclick="updatePrescriptionStatus('${prescription.id}', 'discontinued')" class="action-button discontinue" title="Discontinue">
                                <i class="fas fa-stop"></i>
                            </button>
                        ` : ''}
                        <button onclick="confirmDeletePrescription('${prescription.id}', '${prescription.patient_name}', '${prescription.medication}')" class="action-button delete" title="Delete Prescription">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    tbody.innerHTML = rows;
}

// Helper functions for styling
function getStatusClass(status) {
    const statusClasses = {
        'active': 'active',
        'completed': 'completed',
        'discontinued': 'discontinued'
    };
    return statusClasses[status] || 'active';
}

function getUrgencyClass(urgency) {
    const urgencyClasses = {
        'normal': 'normal',
        'warning': 'warning',
        'urgent': 'urgent'
    };
    return urgencyClasses[urgency] || 'normal';
}

// Prescription management functions
function updatePrescriptionStatus(prescriptionId, newStatus) {
    if (!confirm(`Are you sure you want to mark this prescription as ${newStatus}?`)) {
        return;
    }

    const formData = new FormData();
    formData.append('status', newStatus);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch(`/api/prescriptions/${prescriptionId}/update-status/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            performRealTimeFilter(); // Refresh the table
        } else {
            showNotification(data.error || 'Failed to update prescription status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to update prescription status', 'error');
    });
}

function confirmDeletePrescription(prescriptionId, patientName, medication) {
    if (!confirm(`Are you sure you want to delete the prescription (${medication}) for ${patientName}? This action cannot be undone.`)) {
        return;
    }

    const formData = new FormData();
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch(`/api/prescriptions/${prescriptionId}/delete/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            performRealTimeFilter(); // Refresh the table
        } else {
            showNotification('Failed to delete prescription', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to delete prescription', 'error');
    });
}
