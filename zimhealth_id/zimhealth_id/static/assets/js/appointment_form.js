// Appointment Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Patient search functionality
    const patientSearch = document.getElementById('appointment-patient-search');
    const patientSelect = document.getElementById('appointment-patient-select');
    
    if (patientSearch && patientSelect) {
        let searchTimeout;
        
        patientSearch.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    searchPatients(query);
                }, 300);
            } else {
                // Clear options except the first one
                patientSelect.innerHTML = '<option value="">No patient selected - search or scan QR code above</option>';
            }
        });
    }
    
    function searchPatients(query) {
        fetch(`/api/ajax/search-patients/?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                patientSelect.innerHTML = '<option value="">Select a patient...</option>';
                
                data.patients.forEach(patient => {
                    const option = document.createElement('option');
                    option.value = patient.zimhealth_id;
                    option.textContent = `${patient.full_name} (${patient.zimhealth_id})`;
                    patientSelect.appendChild(option);
                });
                
                if (data.patients.length === 0) {
                    patientSelect.innerHTML = '<option value="">No patients found</option>';
                }
            })
            .catch(error => {
                console.error('Error searching patients:', error);
                patientSelect.innerHTML = '<option value="">Error searching patients</option>';
            });
    }
    
    // QR Scanner functionality
    const qrScannerBtn = document.getElementById('appointment-qr-scanner-btn');
    const qrModal = document.getElementById('appointment-qr-modal');
    const closeQrBtn = document.getElementById('appointment-close-qr-scanner');
    const qrVideo = document.getElementById('appointment-qr-video');
    
    let qrStream = null;
    let qrScanning = false;
    
    if (qrScannerBtn && qrModal) {
        qrScannerBtn.addEventListener('click', function() {
            openQrScanner();
        });
        
        closeQrBtn.addEventListener('click', function() {
            closeQrScanner();
        });
        
        // Close modal when clicking outside
        qrModal.addEventListener('click', function(e) {
            if (e.target === qrModal) {
                closeQrScanner();
            }
        });
    }
    
    function openQrScanner() {
        qrModal.classList.remove('hidden');
        qrModal.classList.add('flex');
        
        navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } })
            .then(stream => {
                qrStream = stream;
                qrVideo.srcObject = stream;
                qrVideo.play();
                qrScanning = true;
                scanQrCode();
            })
            .catch(error => {
                console.error('Error accessing camera:', error);
                alert('Unable to access camera. Please check permissions.');
                closeQrScanner();
            });
    }
    
    function closeQrScanner() {
        qrModal.classList.add('hidden');
        qrModal.classList.remove('flex');
        qrScanning = false;
        
        if (qrStream) {
            qrStream.getTracks().forEach(track => track.stop());
            qrStream = null;
        }
    }
    
    function scanQrCode() {
        if (!qrScanning || !qrVideo.videoWidth || !qrVideo.videoHeight) {
            if (qrScanning) {
                requestAnimationFrame(scanQrCode);
            }
            return;
        }
        
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = qrVideo.videoWidth;
        canvas.height = qrVideo.videoHeight;
        
        context.drawImage(qrVideo, 0, 0, canvas.width, canvas.height);
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        
        if (typeof jsQR !== 'undefined') {
            const code = jsQR(imageData.data, imageData.width, imageData.height);
            
            if (code) {
                handleQrCodeDetected(code.data);
                return;
            }
        }
        
        if (qrScanning) {
            requestAnimationFrame(scanQrCode);
        }
    }
    
    function handleQrCodeDetected(qrData) {
        closeQrScanner();
        
        // Send QR data to server for processing
        fetch('/api/ajax/scan-qr/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `qr_data=${encodeURIComponent(qrData)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Select the patient in the dropdown
                patientSelect.value = data.patient.zimhealth_id;
                patientSearch.value = data.patient.full_name;
                
                // Show success message
                showMessage('Patient selected: ' + data.patient.full_name, 'success');
            } else {
                showMessage(data.error || 'QR code not recognized', 'error');
            }
        })
        .catch(error => {
            console.error('Error processing QR code:', error);
            showMessage('Error processing QR code', 'error');
        });
    }
    
    function showMessage(message, type) {
        // Create a simple message display
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 'bg-red-500'
        }`;
        messageDiv.textContent = message;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }
    
    // Form validation
    const form = document.getElementById('appointment-registration-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const patient = patientSelect.value;
            const date = document.querySelector('input[name="date"]').value;
            const time = document.querySelector('input[name="time"]').value;
            const doctorName = document.querySelector('input[name="doctor_name"]').value;
            const facilityName = document.querySelector('input[name="facility_name"]').value;
            const reason = document.querySelector('textarea[name="reason"]').value;
            
            if (!patient) {
                e.preventDefault();
                showMessage('Please select a patient', 'error');
                return;
            }
            
            if (!date || !time || !doctorName || !facilityName || !reason) {
                e.preventDefault();
                showMessage('Please fill in all required fields', 'error');
                return;
            }
            
            // Check if appointment date is not in the past
            const appointmentDate = new Date(date + 'T' + time);
            const now = new Date();
            
            if (appointmentDate < now) {
                e.preventDefault();
                showMessage('Appointment date and time cannot be in the past', 'error');
                return;
            }
        });
    }
});