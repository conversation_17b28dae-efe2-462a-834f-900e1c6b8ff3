/**
 * Professional Patients Page JavaScript for ZimHealth-ID
 * Handles QR code scanning, search, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize patients page functionality
    initPatientsSearch();
    initQRCodeScanner();
    initTableInteractions();
    initFilterFunctionality();
    initScrollAnimations();
    // Remove initDemoData() as we're using real-time data
});

/**
 * Initialize enhanced patient search functionality with real-time AJAX
 */
function initPatientsSearch() {
    const searchInput = document.getElementById('patient-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            performRealTimeFilter();
        }, 300));
    }
}

/**
 * Initialize filter functionality for all filter controls
 */
function initFilterFunctionality() {
    // Gender filter
    const genderFilter = document.getElementById('gender-filter');
    if (genderFilter) {
        genderFilter.addEventListener('change', performRealTimeFilter);
    }

    // Blood type filter
    const bloodTypeFilter = document.getElementById('blood-type-filter');
    if (bloodTypeFilter) {
        bloodTypeFilter.addEventListener('change', performRealTimeFilter);
    }

    // Date range filters
    const dateFromFilter = document.getElementById('date-from');
    const dateToFilter = document.getElementById('date-to');

    if (dateFromFilter) {
        dateFromFilter.addEventListener('change', performRealTimeFilter);
    }

    if (dateToFilter) {
        dateToFilter.addEventListener('change', performRealTimeFilter);
    }

    // Initialize with current data
    performRealTimeFilter();
}

/**
 * Perform real-time filtering with AJAX
 */
function performRealTimeFilter(page = 1) {
    const searchInput = document.getElementById('patient-search');
    const genderFilter = document.getElementById('gender-filter');
    const bloodTypeFilter = document.getElementById('blood-type-filter');
    const dateFromFilter = document.getElementById('date-from');
    const dateToFilter = document.getElementById('date-to');

    // Show loading state
    showLoadingState();

    // Collect filter parameters
    const params = new URLSearchParams({
        search: searchInput ? searchInput.value : '',
        gender: genderFilter ? genderFilter.value : '',
        blood_type: bloodTypeFilter ? bloodTypeFilter.value : '',
        date_from: dateFromFilter ? dateFromFilter.value : '',
        date_to: dateToFilter ? dateToFilter.value : '',
        page: page
    });

    // Remove empty parameters
    for (let [key, value] of [...params.entries()]) {
        if (!value) {
            params.delete(key);
        }
    }

    // Make AJAX request
    fetch(`/api/ajax/patients/filter/?${params.toString()}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            updatePatientsTable(data.patients);
            updatePagination(data.pagination);
            updateResultsCount(data.pagination.total_count, data.pagination.start_index, data.pagination.end_index);
            hideLoadingState();
        } else {
            throw new Error(data.error || 'Unknown error occurred');
        }
    })
    .catch(error => {
        console.error('Filter error:', error);
        showErrorState('Failed to load patients. Please try again.');
        hideLoadingState();
    });
}

/**
 * Initialize QR Code Scanner functionality
 */
function initQRCodeScanner() {
    const qrButton = document.getElementById('qr-scanner-btn');
    const qrModal = document.getElementById('qr-modal');
    const closeQRBtn = document.getElementById('close-qr-btn');
    
    if (qrButton && qrModal) {
        qrButton.addEventListener('click', openQRScanner);
        closeQRBtn?.addEventListener('click', closeQRScanner);
        qrModal.addEventListener('click', function(e) {
            if (e.target === qrModal) closeQRScanner();
        });
    }
}

/**
 * Open QR Code Scanner
 */
async function openQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        });

        qrVideo.srcObject = stream;
        qrModal.classList.add('active');

        // Update modal content to show scanning status
        updateQRScannerStatus('Scanning for QR codes...', 'info');

        showNotification('QR Scanner activated. Point camera at patient QR code.', 'info');

        // Start QR code detection
        startQRDetection(qrVideo);

    } catch (error) {
        console.error('Error accessing camera:', error);
        showNotification('Camera access denied. Please enable camera permissions.', 'error');
    }
}

/**
 * Start QR Code Detection using jsQR library
 */
function startQRDetection(video) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let isScanning = true;

    function scanFrame() {
        if (!isScanning) return;

        const qrModal = document.getElementById('qr-modal');
        if (!qrModal || !qrModal.classList.contains('active')) {
            isScanning = false;
            return;
        }

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

            // Use jsQR library to detect QR codes
            if (typeof jsQR !== 'undefined') {
                const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });

                if (qrCode) {
                    console.log('QR Code detected:', qrCode.data);
                    updateQRScannerStatus('QR Code detected! Processing...', 'success');
                    isScanning = false;
                    handleQRCodeDetected(qrCode.data);
                    return;
                }

                // Update status to show active scanning
                updateQRScannerStatus('Scanning... Point camera at QR code', 'info');
            } else {
                // Fallback to mock detection if jsQR is not loaded
                console.warn('jsQR library not loaded, using fallback detection');
                updateQRScannerStatus('Demo mode: Simulating QR detection...', 'warning');
                const mockResult = detectQRPatternFallback();
                if (mockResult) {
                    updateQRScannerStatus('Demo QR Code detected!', 'success');
                    isScanning = false;
                    handleQRCodeDetected(mockResult);
                    return;
                }
            }
        }

        // Continue scanning
        requestAnimationFrame(scanFrame);
    }

    // Start scanning when video is ready
    video.addEventListener('loadedmetadata', () => {
        scanFrame();
    });

    // If video is already loaded, start immediately
    if (video.readyState >= video.HAVE_METADATA) {
        scanFrame();
    }

    // Stop scanning when modal is closed
    const qrModal = document.getElementById('qr-modal');
    if (qrModal) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (!qrModal.classList.contains('active')) {
                        isScanning = false;
                    }
                }
            });
        });
        observer.observe(qrModal, { attributes: true });
    }
}

/**
 * Fallback QR pattern detection for demo purposes
 * This is used when jsQR library is not available
 */
function detectQRPatternFallback() {
    // For demo purposes, simulate finding a QR code after a few seconds
    // In a real production environment, you would always use a proper QR library

    // Use a time-based approach to simulate detection
    const now = Date.now();
    const scanStartTime = window.qrScanStartTime || now;

    if (!window.qrScanStartTime) {
        window.qrScanStartTime = now;
    }

    // Simulate detection after 3-5 seconds
    if (now - scanStartTime > 3000 && Math.random() < 0.3) {
        // Return a mock ZimHealth ID from existing patients
        const mockIds = ['ZH-***********', 'ZH-***********', 'ZH-***********'];
        window.qrScanStartTime = null; // Reset for next scan
        return mockIds[Math.floor(Math.random() * mockIds.length)];
    }

    return null;
}

/**
 * Close QR Code Scanner
 */
function closeQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');
    
    if (qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }
    
    qrModal.classList.remove('active');
}

/**
 * Handle QR Code Detection
 */
async function handleQRCodeDetected(qrData) {
    closeQRScanner();

    // Extract ZimHealth ID from QR data
    let zimhealthId = extractZimHealthId(qrData);

    if (!zimhealthId) {
        showNotification('Invalid QR code format. Please scan a valid patient QR code.', 'error');
        return;
    }

    // Show loading notification
    showNotification('Looking up patient...', 'info');

    try {
        // Try to find patient via AJAX
        const response = await fetch(`/api/ajax/scan-qr/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `qr_data=${encodeURIComponent(zimhealthId)}`
        });

        const data = await response.json();

        if (data.success) {
            // Patient found - update search and filter to show only this patient
            const searchInput = document.getElementById('patient-search');
            if (searchInput) {
                // Set the search input to the ZimHealth ID
                searchInput.value = zimhealthId;

                // Clear other filters to ensure only search is active
                clearAllFilters();

                // Perform real-time filter to show only the scanned patient
                performRealTimeFilter();

                // Highlight the patient row after filtering
                setTimeout(() => {
                    highlightPatient(zimhealthId);
                }, 500);
            }

            showNotification(`Patient found: ${data.patient.full_name} (${zimhealthId})`, 'success');

            // Show option to view patient details
            setTimeout(() => {
                showPatientFoundActions(data.patient, data.redirect_url);
            }, 1500);

        } else {
            showNotification(data.error || 'Patient not found with this QR code.', 'error');
        }

    } catch (error) {
        console.error('QR lookup error:', error);
        showNotification('Error looking up patient. Please try again.', 'error');
    }
}

/**
 * Extract ZimHealth ID from QR code data
 */
function extractZimHealthId(qrData) {
    // Handle different QR code formats
    if (typeof qrData === 'string') {
        // Direct ZimHealth ID format: ZH-YYYY-XXXXXX
        if (qrData.match(/^ZH-\d{4}-\d{6}$/)) {
            return qrData;
        }

        // Multi-line format with ZimHealth-ID prefix
        const lines = qrData.split('\n');
        for (const line of lines) {
            if (line.startsWith('ZimHealth-ID:')) {
                const id = line.replace('ZimHealth-ID:', '').trim();
                if (id.match(/^ZH-\d{4}-\d{6}$/)) {
                    return id;
                }
            }
        }

        // Try to find ZH-YYYY-XXXXXX pattern anywhere in the string
        const match = qrData.match(/ZH-\d{4}-\d{6}/);
        if (match) {
            return match[0];
        }
    }

    return null;
}

/**
 * Clear all filter inputs except search
 */
function clearAllFilters() {
    const genderFilter = document.getElementById('gender-filter');
    const bloodTypeFilter = document.getElementById('blood-type-filter');
    const dateFromFilter = document.getElementById('date-from');
    const dateToFilter = document.getElementById('date-to');

    if (genderFilter) genderFilter.value = '';
    if (bloodTypeFilter) bloodTypeFilter.value = '';
    if (dateFromFilter) dateFromFilter.value = '';
    if (dateToFilter) dateToFilter.value = '';
}

/**
 * Show patient found actions modal
 */
function showPatientFoundActions(patient, redirectUrl) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-qrcode text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Patient Found!</h3>
                <p class="text-gray-600 mb-4">${patient.full_name}</p>
                <p class="text-sm text-gray-500 mb-6">ZimHealth ID: ${patient.zimhealth_id}</p>

                <div class="flex space-x-3">
                    <button onclick="this.closest('.fixed').remove()"
                            class="flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors">
                        Stay Here
                    </button>
                    <a href="${redirectUrl}"
                       class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center">
                        View Patient
                    </a>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (modal.parentNode) {
            modal.remove();
        }
    }, 10000);
}

/**
 * Update QR scanner status message
 */
function updateQRScannerStatus(message, type = 'info') {
    const statusElement = document.querySelector('#qr-modal .text-sm.text-gray-600');
    if (statusElement) {
        statusElement.textContent = message;

        // Update color based on type
        statusElement.className = `text-sm ${
            type === 'success' ? 'text-green-600' :
            type === 'error' ? 'text-red-600' :
            type === 'warning' ? 'text-yellow-600' :
            'text-gray-600'
        }`;
    }
}

/**
 * Get CSRF token for AJAX requests
 */
function getCsrfToken() {
    const csrfInput = document.querySelector('[name=csrfmiddlewaretoken]');
    return csrfInput ? csrfInput.value : '';
}

/**
 * Highlight found patient in table
 */
function highlightPatient(patientId) {
    const rows = document.querySelectorAll('.patients-table tbody tr');

    rows.forEach(row => {
        const idCell = row.querySelector('td:nth-child(2)');
        if (idCell && idCell.textContent.trim() === patientId) {
            // Enhanced highlighting for QR scan results
            row.style.background = 'linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.08))';
            row.style.border = '2px solid rgba(34, 197, 94, 0.5)';
            row.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.2)';
            row.style.transform = 'scale(1.02)';
            row.style.transition = 'all 0.3s ease';

            // Scroll to the highlighted patient
            row.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Add a QR scan indicator
            const qrIndicator = document.createElement('div');
            qrIndicator.className = 'qr-scan-indicator';
            qrIndicator.innerHTML = '<i class="fas fa-qrcode"></i> Scanned';
            qrIndicator.style.cssText = `
                position: absolute;
                top: -10px;
                right: 10px;
                background: #22c55e;
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 10px;
                font-weight: bold;
                z-index: 10;
                animation: pulse 2s infinite;
            `;

            row.style.position = 'relative';
            row.appendChild(qrIndicator);

            // Remove highlighting after 5 seconds
            setTimeout(() => {
                row.style.background = '';
                row.style.border = '';
                row.style.boxShadow = '';
                row.style.transform = '';
                if (qrIndicator.parentNode) {
                    qrIndicator.remove();
                }
            }, 5000);
        }
    });
}

/**
 * Initialize table interactions
 */
function initTableInteractions() {
    const actionButtons = document.querySelectorAll('.action-button');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

/**
 * Initialize filter functionality
 */
function initFilterFunctionality() {
    const genderFilter = document.getElementById('gender-filter');
    const bloodTypeFilter = document.getElementById('blood-type-filter');
    
    if (genderFilter) {
        genderFilter.addEventListener('change', applyFilters);
    }
    
    if (bloodTypeFilter) {
        bloodTypeFilter.addEventListener('change', applyFilters);
    }
}

/**
 * Apply filters to patient table
 */
function applyFilters() {
    const genderFilter = document.getElementById('gender-filter')?.value || '';
    const bloodTypeFilter = document.getElementById('blood-type-filter')?.value || '';
    const searchTerm = document.getElementById('patient-search')?.value.toLowerCase() || '';
    
    const rows = document.querySelectorAll('.patients-table tbody tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length === 0) return;
        
        const patientText = row.textContent.toLowerCase();
        const genderText = cells[3]?.textContent.toLowerCase() || '';
        const bloodTypeText = cells[4]?.textContent.toLowerCase() || '';
        
        const matchesSearch = !searchTerm || patientText.includes(searchTerm);
        const matchesGender = !genderFilter || genderText.includes(genderFilter.toLowerCase());
        const matchesBloodType = !bloodTypeFilter || bloodTypeText.includes(bloodTypeFilter.toLowerCase());
        
        const isVisible = matchesSearch && matchesGender && matchesBloodType;
        
        if (isVisible) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    updateResultsCount(visibleCount);
}

/**
 * Update results count display
 */
function updateResultsCount(count) {
    const countElement = document.querySelector('.results-count');
    if (countElement) {
        countElement.textContent = `${count} patients`;
    }
}

/**
 * Initialize scroll animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.search-filter-card, .patients-table-card').forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    const existing = document.querySelector('.patients-notification');
    if (existing) existing.remove();
    
    const notification = document.createElement('div');
    const typeConfig = {
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };
    
    const config = typeConfig[type] || typeConfig.info;
    
    notification.className = `patients-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID Patients</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Get blood type CSS class for color coding
 */
function getBloodTypeClass(bloodType) {
    const bloodTypeMap = {
        'A+': 'a-positive',
        'A-': 'a-negative',
        'B+': 'b-positive',
        'B-': 'b-negative',
        'AB+': 'ab-positive',
        'AB-': 'ab-negative',
        'O+': 'o-positive',
        'O-': 'o-negative'
    };
    return bloodTypeMap[bloodType] || 'a-positive';
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Initialize demo data if no patients exist
 */
function initDemoData() {
    const tbody = document.querySelector('.patients-table tbody');
    const emptyRow = tbody?.querySelector('td[colspan="7"]');

    if (emptyRow) {
        // Create demo patients
        const demoPatients = [
            {
                name: 'Sarah Johnson',
                nationalId: '63-123456-A12',
                zimhealthId: 'ZH-2024-001',
                phone: '+263 77 123 4567',
                address: 'Harare, Zimbabwe',
                age: '32',
                gender: 'Female',
                bloodType: 'A+',
                lastVisit: 'Jan 15, 2025'
            },
            {
                name: 'Michael Brown',
                nationalId: '63-234567-B23',
                zimhealthId: 'ZH-2024-002',
                phone: '+263 77 234 5678',
                address: 'Bulawayo, Zimbabwe',
                age: '45',
                gender: 'Male',
                bloodType: 'O+',
                lastVisit: 'Jan 12, 2025'
            },
            {
                name: 'Emma Wilson',
                nationalId: '63-345678-C34',
                zimhealthId: 'ZH-2024-003',
                phone: '+263 77 345 6789',
                address: 'Mutare, Zimbabwe',
                age: '28',
                gender: 'Female',
                bloodType: 'B+',
                lastVisit: 'Jan 10, 2025'
            },
            {
                name: 'David Martinez',
                nationalId: '63-456789-D45',
                zimhealthId: 'ZH-2024-004',
                phone: '+263 77 456 7890',
                address: 'Gweru, Zimbabwe',
                age: '38',
                gender: 'Male',
                bloodType: 'AB+',
                lastVisit: 'Jan 08, 2025'
            },
            {
                name: 'Lisa Chen',
                nationalId: '63-567890-E56',
                zimhealthId: 'ZH-2024-005',
                phone: '+263 77 567 8901',
                address: 'Masvingo, Zimbabwe',
                age: '29',
                gender: 'Female',
                bloodType: 'O-',
                lastVisit: 'Jan 05, 2025'
            }
        ];

        // Replace empty state with demo patients
        tbody.innerHTML = demoPatients.map(patient => `
            <tr>
                <td>
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="patient-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-semibold text-gray-900">${patient.name}</div>
                            <div class="text-sm text-gray-500">${patient.nationalId}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="text-sm font-mono font-semibold text-gray-900">${patient.zimhealthId}</div>
                </td>
                <td>
                    <div class="text-sm text-gray-900">${patient.phone}</div>
                    <div class="text-sm text-gray-500">${patient.address}</div>
                </td>
                <td>
                    <div class="text-sm text-gray-900">${patient.age} years</div>
                    <div class="text-sm text-gray-500">${patient.gender}</div>
                </td>
                <td>
                    <span class="blood-type-badge ${getBloodTypeClass(patient.bloodType)}">${patient.bloodType}</span>
                </td>
                <td>
                    <div class="text-sm text-gray-500">${patient.lastVisit}</div>
                </td>
                <td>
                    <div class="flex items-center space-x-2">
                        <a href="#" class="action-button view" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="#" class="action-button edit" title="Edit Patient">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="#" class="action-button medical" title="Medical Record">
                            <i class="fas fa-file-medical"></i>
                        </a>
                        <a href="#" class="action-button appointment" title="Schedule Appointment">
                            <i class="fas fa-calendar"></i>
                        </a>
                    </div>
                </td>
            </tr>
        `).join('');

        // Update results count
        updateResultsCount(demoPatients.length);

        // Re-initialize interactions
        initTableInteractions();
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }

    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
`;
document.head.appendChild(style);

/**
 * Delete Confirmation Strip Functions
 */
let currentDeleteId = null;
let currentDeleteName = null;

function showDeleteConfirmation(zimhealthId, patientName) {
    const strip = document.getElementById('deleteConfirmationStrip');
    const nameElement = document.getElementById('deletePatientNameStrip');

    // Store current patient info
    currentDeleteId = zimhealthId;
    currentDeleteName = patientName;

    // Set patient name
    nameElement.textContent = patientName;

    // Show strip
    strip.classList.remove('hidden');
    setTimeout(() => {
        strip.classList.add('show');
    }, 10);

    // Auto-hide after 10 seconds
    setTimeout(() => {
        if (strip.classList.contains('show')) {
            closeDeleteStrip();
        }
    }, 10000);
}

function confirmDeleteStrip() {
    if (!currentDeleteId) return;

    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    // Show loading state
    const deleteBtn = document.querySelector('.delete-strip-btn.delete-btn');
    const originalText = deleteBtn.innerHTML;
    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Deleting...';
    deleteBtn.disabled = true;

    // Send AJAX request
    fetch(`/api/patients/${currentDeleteId}/delete/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken,
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close strip
            closeDeleteStrip();

            // Remove patient row from table
            const patientRow = document.querySelector(`tr[data-patient-id="${currentDeleteId}"]`);
            if (patientRow) {
                patientRow.style.transition = 'all 0.3s ease';
                patientRow.style.opacity = '0';
                patientRow.style.transform = 'translateX(-100%)';
                setTimeout(() => patientRow.remove(), 300);
            }

            // Show success notification
            showSuccessNotification('Patient deleted successfully');

            // Reset state
            currentDeleteId = null;
            currentDeleteName = null;
        } else {
            showErrorNotification('Error deleting patient');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorNotification('Error deleting patient');
    })
    .finally(() => {
        // Reset button
        deleteBtn.innerHTML = originalText;
        deleteBtn.disabled = false;
    });
}

function closeDeleteStrip() {
    const strip = document.getElementById('deleteConfirmationStrip');

    strip.classList.remove('show');
    setTimeout(() => {
        strip.classList.add('hidden');
        // Reset state
        currentDeleteId = null;
        currentDeleteName = null;
    }, 300);
}

// Close strip with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const strip = document.getElementById('deleteConfirmationStrip');
        if (strip.classList.contains('show')) {
            closeDeleteStrip();
        }
    }
});

// Simple notification functions
function showSuccessNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification success-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-check-circle mr-2"></i>
            ${message}
        </div>
    `;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification error-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-exclamation-circle mr-2"></i>
            ${message}
        </div>
    `;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

/**
 * Update patients table with new data
 */
function updatePatientsTable(patients) {
    const tbody = document.querySelector('.patients-table tbody');
    if (!tbody) return;

    if (patients.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="patient-avatar mx-auto mb-4">
                            <i class="fas fa-users text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No patients found</h3>
                        <p class="text-gray-500 mb-4">Try adjusting your search criteria or filters.</p>
                        <a href="/api/patients/new/" class="add-patient-button">
                            <i class="fas fa-user-plus mr-2"></i>Add Patient
                        </a>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const rows = patients.map(patient => `
        <tr data-patient-id="${patient.zimhealth_id}">
            <td>
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="patient-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-semibold text-gray-900">${patient.full_name}</div>
                        <div class="text-sm text-gray-500">${patient.national_id || 'No ID'}</div>
                    </div>
                </div>
            </td>
            <td>
                <div class="text-sm font-mono font-semibold text-gray-900">${patient.zimhealth_id}</div>
            </td>
            <td>
                <div class="text-sm text-gray-900">${patient.phone_number}</div>
                <div class="text-sm text-gray-500">${patient.address || 'No address'}</div>
            </td>
            <td>
                <div class="text-sm text-gray-900">${patient.age} years</div>
                <div class="text-sm text-gray-500">${patient.gender}</div>
            </td>
            <td>
                ${patient.blood_type ?
                    `<span class="blood-type-badge ${patient.blood_type.toLowerCase().replace('+', '-positive').replace('-', '-negative')}">${patient.blood_type}</span>` :
                    '<span class="blood-type-badge unknown">Unknown</span>'
                }
            </td>
            <td>
                <div class="text-sm text-gray-500">
                    ${patient.last_visit || '<span class="text-gray-400">No visits</span>'}
                </div>
            </td>
            <td>
                <div class="flex items-center space-x-2">
                    <a href="${patient.detail_url}" class="action-button view" title="View Details">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="${patient.edit_url}" class="action-button edit" title="Edit Patient">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a href="${patient.medical_record_url}" class="action-button medical" title="Medical Record">
                        <i class="fas fa-file-medical"></i>
                    </a>
                    <a href="${patient.appointment_url}" class="action-button appointment" title="Schedule Appointment">
                        <i class="fas fa-calendar"></i>
                    </a>
                    <button onclick="confirmDeletePatient('${patient.zimhealth_id}', '${patient.full_name}')" class="action-button delete" title="Delete Patient">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    tbody.innerHTML = rows;
}

/**
 * Update pagination controls
 */
function updatePagination(pagination) {
    // Update pagination info if pagination controls exist
    const paginationContainer = document.querySelector('.pagination-container');
    if (!paginationContainer) return;

    if (pagination.total_pages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }

    paginationContainer.style.display = 'block';

    let paginationHTML = '<div class="flex items-center justify-between">';

    // Previous button
    if (pagination.has_previous) {
        paginationHTML += `<button onclick="performRealTimeFilter(${pagination.current_page - 1})" class="pagination-btn">Previous</button>`;
    } else {
        paginationHTML += '<button class="pagination-btn disabled" disabled>Previous</button>';
    }

    // Page info
    paginationHTML += `<span class="text-sm text-gray-600">Page ${pagination.current_page} of ${pagination.total_pages}</span>`;

    // Next button
    if (pagination.has_next) {
        paginationHTML += `<button onclick="performRealTimeFilter(${pagination.current_page + 1})" class="pagination-btn">Next</button>`;
    } else {
        paginationHTML += '<button class="pagination-btn disabled" disabled>Next</button>';
    }

    paginationHTML += '</div>';
    paginationContainer.innerHTML = paginationHTML;
}

/**
 * Update results count display
 */
function updateResultsCount(totalCount, startIndex = 0, endIndex = 0) {
    const resultsCounts = document.querySelectorAll('.results-count');

    let countText;
    if (totalCount === 0) {
        countText = 'No patients found';
    } else if (totalCount === 1) {
        countText = '1 patient found';
    } else if (startIndex && endIndex) {
        countText = `Showing ${startIndex}-${endIndex} of ${totalCount} patients`;
    } else {
        countText = `${totalCount} patients found`;
    }

    resultsCounts.forEach(element => {
        element.textContent = countText;
    });
}

/**
 * Show loading state
 */
function showLoadingState() {
    const tbody = document.querySelector('.patients-table tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                        <p class="text-gray-500">Loading patients...</p>
                    </div>
                </td>
            </tr>
        `;
    }

    // Update results count
    const resultsCounts = document.querySelectorAll('.results-count');
    resultsCounts.forEach(element => {
        element.textContent = 'Loading patients...';
    });
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    // Loading state is hidden when table is updated
}

/**
 * Show error state
 */
function showErrorState(message) {
    const tbody = document.querySelector('.patients-table tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-12">
                    <div class="flex flex-col items-center">
                        <div class="text-red-500 mb-4">
                            <i class="fas fa-exclamation-triangle text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Patients</h3>
                        <p class="text-gray-500 mb-4">${message}</p>
                        <button onclick="performRealTimeFilter()" class="add-patient-button">
                            <i class="fas fa-refresh mr-2"></i>Try Again
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
}
