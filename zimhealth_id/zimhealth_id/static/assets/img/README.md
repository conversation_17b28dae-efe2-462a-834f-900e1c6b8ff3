# Images Assets Directory

This directory contains image files for the ZimHealth-ID application.

## Purpose
- Store application logos, icons, and branding assets
- Organize user interface images and graphics
- Include background images and design elements
- Store profile pictures and user-generated content placeholders

## File Organization
```
img/
├── logo/             # Application logos and branding
├── icons/            # UI icons and symbols
├── backgrounds/      # Background images and patterns
├── placeholders/     # Default/placeholder images
├── ui/              # User interface graphics
└── uploads/         # User-generated content (if stored locally)
```

## Supported Formats
- **PNG**: For images with transparency, logos, icons
- **JPG/JPEG**: For photographs and complex images
- **SVG**: For scalable vector graphics and icons
- **WebP**: For optimized web images (when supported)
- **GIF**: For simple animations (use sparingly)

## Usage
Images in this directory are automatically served by Django's static file handling system and can be included in templates using:

```html
{% load static %}
<img src="{% static 'assets/img/filename.png' %}" alt="Description">
```

## Optimization Guidelines
- Compress images to reduce file size
- Use appropriate formats for different image types
- Provide multiple resolutions for responsive design
- Include descriptive alt text for accessibility
- Consider lazy loading for large images
- Use CSS sprites for small, frequently used icons

## Naming Conventions
- Use descriptive, lowercase filenames
- Separate words with hyphens (kebab-case)
- Include size information when relevant (e.g., `logo-200x100.png`)
- Group related images with consistent prefixes
