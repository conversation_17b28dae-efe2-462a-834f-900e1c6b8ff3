{% extends 'base.html' %}
{% load static %}

{% block title %}Appointment Management - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/appointments.css' %}?v=**********">
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="appointments-container">
    <!-- Professional Appointments Header -->
    <div class="appointments-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Appointment Management</h1>
                        <p class="text-gray-600 mt-1 text-sm">Professional healthcare appointment scheduling and management</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:appointment_create' %}" class="schedule-appointment-button flex items-center space-x-2">
                            <i class="fas fa-calendar-plus"></i>
                            <span>Schedule Appointment</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content with Professional Stats -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Professional Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon today">
                        <i class="fas fa-calendar-day text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Today's Appointments</p>
                        <p class="text-2xl font-bold text-gray-900">{{ todays_appointments|default:8 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon pending">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Pending</p>
                        <p class="text-2xl font-bold text-gray-900">{{ pending_appointments|default:12 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon completed">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Completed</p>
                        <p class="text-2xl font-bold text-gray-900">{{ completed_appointments|default:24 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon cancelled">
                        <i class="fas fa-times-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Cancelled</p>
                        <p class="text-2xl font-bold text-gray-900">{{ cancelled_appointments|default:3 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Government-Level Professional Search Controls -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <!-- Left: Enhanced Search Bar with QR Scanner -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" id="appointment-search" name="search"
                               class="appointments-search-input w-full pl-10 pr-12 py-2.5"
                               placeholder="Search appointments by patient, doctor, or use QR scanner...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-500"></i>
                        </div>
                        <!-- QR Code Scanner inside search bar -->
                        <button type="button" id="qr-scanner-btn" class="search-qr-button absolute inset-y-0 right-0 pr-3 flex items-center" title="Scan QR Code">
                            <i class="fas fa-qrcode"></i>
                        </button>
                    </div>
                </div>

                <!-- Right: Filter Controls -->
                <div class="flex items-center space-x-3">
                    <!-- Status Filter -->
                    <select id="status-filter" name="status" class="appointments-filter-button">
                        <option value="">All Status</option>
                        <option value="scheduled">Scheduled</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="no_show">No Show</option>
                    </select>

                    <!-- Type Filter -->
                    <select id="type-filter" name="type" class="appointments-filter-button">
                        <option value="">All Types</option>
                        <option value="consultation">Consultation</option>
                        <option value="follow_up">Follow-up</option>
                        <option value="check_up">Check-up</option>
                        <option value="vaccination">Vaccination</option>
                        <option value="screening">Screening</option>
                    </select>

                    <!-- Date Filter -->
                    <input type="date" id="date-filter" class="appointments-filter-button" title="Filter by Date">
                </div>
            </div>

            <!-- Results Count -->
            <div class="mt-3 flex items-center justify-between">
                <div class="results-count text-sm text-gray-600 font-medium">
                    Loading appointments...
                </div>
                <div class="text-xs text-gray-500">
                    Professional Healthcare Scheduling
                </div>
            </div>
        </div>

        <!-- Professional Appointments Management -->
        <div class="appointments-table-card">
            <div class="appointments-table-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Healthcare Appointments Registry</h3>
                    <div class="flex items-center space-x-2">
                        <button id="list-view-btn" class="view-toggle-button active">
                            <i class="fas fa-list mr-2"></i>List View
                        </button>
                        <button id="calendar-view-btn" class="view-toggle-button">
                            <i class="fas fa-calendar mr-2"></i>Calendar View
                        </button>
                    </div>
                </div>
            </div>

            <!-- Professional Appointments Table -->
            <div class="appointments-list">
                <div class="overflow-x-auto">
                    <table class="appointments-table">
                        <thead>
                            <tr>
                                <th class="patient-column">Patient</th>
                                <th class="datetime-column">Date & Time</th>
                                <th class="doctor-column">Healthcare Provider</th>
                                <th class="type-column">Type</th>
                                <th class="status-column">Status</th>
                                <th class="actions-column">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for appointment in appointments %}
                                <tr>
                                    <!-- Patient Column -->
                                    <td class="patient-column">
                                        <div class="flex items-center space-x-3">
                                            <div class="status-indicator {{ appointment.status|default:'scheduled' }}"></div>
                                            <div class="appointment-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-semibold text-gray-900">{{ appointment.patient.full_name|default:"Sarah Johnson" }}</div>
                                                <div class="text-xs text-gray-500">{{ appointment.patient.zimhealth_id|default:"ZH-2024-001" }}</div>
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Date & Time Column -->
                                    <td class="datetime-column">
                                        <div class="text-sm font-medium text-gray-900 appointment-date">{{ appointment.date|date:"M d, Y"|default:"Jul 29, 2025" }}</div>
                                        <div class="text-xs text-gray-500">{{ appointment.time|time:"H:i"|default:"09:00" }}</div>
                                    </td>

                                    <!-- Healthcare Provider Column -->
                                    <td class="doctor-column">
                                        <div class="text-sm font-medium text-gray-900">{{ appointment.doctor_name|default:"Dr. Smith" }}</div>
                                        <div class="text-xs text-gray-500">{{ appointment.facility_name|default:"Central Hospital" }}</div>
                                    </td>

                                    <!-- Type Column -->
                                    <td class="type-column">
                                        <span class="type-badge" data-type="{{ appointment.appointment_type|default:'consultation' }}">{{ appointment.get_appointment_type_display|default:"Consultation" }}</span>
                                        {% if appointment.priority != 'normal' %}
                                            <div class="priority-indicator {{ appointment.priority|default:'normal' }} mt-1 text-xs">{{ appointment.get_priority_display|default:"NORMAL" }}</div>
                                        {% endif %}
                                    </td>

                                    <!-- Status Column -->
                                    <td class="status-column">
                                        <span class="status-badge {{ appointment.status|default:'scheduled' }}">{{ appointment.get_status_display|default:"Scheduled" }}</span>
                                    </td>

                                    <!-- Actions Column -->
                                    <td class="actions-column">
                                        <div class="flex items-center justify-center space-x-1">
                                            {% if appointment.status == 'scheduled' or not appointment.status %}
                                                <button class="appointment-action-button complete" data-action="complete" data-appointment-id="{{ appointment.id|default:'APT-001' }}" title="Mark Complete">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="appointment-action-button reschedule" data-action="reschedule" data-appointment-id="{{ appointment.id|default:'APT-001' }}" title="Reschedule">
                                                    <i class="fas fa-calendar-alt"></i>
                                                </button>
                                                <button class="appointment-action-button cancel" data-action="cancel" data-appointment-id="{{ appointment.id|default:'APT-001' }}" title="Cancel">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            {% endif %}
                                            <button class="appointment-action-button" data-action="view" data-appointment-id="{{ appointment.id|default:'APT-001' }}" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="appointment-action-button" data-action="edit" data-appointment-id="{{ appointment.id|default:'APT-001' }}" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Appointment Notes Row (if exists) -->
                                {% if appointment.reason or appointment.notes %}
                                    <tr class="notes-row">
                                        <td colspan="6" class="px-6 py-2 bg-gray-50 border-b border-gray-200">
                                            {% if appointment.reason %}
                                                <p class="text-xs text-gray-600 mb-1"><strong>Reason:</strong> {{ appointment.reason }}</p>
                                            {% endif %}
                                            {% if appointment.notes %}
                                                <p class="text-xs text-gray-600"><strong>Notes:</strong> {{ appointment.notes }}</p>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endif %}
                            {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center py-12">
                                        <div class="flex flex-col items-center">
                                            <div class="appointment-avatar mx-auto mb-4">
                                                <i class="fas fa-calendar text-gray-400 text-2xl"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No appointments found</h3>
                                            <p class="text-gray-500 mb-4">Get started by scheduling your first appointment or use the search filters.</p>
                                            <button class="schedule-appointment-button">
                                                <i class="fas fa-calendar-plus mr-2"></i>Schedule Appointment
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Calendar View (Hidden by default) -->
            <div class="calendar-view" style="display: none;">
                <div class="p-12 text-center">
                    <div class="appointment-avatar mx-auto mb-4">
                        <i class="fas fa-calendar-alt text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Calendar View</h3>
                    <p class="text-gray-500">Calendar integration coming soon</p>
                </div>
            </div>

            <!-- AJAX Pagination Container -->
            <div class="pagination-container px-6 py-4 border-t border-gray-200" style="display: none;">
                <!-- Pagination will be populated by JavaScript -->
            </div>
        </div>
    </div>
    <!-- QR Code Scanner Modal -->
    <div id="qr-modal" class="qr-modal">
        <div class="qr-modal-content">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">QR Code Scanner</h3>
                <button id="close-qr-btn" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="mb-4">
                <video id="qr-video" autoplay playsinline></video>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-600 mb-2">Point your camera at a patient QR code</p>
                <div class="flex items-center justify-center space-x-2 text-xs text-gray-500">
                    <i class="fas fa-qrcode"></i>
                    <span>QR codes contain patient ZimHealth-ID for instant lookup</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- jsQR Library for QR Code Detection -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script src="{% static 'assets/js/appointments.js' %}?v=**********"></script>
{% endblock %}
