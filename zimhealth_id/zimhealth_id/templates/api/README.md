# API Templates Directory

This directory contains Django templates for API-related pages and documentation in the ZimHealth-ID application.

## Purpose
- Store HTML templates for API interfaces
- Include API response formatting templates

api/
├── profile.html            # User profile management
├── settings.html           # User settings and preferences
├── notifications.html      # Notifications and alerts page
├── reports.html            # Reports and analytics page
├── health_records.html     # Health records management
├── appointments.html       # Appointments scheduling and viewing
├── components/             # Reusable dashboard components
│   ├── sidebar.html        # Navigation sidebar
│   ├── header.html         # Dashboard header
│   ├── stats_cards.html    # Statistics cards widget
│   ├── chart_widgets.html  # Chart and graph widgets
│   └── data_tables.html    # Data table components
└── partials/              # Partial templates
├── user_info.html      # User information widget
├── quick_actions.html  # Quick action buttons
└── recent_activity.html # Recent activity feed
```
# TODO
ETC AND MORE CAN BE ADDED AS NECESSARY
