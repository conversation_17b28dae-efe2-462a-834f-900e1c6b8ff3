{% extends 'base.html' %}

{% block title %}Patient Registration Successful - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}?v=**********">
<style>
.success-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.popup-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.qr-code-container {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.patient-id-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2563eb;
    margin: 1rem 0;
    padding: 0.75rem;
    background: #eff6ff;
    border-radius: 8px;
    border: 2px solid #2563eb;
}

.print-button {
    background: #059669;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    margin: 0.5rem;
    transition: background 0.2s;
}

.print-button:hover {
    background: #047857;
}

.close-button {
    background: #6b7280;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    margin: 0.5rem;
    transition: background 0.2s;
}

.close-button:hover {
    background: #4b5563;
}

@media print {
    body * {
        visibility: hidden;
    }
    .print-area, .print-area * {
        visibility: visible;
    }
    .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Success Popup -->
    <div class="success-popup" id="successPopup">
        <div class="popup-content">
            <div class="text-center">
                <div class="text-green-600 mb-4">
                    <i class="fas fa-check-circle text-6xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Patient Registration Successful!</h2>
                <p class="text-gray-600 mb-4">{{ patient.full_name }} has been successfully registered in the system.</p>
                
                <!-- Patient ID Display -->
                <div class="patient-id-display">
                    ZimHealth ID: {{ patient.zimhealth_id }}
                </div>
                
                <!-- QR Code Display -->
                <div class="qr-code-container">
                    <h3 class="text-lg font-semibold mb-3">Patient QR Code</h3>
                    {% if patient.qr_code %}
                        <img src="{{ patient.qr_code.url }}" alt="QR Code for {{ patient.zimhealth_id }}" 
                             class="mx-auto" style="width: 200px; height: 200px;">
                    {% else %}
                        <div class="text-gray-500">QR Code not available</div>
                    {% endif %}
                </div>
                
                <!-- Print Area (Hidden, only visible when printing) -->
                <div class="print-area" style="display: none;">
                    <div style="text-align: center; padding: 20px;">
                        <h1>ZimHealth-ID Patient Card</h1>
                        <h2>{{ patient.full_name }}</h2>
                        <div style="font-size: 24px; font-weight: bold; margin: 20px 0;">
                            ID: {{ patient.zimhealth_id }}
                        </div>
                        {% if patient.qr_code %}
                            <img src="{{ patient.qr_code.url }}" alt="QR Code" style="width: 200px; height: 200px;">
                        {% endif %}
                        <div style="margin-top: 20px;">
                            <p><strong>Date of Birth:</strong> {{ patient.date_of_birth|date:"F d, Y" }}</p>
                            <p><strong>Gender:</strong> {{ patient.get_gender_display }}</p>
                            <p><strong>Blood Type:</strong> {{ patient.blood_type|default:"Not specified" }}</p>
                            <p><strong>Phone:</strong> {{ patient.phone_number }}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-6">
                    <button onclick="printQRCode()" class="print-button">
                        <i class="fas fa-print mr-2"></i>Print QR Code
                    </button>
                    <button onclick="closePopup()" class="close-button">
                        <i class="fas fa-times mr-2"></i>Close
                    </button>
                </div>
                
                <!-- Additional Actions -->
                <div class="mt-4 text-sm text-gray-600">
                    <a href="{% url 'api:patient_detail' patient.zimhealth_id %}" class="text-blue-600 hover:underline mr-4">
                        <i class="fas fa-user mr-1"></i>View Patient Profile
                    </a>
                    <a href="{% url 'api:patient_create' %}" class="text-blue-600 hover:underline">
                        <i class="fas fa-plus mr-1"></i>Add Another Patient
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printQRCode() {
    // Show print area
    document.querySelector('.print-area').style.display = 'block';
    
    // Print
    window.print();
    
    // Hide print area after printing
    setTimeout(() => {
        document.querySelector('.print-area').style.display = 'none';
    }, 1000);
}

function closePopup() {
    // Redirect to patients list
    window.location.href = "{% url 'api:patients' %}";
}

// Auto-close popup after 30 seconds
setTimeout(() => {
    closePopup();
}, 30000);

// Close popup on escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closePopup();
    }
});
</script>
{% endblock %}