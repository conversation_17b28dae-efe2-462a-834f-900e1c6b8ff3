{% extends 'base.html' %}

{% block title %}Patient Management - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}">
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="patients-container">
    <!-- Professional Patients Header -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Patient Management</h1>
                        <p class="text-gray-600 mt-1 text-sm">Comprehensive patient records and healthcare information</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:patient_create' %}" class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-user-plus"></i>
                            <span>Add New Patient</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content with QR Code -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Government-Level Professional Search Controls -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <!-- Left: Enhanced Search Bar with QR Scanner -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" id="patient-search" name="search"
                               class="government-search-input w-full pl-10 pr-12 py-2.5"
                               placeholder="Search patients by name, ID, or phone...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-500"></i>
                        </div>
                        <!-- QR Code Scanner inside search bar -->
                        <button type="button" id="qr-scanner-btn" class="search-qr-button absolute inset-y-0 right-0 pr-3 flex items-center" title="Scan QR Code">
                            <i class="fas fa-qrcode"></i>
                        </button>
                    </div>
                </div>

                <!-- Right: Filter Controls -->
                <div class="flex items-center space-x-3">
                    <!-- Gender Filter -->
                    <select id="gender-filter" name="gender" class="government-filter-button">
                        <option value="">All Genders</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                    </select>

                    <!-- Blood Type Filter -->
                    <select id="blood-type-filter" name="blood_type" class="government-filter-button">
                        <option value="">All Blood Types</option>
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                    </select>

                    <!-- Date Range Picker -->
                    <input type="date" id="date-from" class="government-filter-button" title="From Date">
                    <span class="text-gray-400">to</span>
                    <input type="date" id="date-to" class="government-filter-button" title="To Date">
                </div>
            </div>

            <!-- Results Count -->
            <div class="mt-3 flex items-center justify-between">
                <div class="results-count text-sm text-gray-600 font-medium">
                    Loading patients...
                </div>
                <div class="text-xs text-gray-500">
                    Professional Medical Database
                </div>
            </div>
        </div>

        <!-- Enhanced Patients Table -->
        <div class="patients-table-card">
            <div class="patients-table-header px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Patients Registry</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-users"></i>
                        <span class="results-count">{{ patients.count }} patients</span>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="patients-table w-full">
                    <thead>
                        <tr>
                            <th>Patient</th>
                            <th>ZimHealth ID</th>
                            <th>Contact</th>
                            <th>Age/Gender</th>
                            <th>Blood Type</th>
                            <th>Last Visit</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for patient in patients %}
                            <tr data-patient-id="{{ patient.zimhealth_id }}">
                                <td>
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="patient-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-semibold text-gray-900">{{ patient.full_name|default:"John Doe" }}</div>
                                            <div class="text-sm text-gray-500">{{ patient.national_id|default:"63-123456-A12" }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-sm font-mono font-semibold text-gray-900">{{ patient.zimhealth_id|default:"ZH-2024-001" }}</div>
                                </td>
                                <td>
                                    <div class="text-sm text-gray-900">{{ patient.phone_number|default:"+263 77 123 4567" }}</div>
                                    <div class="text-sm text-gray-500">{{ patient.address|truncatechars:30|default:"Harare, Zimbabwe" }}</div>
                                </td>
                                <td>
                                    <div class="text-sm text-gray-900">{{ patient.age|default:"32" }} years</div>
                                    <div class="text-sm text-gray-500">{{ patient.get_gender_display|default:"Male" }}</div>
                                </td>
                                <td>
                                    {% if patient.blood_type %}
                                        <span class="blood-type-badge {{ patient.blood_type|lower|slugify }}">{{ patient.blood_type }}</span>
                                    {% else %}
                                        <span class="blood-type-badge a-positive">A+</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="text-sm text-gray-500">
                                        {% if patient.last_visit %}
                                            {{ patient.last_visit|date:"M d, Y" }}
                                        {% else %}
                                            <span class="text-gray-400">Jan 15, 2025</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center space-x-2">
                                        <a href="{% url 'api:patient_detail' patient.zimhealth_id %}" class="action-button view" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'api:patient_edit' patient.zimhealth_id %}" class="action-button edit" title="Edit Patient">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'api:medical_record_create_for_patient' patient.zimhealth_id %}" class="action-button medical" title="Medical Record">
                                            <i class="fas fa-file-medical"></i>
                                        </a>
                                        <a href="{% url 'api:appointment_create_for_patient' patient.zimhealth_id %}" class="action-button appointment" title="Schedule Appointment">
                                            <i class="fas fa-calendar"></i>
                                        </a>
                                        <button onclick="confirmDeletePatient('{{ patient.zimhealth_id }}', '{{ patient.full_name }}')" class="action-button delete" title="Delete Patient">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="7" class="text-center py-12">
                                    <div class="flex flex-col items-center">
                                        <div class="patient-avatar mx-auto mb-4">
                                            <i class="fas fa-users text-gray-400 text-2xl"></i>
                                        </div>
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No patients found</h3>
                                        <p class="text-gray-500 mb-4">Get started by adding your first patient or use the search filters.</p>
                                        <a href="{% url 'api:patient_create' %}" class="add-patient-button">
                                            <i class="fas fa-user-plus mr-2"></i>Add Patient
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- AJAX Pagination Container -->
            <div class="pagination-container px-6 py-4 border-t border-gray-200" style="display: none;">
                <!-- Pagination will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Strip - Themed with Patients Page -->
    <div id="deleteConfirmationStrip" class="delete-confirmation-strip hidden">
        <div class="delete-strip-container">
            <div class="delete-strip-content">
                <div class="delete-strip-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="delete-strip-text">
                    <h4 class="delete-strip-title">Delete Patient</h4>
                    <p class="delete-strip-message">
                        Are you sure you want to delete <span id="deletePatientNameStrip" class="font-semibold"></span>?
                        <br><span class="text-xs opacity-75">This action cannot be undone.</span>
                    </p>
                </div>
                <div class="delete-strip-actions">
                    <button onclick="confirmDeleteStrip()" class="delete-strip-btn delete-btn">
                        <i class="fas fa-trash mr-1"></i>Delete
                    </button>
                    <button onclick="closeDeleteStrip()" class="delete-strip-btn cancel-btn">
                        <i class="fas fa-times mr-1"></i>Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- QR Code Scanner Modal -->
    <div id="qr-modal" class="qr-modal">
        <div class="qr-modal-content">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">QR Code Scanner</h3>
                <button id="close-qr-btn" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="mb-4">
                <video id="qr-video" autoplay playsinline></video>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-600 mb-2">Point your camera at a patient QR code</p>
                <div class="flex items-center justify-center space-x-2 text-xs text-gray-500">
                    <i class="fas fa-qrcode"></i>
                    <span>QR codes contain patient ZimHealth-ID for instant lookup</span>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<!-- jsQR Library for QR Code Detection -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script src="{% static 'assets/js/patients.js' %}"></script>
<script>
// Simple delete confirmation function
function confirmDeletePatient(zimhealthId, patientName) {
    // Show the confirmation strip
    const strip = document.getElementById('deleteConfirmationStrip');
    const nameElement = document.getElementById('deletePatientNameStrip');

    if (!strip || !nameElement) {
        // Fallback to simple confirm dialog
        if (confirm('Are you sure you want to delete patient ' + patientName + '? This action cannot be undone.')) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/api/patients/' + zimhealthId + '/delete/';

            // Add CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);

            document.body.appendChild(form);
            form.submit();
        }
        return;
    }

    // Store current patient info
    window.currentDeleteId = zimhealthId;
    window.currentDeleteName = patientName;

    // Set patient name
    nameElement.textContent = patientName;

    // Show strip
    strip.classList.remove('hidden');
    setTimeout(() => {
        strip.classList.add('show');
    }, 10);

    // Auto-hide after 10 seconds
    setTimeout(() => {
        if (strip.classList.contains('show')) {
            closeDeleteStrip();
        }
    }, 10000);
}

function confirmDeleteStrip() {
    if (!window.currentDeleteId) return;

    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    // Show loading state
    const deleteBtn = document.querySelector('.delete-strip-btn.delete-btn');
    const originalText = deleteBtn.innerHTML;
    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Deleting...';
    deleteBtn.disabled = true;

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/api/patients/' + window.currentDeleteId + '/delete/';

    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrfmiddlewaretoken';
    csrfInput.value = csrfToken;
    form.appendChild(csrfInput);

    document.body.appendChild(form);
    form.submit();
}

function closeDeleteStrip() {
    const strip = document.getElementById('deleteConfirmationStrip');
    if (strip) {
        strip.classList.remove('show');
        setTimeout(() => {
            strip.classList.add('hidden');
            // Reset state
            window.currentDeleteId = null;
            window.currentDeleteName = null;
        }, 300);
    }
}

// Close strip with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const strip = document.getElementById('deleteConfirmationStrip');
        if (strip && strip.classList.contains('show')) {
            closeDeleteStrip();
        }
    }
});
</script>
{% endblock %}
{% endblock %}
