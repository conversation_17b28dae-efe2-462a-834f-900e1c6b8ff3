{% extends 'base.html' %}

{% block title %}Prescription Details - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}">
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Patients Header - EXACT Mirror of Patients Page -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ prescription.medication }}</h1>
                        <p class="text-gray-600 mt-1 text-sm">Prescription details for {{ prescription.medical_record.patient.full_name }}</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:prescriptions' %}" class="add-patient-button flex items-center space-x-2 bg-gray-600 hover:bg-gray-700">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back to Prescriptions</span>
                        </a>
                        <a href="{% url 'api:prescription_edit' prescription_id=prescription.id %}" class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-edit"></i>
                            <span>Edit Prescription</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Prescription Detail Layout - Matching Patient Detail Structure -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Top Section: Prescription ID and Status - Patients Page Theme -->
        <div class="mb-6 flex items-center justify-between">
            <div class="government-filter-button">
                <i class="fas fa-pills mr-2"></i>Prescription ID: #{{ prescription.id|slice:":8" }}
            </div>
            <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    {% if prescription.status == 'active' %}bg-green-100 text-green-800
                    {% elif prescription.status == 'completed' %}bg-blue-100 text-blue-800
                    {% elif prescription.status == 'discontinued' %}bg-red-100 text-red-800
                    {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                    {{ prescription.get_status_display }}
                </span>
                <button onclick="printPrescription()" class="government-filter-button">
                    <i class="fas fa-print mr-2"></i>Print
                </button>
            </div>
        </div>

        <!-- Main Layout: Prescription Info (Left) and Actions/Timeline (Right) -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column: Prescription Information -->
            <div class="lg:col-span-1">
                <div class="patients-table-card h-full">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-center">
                            <h3 class="text-lg font-semibold text-gray-900">Prescription Info</h3>
                        </div>
                    </div>

                    <div class="p-6">
                        <!-- Medication Icon -->
                        <div class="flex justify-center mb-6">
                            <div class="w-24 h-24 rounded-full bg-purple-600 flex items-center justify-center">
                                <i class="fas fa-pills text-white text-2xl"></i>
                            </div>
                        </div>

                        <!-- Prescription Details -->
                        <div class="space-y-4">
                            <div class="text-center">
                                <h4 class="text-lg font-bold text-gray-900">{{ prescription.medication }}</h4>
                                <p class="text-sm text-gray-600">{{ prescription.dosage }} • {{ prescription.frequency }}</p>
                            </div>

                            <div class="border-t pt-4 space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">Patient</span>
                                    <span class="text-sm text-gray-900">{{ prescription.medical_record.patient.full_name }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">Patient ID</span>
                                    <span class="text-sm text-gray-900 font-mono">{{ prescription.medical_record.patient.zimhealth_id }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">Duration</span>
                                    <span class="text-sm text-gray-900">{{ prescription.duration|default:"As needed" }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">Start Date</span>
                                    <span class="text-sm text-gray-900">{{ prescription.start_date|date:"M d, Y" }}</span>
                                </div>
                                {% if prescription.end_date %}
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">End Date</span>
                                    <span class="text-sm text-gray-900">{{ prescription.end_date|date:"M d, Y" }}</span>
                                </div>
                                {% endif %}
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">Status</span>
                                    <span class="text-sm text-gray-900">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            {% if prescription.status == 'active' %}bg-green-100 text-green-800
                                            {% elif prescription.status == 'completed' %}bg-blue-100 text-blue-800
                                            {% elif prescription.status == 'discontinued' %}bg-red-100 text-red-800
                                            {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                            {{ prescription.get_status_display }}
                                        </span>
                                    </span>
                                </div>
                            </div>

                            <div class="border-t pt-4">
                                <span class="text-xs font-semibold text-gray-600 uppercase block mb-2">Prescribed By</span>
                                <p class="text-sm text-gray-900">{{ prescription.prescribed_by.get_full_name|default:prescription.prescribed_by.username }}</p>
                                <p class="text-xs text-gray-600">{{ prescription.medical_record.facility_name }}</p>
                            </div>

                            {% if prescription.instructions %}
                            <div class="border-t pt-4">
                                <span class="text-xs font-semibold text-gray-600 uppercase block mb-2">Instructions</span>
                                <p class="text-sm text-gray-900">{{ prescription.instructions }}</p>
                            </div>
                            {% endif %}

                            {% if prescription.side_effects %}
                            <div class="border-t pt-4">
                                <span class="text-xs font-semibold text-gray-600 uppercase block mb-2">Side Effects</span>
                                <p class="text-sm text-red-600">{{ prescription.side_effects }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column: Quick Actions and Timeline -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Quick Actions Section -->
                <div class="patients-table-card mb-6">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-bolt"></i>
                                <span>Available Actions</span>
                            </div>
                        </div>
                    </div>

                    <div class="p-6" style="min-height: 200px;">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="border-2 border-gray-200 p-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 border border-blue-300 flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-bold text-gray-900 uppercase">Patient Profile</p>
                                        <a href="{% url 'api:patient_detail' prescription.medical_record.patient.zimhealth_id %}" class="text-xs font-semibold text-blue-600 uppercase hover:text-blue-800">View Patient Details</a>
                                    </div>
                                </div>
                            </div>

                            <div class="border-2 border-gray-200 p-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-green-100 border border-green-300 flex items-center justify-center">
                                        <i class="fas fa-file-medical text-green-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-bold text-gray-900 uppercase">Medical Record</p>
                                        <a href="{% url 'api:medical_record_detail' prescription.medical_record.id %}" class="text-xs font-semibold text-green-600 uppercase hover:text-green-800">View Full Record</a>
                                    </div>
                                </div>
                            </div>

                            <div class="border-2 border-gray-200 p-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-purple-100 border border-purple-300 flex items-center justify-center">
                                        <i class="fas fa-pills text-purple-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-bold text-gray-900 uppercase">Add Prescription</p>
                                        <a href="{% url 'api:prescription_create_for_record' prescription.medical_record.id %}" class="text-xs font-semibold text-purple-600 uppercase hover:text-purple-800">New Prescription</a>
                                    </div>
                                </div>
                            </div>

                            <div class="border-2 border-gray-200 p-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gray-100 border border-gray-300 flex items-center justify-center">
                                        <i class="fas fa-print text-gray-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-bold text-gray-900 uppercase">Print Prescription</p>
                                        <button onclick="printPrescription()" class="text-xs font-semibold text-gray-600 uppercase hover:text-gray-800">Generate Print</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Management Section -->
                {% if prescription.status == 'active' %}
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Status Management</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-cog"></i>
                                <span>Update Status</span>
                            </div>
                        </div>
                    </div>

                    <div class="p-6" style="min-height: 150px;">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="border-2 border-gray-200 p-4">
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-green-100 border border-green-300 flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-check text-green-600"></i>
                                    </div>
                                    <p class="text-sm font-bold text-gray-900 uppercase mb-2">Complete</p>
                                    <button onclick="updatePrescriptionStatus('completed')" class="text-xs font-semibold text-green-600 uppercase hover:text-green-800 border border-green-300 px-3 py-1 rounded">Mark Complete</button>
                                </div>
                            </div>

                            <div class="border-2 border-gray-200 p-4">
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-yellow-100 border border-yellow-300 flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-pause text-yellow-600"></i>
                                    </div>
                                    <p class="text-sm font-bold text-gray-900 uppercase mb-2">Hold</p>
                                    <button onclick="updatePrescriptionStatus('on_hold')" class="text-xs font-semibold text-yellow-600 uppercase hover:text-yellow-800 border border-yellow-300 px-3 py-1 rounded">Put On Hold</button>
                                </div>
                            </div>

                            <div class="border-2 border-gray-200 p-4">
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-red-100 border border-red-300 flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-stop text-red-600"></i>
                                    </div>
                                    <p class="text-sm font-bold text-gray-900 uppercase mb-2">Discontinue</p>
                                    <button onclick="updatePrescriptionStatus('discontinued')" class="text-xs font-semibold text-red-600 uppercase hover:text-red-800 border border-red-300 px-3 py-1 rounded">Discontinue</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Main Action Buttons - Patients Page Theme -->
        <div class="mt-8 flex items-center justify-between">
            <div class="results-count text-sm text-gray-600 font-medium">
                Prescription management actions for {{ prescription.medication }}
            </div>
            <div class="flex items-center space-x-4">
                <a href="{% url 'api:prescription_create_for_record' prescription.medical_record.id %}" class="add-patient-button flex items-center space-x-2">
                    <i class="fas fa-pills"></i>
                    <span>Add Another Prescription</span>
                </a>
                <a href="{% url 'api:prescriptions' %}" class="add-patient-button flex items-center space-x-2 bg-gray-600 hover:bg-gray-700">
                    <i class="fas fa-list"></i>
                    <span>All Prescriptions</span>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function updatePrescriptionStatus(status) {
    if (confirm(`Are you sure you want to mark this prescription as ${status}?`)) {
        const formData = new FormData();
        formData.append('status', status);
        
        fetch(`{% url 'api:update_prescription_status_ajax' prescription_id=prescription.id %}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating status: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the status');
        });
    }
}

function printPrescription() {
    // Hide action buttons for printing
    const elementsToHide = document.querySelectorAll('.government-filter-button, .add-patient-button, .results-count');
    const originalDisplay = [];
    
    elementsToHide.forEach((element, index) => {
        originalDisplay[index] = element.style.display;
        element.style.display = 'none';
    });
    
    // Print
    window.print();
    
    // Restore elements after printing
    setTimeout(() => {
        elementsToHide.forEach((element, index) => {
            element.style.display = originalDisplay[index];
        });
    }, 1000);
}

// Initialize prescription detail page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Ensure proper layout on load
    adjustLayoutForSinglePage();
    
    // Handle window resize
    window.addEventListener('resize', adjustLayoutForSinglePage);
});

function adjustLayoutForSinglePage() {
    // Ensure the layout fits within viewport
    const container = document.querySelector('.prescription-detail-container');
    if (container) {
        const viewportHeight = window.innerHeight;
        const headerHeight = document.querySelector('.prescriptions-header')?.offsetHeight || 0;
        const availableHeight = viewportHeight - headerHeight;
        
        const contentArea = container.querySelector('.max-w-7xl');
        if (contentArea) {
            contentArea.style.maxHeight = `${availableHeight}px`;
        }
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+P for print
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        printPrescription();
    }
    
    // Escape to go back
    if (e.key === 'Escape') {
        const backButton = document.querySelector('a[href*="prescriptions"]');
        if (backButton) {
            window.location.href = backButton.href;
        }
    }
});
</script>
{% endblock %}

{% block extra_js %}
<script src="{% static 'assets/js/prescription_detail.js' %}"></script>
{% endblock %}