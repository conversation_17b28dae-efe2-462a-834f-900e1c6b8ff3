{% extends 'base.html' %}
{% load static %}

{% block title %}{% if record %}Edit Medical Record{% else %}New Medical Record{% endif %} - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %">
<style>
/* Compact styling for Medical Record Form - Same as Edit Patient */
.patients-table-card .patients-table-header {
    padding: 8px 16px !important; /* Reduced header padding */
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0; /* Don't shrink header */
}

.patients-table td {
    padding: 4px 8px !important; /* Substantially reduced cell padding */
    vertical-align: middle;
    line-height: 1.2 !important;
}

/* Compact input styling for Medical Record */
.government-search-input {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 0;
    font-size: 0.8rem;
    color: #374151;
    padding: 8px 12px !important; /* Substantially reduced padding */
    line-height: 1.2 !important;
    min-height: 32px !important; /* Much smaller height */
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.government-search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Compact textarea styling */
textarea.government-search-input {
    padding: 8px 12px !important;
    line-height: 1.3 !important;
    min-height: 60px !important; /* Much smaller textarea */
    resize: none;
}

/* Mobile responsive for compact inputs */
@media (max-width: 768px) {
    .government-search-input {
        font-size: 0.75rem !important;
        padding: 6px 10px !important;
        min-height: 28px !important;
    }

    textarea.government-search-input {
        padding: 6px 10px !important;
        min-height: 50px !important;
    }

    .patients-table td {
        padding: 2px 4px !important;
    }
}

/* Compact card spacing */
.patients-table-card {
    margin-bottom: 1rem !important; /* Reduced margin between cards */
}

/* Compact form sections */
.max-w-7xl.mx-auto.px-4.sm\:px-6.lg\:px-8.py-8 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}
</style>
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Patients Header - Compact Version -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-4"> <!-- Reduced from py-6 to py-4 -->
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-xl font-bold text-gray-900"> <!-- Reduced from text-2xl to text-xl -->
                            {% if record %}Edit Medical Record{% else %}New Medical Record{% endif %}
                        </h1>
                        <p class="text-gray-600 mt-1 text-xs"> <!-- Reduced from text-sm to text-xs -->
                            {% if patient %}Medical consultation record for {{ patient.full_name }}{% else %}Create a new medical consultation record{% endif %}
                        </p>
                    </div>
                    <div class="flex items-center space-x-3"> <!-- Reduced from space-x-4 to space-x-3 -->
                        <a href="{% url 'api:medical_records' %}" class="government-filter-button">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Records
                        </a>
                        <div class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-file-medical"></i>
                            <span>Medical Record</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content - Compact Version -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 relative z-10"> <!-- Reduced from py-8 to py-4 -->
        <form method="post">
            {% csrf_token %}

            <!-- Patient Selection - Mirror of Search Section -->
            {% if not patient %}
            <div class="mb-4"> <!-- Reduced from mb-6 to mb-4 -->
                <div class="flex items-center justify-between">
                    <div class="flex-1 max-w-md">
                        <div class="relative">
                            <input type="text" id="patient-search"
                                   class="government-search-input w-full pl-10 pr-4"
                                   placeholder="Search patients by name, ID, or phone...">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-500"></i>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <!-- QR Code Scanner Button -->
                        <button type="button" id="qr-scanner-btn" class="government-filter-button flex items-center space-x-2" title="Scan Patient QR Code">
                            <i class="fas fa-qrcode"></i>
                            <span>Scan QR</span>
                        </button>
                        <select name="patient" required class="government-filter-button min-w-0 flex-1">
                            <option value="">Select Patient</option>
                            {% for p in patients %}
                                <option value="{{ p.zimhealth_id }}">{{ p.full_name }} ({{ p.zimhealth_id }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- Form Status - Compact Version -->
                <div class="mt-2 flex items-center justify-between"> <!-- Reduced from mt-3 to mt-2 -->
                    <div class="results-count text-xs text-gray-600 font-medium"> <!-- Reduced from text-sm to text-xs -->
                        Medical Record Form • Select patient to continue
                    </div>
                    <div class="text-xs text-gray-500">
                        Professional Medical Database
                    </div>
                </div>
            </div>
            {% else %}
            <!-- Patient Info Display - Compact Horizontal Format -->
            <div class="mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex-1 max-w-lg"> <!-- Reduced from max-w-2xl to max-w-lg -->
                        <div class="relative">
                            <div class="government-search-input w-full pl-10 pr-4 bg-gray-50 border-gray-300 text-sm font-medium">
                                <i class="fas fa-user text-gray-500 mr-2"></i>{{ patient.full_name|title }} ({{ patient.zimhealth_id }})
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-2 flex items-center justify-between">
                    <div class="results-count text-xs text-gray-600 font-medium"> <!-- Reduced from text-sm to text-xs -->
                        Medical Record • {{ patient.full_name|title }} • Age: {{ patient.age }} • {{ patient.get_gender_display }}
                    </div>
                    <div class="text-xs text-gray-500">
                        Professional Database
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Visit Information Table - Compact Version -->
            <div class="patients-table-card mb-4"> <!-- Reduced from mb-6 to mb-4 -->
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Visit Information</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-hospital"></i>
                            <span>Medical Consultation</span>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 20%;">
                            <col style="width: 30%;">
                            <col style="width: 20%;">
                            <col style="width: 30%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Visit Date & Time</td>
                                <td>
                                    <input type="datetime-local" name="date"
                                           value="{{ form.date.value|default:'' }}"
                                           class="government-search-input w-full"
                                           id="visit-datetime" required>
                                </td>
                                <td class="font-semibold text-gray-700">Healthcare Facility</td>
                                <td>
                                    <input type="text" name="facility_name"
                                           value="{{ form.facility_name.value|default:'Harare Central Hospital' }}"
                                           class="government-search-input w-full"
                                           placeholder="e.g., Harare Central Hospital" required>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Attending Doctor</td>
                                <td>
                                    <input type="text" name="doctor_name"
                                           value="{{ form.doctor_name.value|default:user.get_full_name }}"
                                           class="government-search-input w-full"
                                           placeholder="Dr. {{ user.get_full_name|default:'Medical Professional' }}" required>
                                </td>
                                <td class="font-semibold text-gray-700">Department</td>
                                <td>
                                    <input type="text" name="department"
                                           value="{{ form.department.value|default:'General Medicine' }}"
                                           class="government-search-input w-full"
                                           placeholder="e.g., General Medicine">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Vital Signs Table - Compact Version -->
            <div class="patients-table-card mb-4"> <!-- Reduced from mb-6 to mb-4 -->
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Vital Signs</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-heartbeat"></i>
                            <span>Patient Vitals</span>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 25%;">
                            <col style="width: 25%;">
                            <col style="width: 25%;">
                            <col style="width: 25%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Temperature (°C)</td>
                                <td>
                                    <input type="number" step="0.1" name="temperature"
                                           value="{{ form.temperature.value|default:'' }}"
                                           class="government-search-input w-full"
                                           placeholder="36.5">
                                </td>
                                <td class="font-semibold text-gray-700">Heart Rate (bpm)</td>
                                <td>
                                    <input type="number" name="heart_rate"
                                           value="{{ form.heart_rate.value|default:'' }}"
                                           class="government-search-input w-full"
                                           placeholder="72">
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Weight (kg)</td>
                                <td>
                                    <input type="number" step="0.1" name="weight"
                                           value="{{ form.weight.value|default:'' }}"
                                           class="government-search-input w-full"
                                           placeholder="70.0">
                                </td>
                                <td class="font-semibold text-gray-700">Height (cm)</td>
                                <td>
                                    <input type="number" name="height"
                                           value="{{ form.height.value|default:'' }}"
                                           class="government-search-input w-full"
                                           placeholder="170">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Medical Details Table - Compact Version -->
            <div class="patients-table-card mb-4"> <!-- Reduced from mb-6 to mb-4 -->
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Medical Details</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-file-medical"></i>
                            <span>Clinical Information</span>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 20%;">
                            <col style="width: 80%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Diagnosis</td>
                                <td>
                                    <textarea name="diagnosis" rows="3" required
                                              class="government-search-input w-full resize-none"
                                              placeholder="Primary diagnosis and any secondary diagnoses">{{ form.diagnosis.value|default:'' }}</textarea>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Treatment</td>
                                <td>
                                    <textarea name="treatment" rows="3" required
                                              class="government-search-input w-full resize-none"
                                              placeholder="Treatment provided or recommended">{{ form.treatment.value|default:'' }}</textarea>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Additional Notes</td>
                                <td>
                                    <textarea name="notes" rows="2"
                                              class="government-search-input w-full resize-none"
                                              placeholder="Any additional observations or notes">{{ form.notes.value|default:'' }}</textarea>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Submit Section - Mirror of Patients Page Actions -->
            <div class="flex items-center justify-between">
                <div class="results-count text-sm text-gray-600 font-medium">
                    Complete all required fields to save medical record
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'api:medical_records' %}" class="government-filter-button">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                    <button type="submit" class="add-patient-button flex items-center space-x-2">
                        <i class="fas fa-save"></i>
                        <span>Save Medical Record</span>
                    </button>
                    <button type="submit" name="save_and_add_prescription" class="add-patient-button flex items-center space-x-2" style="background: linear-gradient(135deg, #22c55e, #16a34a); border-color: #15803d;">
                        <i class="fas fa-pills"></i>
                        <span>Save & Add Prescription</span>
                    </button>
                </div>
        </form>
    </div>
</div>

<!-- QR Code Scanner Modal - ZimHealth-ID Design Language -->
<div id="qr-modal" class="qr-scanner-overlay">
    <div class="qr-scanner-modal">
        <div class="qr-modal-header">
            <div class="qr-header-content">
                <div class="qr-success-indicator">
                    <div class="qr-icon-container">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div>
                        <h3 class="qr-modal-title">Scan Patient QR Code</h3>
                        <p class="qr-modal-subtitle">Position QR code within camera view</p>
                    </div>
                </div>
            </div>
            <button type="button" id="close-qr-btn" class="qr-close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="qr-modal-body">
            <div class="qr-video-container">
                <video id="qr-video" autoplay playsinline></video>
                <div class="qr-overlay">
                    <div class="qr-scanner-frame"></div>
                </div>
            </div>
            <div class="qr-status" id="qr-status">
                <i class="fas fa-qrcode"></i>
                <span>Position QR code within the frame</span>
            </div>
        </div>
    </div>
</div>

<style>
/* QR Scanner Modal - ZimHealth-ID Design Language */
.qr-scanner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.qr-scanner-overlay.active {
    display: flex;
    opacity: 1;
    visibility: visible;
}

/* Main Modal Container - Matching patient success modal */
.qr-scanner-modal {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    transform: scale(0.95) translateY(20px);
    transition: transform 0.3s ease;
    position: relative;
}

.qr-scanner-overlay.active .qr-scanner-modal {
    transform: scale(1) translateY(0);
}

/* Header Section - Matching established pattern */
.qr-modal-header {
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    padding: 1rem 1.5rem;
    position: relative;
}

.qr-header-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-success-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
}

.qr-icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05));
    border: 1px solid rgba(14, 165, 233, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0ea5e9;
    font-size: 20px;
}

.qr-modal-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
    line-height: 1.2;
}

.qr-modal-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    margin-top: 2px;
}

.qr-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: #ffffff;
    border: 1px solid #d1d5db;
    color: #6b7280;
    width: 32px;
    height: 32px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.qr-close-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

/* Modal Body */
.qr-modal-body {
    padding: 1.5rem;
}

/* Video Container - Professional styling */
.qr-video-container {
    position: relative;
    width: 100%;
    height: 320px;
    background: #000000;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1rem;
    border: 2px solid #e5e7eb;
}

#qr-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.qr-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.qr-scanner-frame {
    width: 220px;
    height: 220px;
    border: 3px solid #22c55e;
    border-radius: 12px;
    position: relative;
    animation: qrPulse 2s infinite;
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.qr-scanner-frame::before,
.qr-scanner-frame::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    border: 4px solid #22c55e;
    border-radius: 4px;
}

.qr-scanner-frame::before {
    top: -4px;
    left: -4px;
    border-right: none;
    border-bottom: none;
}

.qr-scanner-frame::after {
    bottom: -4px;
    right: -4px;
    border-left: none;
    border-top: none;
}

/* Status Display - Matching design language */
.qr-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 16px;
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.qr-status.success {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    border-color: #86efac;
    color: #166534;
}

.qr-status.error {
    background: linear-gradient(135deg, #fef2f2, #fecaca);
    border-color: #fca5a5;
    color: #dc2626;
}

.qr-status.warning {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border-color: #fcd34d;
    color: #d97706;
}

.qr-status.info {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-color: #93c5fd;
    color: #2563eb;
}

@keyframes qrPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.02);
        box-shadow: 0 0 30px rgba(34, 197, 94, 0.5);
    }
}

/* Button Styling Enhancements for Medical Record Form */
.government-filter-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    font-weight: 500;
}

.government-filter-button:hover {
    border-color: #9ca3af;
    background: #f9fafb;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.government-filter-button:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

/* Enhanced Add Patient Button for Medical Records */
.add-patient-button {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border: 1px solid #1e3a8a;
    border-radius: 6px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.add-patient-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.add-patient-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-patient-button:hover::before {
    left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .qr-scanner-modal {
        max-width: 95%;
        margin: 1rem;
    }

    .qr-video-container {
        height: 280px;
    }

    .qr-scanner-frame {
        width: 180px;
        height: 180px;
    }

    .qr-modal-header {
        padding: 1rem;
    }

    .qr-modal-body {
        padding: 1rem;
    }

    .government-filter-button {
        min-width: 100px;
        font-size: 0.75rem;
        padding: 0.5rem;
    }

    .add-patient-button {
        padding: 0.625rem 1.25rem;
        font-size: 0.8rem;
    }
}
</style>

<script>
// Auto-populate visit date and time with current date/time
document.addEventListener('DOMContentLoaded', function() {
    const visitDateTimeInput = document.getElementById('visit-datetime');
    if (visitDateTimeInput && !visitDateTimeInput.value) {
        const now = new Date();
        // Format for datetime-local input (YYYY-MM-DDTHH:MM)
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        visitDateTimeInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
    }
});

// Patient search functionality
document.getElementById('patient-search')?.addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const select = document.querySelector('select[name="patient"]');
    const options = select.querySelectorAll('option');

    options.forEach(option => {
        if (option.value === '') return; // Skip the default option
        const text = option.textContent.toLowerCase();
        option.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// QR Code Scanner Functionality - Using working implementation from patients page
document.getElementById('qr-scanner-btn')?.addEventListener('click', function() {
    openQRScanner();
});

document.getElementById('close-qr-btn')?.addEventListener('click', function() {
    closeQRScanner();
});

// Close modal when clicking outside
document.getElementById('qr-modal')?.addEventListener('click', function(e) {
    if (e.target === this) closeQRScanner();
});

// Open QR Code Scanner - Using working implementation
async function openQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        });

        qrVideo.srcObject = stream;
        qrModal.classList.add('active');

        // Update status
        updateQRScannerStatus('Scanning for QR codes...', 'info');
        showNotification('QR Scanner activated. Point camera at patient QR code.', 'info');

        // Start QR code detection
        startQRDetection(qrVideo);

    } catch (error) {
        console.error('Error accessing camera:', error);
        showNotification('Camera access denied. Please enable camera permissions.', 'error');
    }
}

// Start QR Code Detection using jsQR library - Working implementation
function startQRDetection(video) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let isScanning = true;

    function scanFrame() {
        if (!isScanning) return;

        const qrModal = document.getElementById('qr-modal');
        if (!qrModal || !qrModal.classList.contains('active')) {
            isScanning = false;
            return;
        }

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

            // Use jsQR library to detect QR codes
            if (typeof jsQR !== 'undefined') {
                const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });

                if (qrCode) {
                    console.log('QR Code detected:', qrCode.data);
                    updateQRScannerStatus('QR Code detected! Processing...', 'success');
                    isScanning = false;
                    handleQRCodeDetected(qrCode.data);
                    return;
                }

                // Update status to show active scanning
                updateQRScannerStatus('Scanning... Point camera at QR code', 'info');
            } else {
                // Fallback to mock detection if jsQR is not loaded
                console.warn('jsQR library not loaded, using fallback detection');
                updateQRScannerStatus('Demo mode: Simulating QR detection...', 'warning');
                const mockResult = detectQRPatternFallback();
                if (mockResult) {
                    updateQRScannerStatus('Demo QR Code detected!', 'success');
                    isScanning = false;
                    handleQRCodeDetected(mockResult);
                    return;
                }
            }
        }

        // Continue scanning
        requestAnimationFrame(scanFrame);
    }

    // Start scanning when video is ready
    video.addEventListener('loadedmetadata', () => {
        scanFrame();
    });

    // If video is already loaded, start immediately
    if (video.readyState >= video.HAVE_METADATA) {
        scanFrame();
    }

    // Stop scanning when modal is closed
    const qrModal = document.getElementById('qr-modal');
    if (qrModal) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (!qrModal.classList.contains('active')) {
                        isScanning = false;
                    }
                }
            });
        });
        observer.observe(qrModal, { attributes: true });
    }
}

// Close QR Code Scanner
function closeQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');

    if (qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }

    qrModal.classList.remove('active');
}

// Fallback QR pattern detection for demo purposes
function detectQRPatternFallback() {
    // For demo purposes, simulate finding a QR code after a few seconds
    const now = Date.now();
    const scanStartTime = window.qrScanStartTime || now;

    if (!window.qrScanStartTime) {
        window.qrScanStartTime = now;
    }

    // Simulate detection after 3-5 seconds
    if (now - scanStartTime > 3000 && Math.random() < 0.3) {
        // Return a mock ZimHealth ID from existing patients
        const mockIds = ['ZH-***********', 'ZH-***********', 'ZH-***********'];
        window.qrScanStartTime = null; // Reset for next scan
        return mockIds[Math.floor(Math.random() * mockIds.length)];
    }

    return null;
}

// Update QR Scanner Status
function updateQRScannerStatus(message, type) {
    const statusElement = document.getElementById('qr-status');
    if (statusElement) {
        const icon = type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-triangle' :
                    type === 'warning' ? 'fa-exclamation-circle' : 'fa-qrcode';

        statusElement.innerHTML = `<i class="fas ${icon}"></i><span>${message}</span>`;

        // Update color based on type
        statusElement.className = `qr-status ${type}`;
    }
}



async function handleQRCodeDetected(qrData) {
    closeQRScanner();

    // Debug: Log the raw QR data
    console.log('Raw QR Data:', qrData);

    // Extract ZimHealth ID from QR data
    let zimhealthId = extractZimHealthId(qrData);

    console.log('Extracted ZimHealth ID:', zimhealthId);

    if (!zimhealthId) {
        showNotification(`Invalid QR code format. Scanned: "${qrData.substring(0, 50)}..."`, 'error');
        return;
    }

    showNotification('Looking up patient...', 'info');

    try {
        // Send the full QR data to the server for processing
        const response = await fetch(`/api/ajax/scan-qr/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `qr_data=${encodeURIComponent(qrData)}`
        });

        const data = await response.json();

        console.log('Server response:', data);

        if (data.success) {
            // Select the patient in the dropdown
            const patientSelect = document.querySelector('select[name="patient"]');
            const patientSearch = document.getElementById('patient-search');

            if (patientSelect && patientSearch) {
                patientSelect.value = data.patient.zimhealth_id;
                patientSearch.value = data.patient.full_name;

                // Trigger change event to update any dependent fields
                patientSelect.dispatchEvent(new Event('change'));

                showNotification(`✅ Patient selected: ${data.patient.full_name}`, 'success');
            } else {
                showNotification('Error: Could not find patient selection fields', 'error');
            }
        } else {
            showNotification(`❌ ${data.error || 'Patient not found'}`, 'error');
        }

    } catch (error) {
        console.error('QR lookup error:', error);
        showNotification('❌ Error looking up patient. Please try again.', 'error');
    }
}

function extractZimHealthId(qrData) {
    // Handle different QR code formats
    if (typeof qrData === 'string') {
        // Direct ZimHealth ID format
        if (qrData.match(/^ZH-\d{4}-\d{6}$/)) {
            return qrData;
        }

        // Multi-line format (as generated by the system)
        // Format: "ZimHealth-ID: ZH-YYYY-XXXXXX\nName: ...\nDOB: ..."
        const lines = qrData.split('\n');
        for (const line of lines) {
            if (line.startsWith('ZimHealth-ID:')) {
                const id = line.replace('ZimHealth-ID:', '').trim();
                if (id.match(/^ZH-\d{4}-\d{6}$/)) {
                    return id;
                }
            }
        }

        // JSON format
        try {
            const parsed = JSON.parse(qrData);
            return parsed.zimhealth_id || parsed.id;
        } catch (e) {
            // Not JSON, try to extract ID pattern anywhere in the string
            const match = qrData.match(/ZH-\d{4}-\d{6}/);
            return match ? match[0] : null;
        }
    }

    return null;
}

function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-xl max-w-sm transform transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white border-l-4 border-green-700' :
        type === 'error' ? 'bg-red-500 text-white border-l-4 border-red-700' :
        type === 'warning' ? 'bg-yellow-500 text-white border-l-4 border-yellow-700' :
        'bg-blue-500 text-white border-l-4 border-blue-700'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${
                type === 'success' ? 'check-circle' :
                type === 'error' ? 'exclamation-triangle' :
                type === 'warning' ? 'exclamation-circle' :
                'info-circle'
            } mr-3 text-lg"></i>
            <span class="font-medium">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);

    // Remove after 5 seconds (longer for error messages)
    const timeout = type === 'error' ? 7000 : 4000;
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }, timeout);
}
</script>

<!-- jsQR Library for QR Code Detection -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
{% endblock %}
