{% extends 'base.html' %}
{% load static %}

{% block title %}{% if record %}Edit Medical Record{% else %}New Medical Record{% endif %} - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}">
<style>
/* Compact styling for Medical Record Form - Same as Edit Patient */
.patients-table-card .patients-table-header {
    padding: 8px 16px !important; /* Reduced header padding */
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0; /* Don't shrink header */
}

.patients-table td {
    padding: 4px 8px !important; /* Substantially reduced cell padding */
    vertical-align: middle;
    line-height: 1.2 !important;
}

/* Compact input styling for Medical Record */
.government-search-input {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 0;
    font-size: 0.8rem;
    color: #374151;
    padding: 8px 12px !important; /* Substantially reduced padding */
    line-height: 1.2 !important;
    min-height: 32px !important; /* Much smaller height */
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.government-search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Compact textarea styling */
textarea.government-search-input {
    padding: 8px 12px !important;
    line-height: 1.3 !important;
    min-height: 60px !important; /* Much smaller textarea */
    resize: none;
}

/* Mobile responsive for compact inputs */
@media (max-width: 768px) {
    .government-search-input {
        font-size: 0.75rem !important;
        padding: 6px 10px !important;
        min-height: 28px !important;
    }

    textarea.government-search-input {
        padding: 6px 10px !important;
        min-height: 50px !important;
    }

    .patients-table td {
        padding: 2px 4px !important;
    }
}

/* Compact card spacing */
.patients-table-card {
    margin-bottom: 1rem !important; /* Reduced margin between cards */
}

/* Compact form sections */
.max-w-7xl.mx-auto.px-4.sm\:px-6.lg\:px-8.py-8 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}
</style>
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Patients Header - Compact Version -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-4"> <!-- Reduced from py-6 to py-4 -->
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-xl font-bold text-gray-900"> <!-- Reduced from text-2xl to text-xl -->
                            {% if record %}Edit Medical Record{% else %}New Medical Record{% endif %}
                        </h1>
                        <p class="text-gray-600 mt-1 text-xs"> <!-- Reduced from text-sm to text-xs -->
                            {% if patient %}Medical consultation record for {{ patient.full_name }}{% else %}Create a new medical consultation record{% endif %}
                        </p>
                    </div>
                    <div class="flex items-center space-x-3"> <!-- Reduced from space-x-4 to space-x-3 -->
                        <a href="{% url 'api:medical_records' %}" class="government-filter-button">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Records
                        </a>
                        <div class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-file-medical"></i>
                            <span>Medical Record</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content - Compact Version -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 relative z-10"> <!-- Reduced from py-8 to py-4 -->
        <form method="post">
            {% csrf_token %}

            <!-- Patient Selection - Mirror of Search Section -->
            {% if not patient %}
            <div class="mb-4"> <!-- Reduced from mb-6 to mb-4 -->
                <div class="flex items-center justify-between">
                    <div class="flex-1 max-w-md">
                        <div class="relative">
                            <input type="text" id="patient-search"
                                   class="government-search-input w-full pl-10 pr-4"
                                   placeholder="Search patients by name, ID, or phone...">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-500"></i>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- QR Code Scanner Button -->
                        <button type="button" id="qr-scanner-btn" class="government-filter-button flex items-center space-x-2" title="Scan Patient QR Code">
                            <i class="fas fa-qrcode"></i>
                            <span>Scan QR</span>
                        </button>
                        <select name="patient" required class="government-filter-button">
                            <option value="">Select Patient</option>
                            {% for p in patients %}
                                <option value="{{ p.zimhealth_id }}">{{ p.full_name }} ({{ p.zimhealth_id }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- Form Status - Compact Version -->
                <div class="mt-2 flex items-center justify-between"> <!-- Reduced from mt-3 to mt-2 -->
                    <div class="results-count text-xs text-gray-600 font-medium"> <!-- Reduced from text-sm to text-xs -->
                        Medical Record Form • Select patient to continue
                    </div>
                    <div class="text-xs text-gray-500">
                        Professional Medical Database
                    </div>
                </div>
            </div>
            {% else %}
            <!-- Patient Info Display - Compact Horizontal Format -->
            <div class="mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex-1 max-w-lg"> <!-- Reduced from max-w-2xl to max-w-lg -->
                        <div class="relative">
                            <div class="government-search-input w-full pl-10 pr-4 bg-gray-50 border-gray-300 text-sm font-medium">
                                <i class="fas fa-user text-gray-500 mr-2"></i>{{ patient.full_name|title }} ({{ patient.zimhealth_id }})
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-2 flex items-center justify-between">
                    <div class="results-count text-xs text-gray-600 font-medium"> <!-- Reduced from text-sm to text-xs -->
                        Medical Record • {{ patient.full_name|title }} • Age: {{ patient.age }} • {{ patient.get_gender_display }}
                    </div>
                    <div class="text-xs text-gray-500">
                        Professional Database
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Visit Information Table - Compact Version -->
            <div class="patients-table-card mb-4"> <!-- Reduced from mb-6 to mb-4 -->
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Visit Information</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-hospital"></i>
                            <span>Medical Consultation</span>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 20%;">
                            <col style="width: 30%;">
                            <col style="width: 20%;">
                            <col style="width: 30%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Visit Date & Time</td>
                                <td>
                                    <input type="datetime-local" name="date"
                                           value="{{ form.date.value|default:'' }}"
                                           class="government-search-input w-full"
                                           id="visit-datetime" required>
                                </td>
                                <td class="font-semibold text-gray-700">Healthcare Facility</td>
                                <td>
                                    <input type="text" name="facility_name"
                                           value="{{ form.facility_name.value|default:'Harare Central Hospital' }}"
                                           class="government-search-input w-full"
                                           placeholder="e.g., Harare Central Hospital" required>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Attending Doctor</td>
                                <td>
                                    <input type="text" name="doctor_name"
                                           value="{{ form.doctor_name.value|default:user.get_full_name }}"
                                           class="government-search-input w-full"
                                           placeholder="Dr. {{ user.get_full_name|default:'Medical Professional' }}" required>
                                </td>
                                <td class="font-semibold text-gray-700">Department</td>
                                <td>
                                    <input type="text" name="department"
                                           value="{{ form.department.value|default:'General Medicine' }}"
                                           class="government-search-input w-full"
                                           placeholder="e.g., General Medicine">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Vital Signs Table - Compact Version -->
            <div class="patients-table-card mb-4"> <!-- Reduced from mb-6 to mb-4 -->
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Vital Signs</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-heartbeat"></i>
                            <span>Patient Vitals</span>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 25%;">
                            <col style="width: 25%;">
                            <col style="width: 25%;">
                            <col style="width: 25%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Temperature (°C)</td>
                                <td>
                                    <input type="number" step="0.1" name="temperature"
                                           value="{{ form.temperature.value|default:'' }}"
                                           class="government-search-input w-full"
                                           placeholder="36.5">
                                </td>
                                <td class="font-semibold text-gray-700">Heart Rate (bpm)</td>
                                <td>
                                    <input type="number" name="heart_rate"
                                           value="{{ form.heart_rate.value|default:'' }}"
                                           class="government-search-input w-full"
                                           placeholder="72">
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Weight (kg)</td>
                                <td>
                                    <input type="number" step="0.1" name="weight"
                                           value="{{ form.weight.value|default:'' }}"
                                           class="government-search-input w-full"
                                           placeholder="70.0">
                                </td>
                                <td class="font-semibold text-gray-700">Height (cm)</td>
                                <td>
                                    <input type="number" name="height"
                                           value="{{ form.height.value|default:'' }}"
                                           class="government-search-input w-full"
                                           placeholder="170">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Medical Details Table - Compact Version -->
            <div class="patients-table-card mb-4"> <!-- Reduced from mb-6 to mb-4 -->
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Medical Details</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-file-medical"></i>
                            <span>Clinical Information</span>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 20%;">
                            <col style="width: 80%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Diagnosis</td>
                                <td>
                                    <textarea name="diagnosis" rows="3" required
                                              class="government-search-input w-full resize-none"
                                              placeholder="Primary diagnosis and any secondary diagnoses">{{ form.diagnosis.value|default:'' }}</textarea>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Treatment</td>
                                <td>
                                    <textarea name="treatment" rows="3" required
                                              class="government-search-input w-full resize-none"
                                              placeholder="Treatment provided or recommended">{{ form.treatment.value|default:'' }}</textarea>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Additional Notes</td>
                                <td>
                                    <textarea name="notes" rows="2"
                                              class="government-search-input w-full resize-none"
                                              placeholder="Any additional observations or notes">{{ form.notes.value|default:'' }}</textarea>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Submit Section - Mirror of Patients Page Actions -->
            <div class="flex items-center justify-between">
                <div class="results-count text-sm text-gray-600 font-medium">
                    Complete all required fields to save medical record
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'api:medical_records' %}" class="government-filter-button">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                    <button type="submit" class="add-patient-button flex items-center space-x-2">
                        <i class="fas fa-save"></i>
                        <span>Save Medical Record</span>
                    </button>
                    <button type="submit" name="save_and_add_prescription" class="add-patient-button flex items-center space-x-2 bg-green-600 hover:bg-green-700">
                        <i class="fas fa-pills"></i>
                        <span>Save & Add Prescription</span>
                    </button>
                </div>
        </form>
    </div>
</div>

<!-- QR Code Scanner Modal -->
<div id="qr-scanner-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Scan Patient QR Code</h3>
                    <button type="button" id="close-qr-scanner" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="text-center">
                    <div id="qr-scanner-container" class="mb-4">
                        <video id="qr-video" class="w-full h-64 bg-gray-100 rounded-lg"></video>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Position the QR code within the camera view</p>
                    <div class="flex space-x-3">
                        <button type="button" id="start-qr-scan" class="flex-1 bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700">
                            <i class="fas fa-camera mr-2"></i>Start Scanning
                        </button>
                        <button type="button" id="stop-qr-scan" class="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                            <i class="fas fa-stop mr-2"></i>Stop
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-populate visit date and time with current date/time
document.addEventListener('DOMContentLoaded', function() {
    const visitDateTimeInput = document.getElementById('visit-datetime');
    if (visitDateTimeInput && !visitDateTimeInput.value) {
        const now = new Date();
        // Format for datetime-local input (YYYY-MM-DDTHH:MM)
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        visitDateTimeInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
    }
});

// Patient search functionality
document.getElementById('patient-search')?.addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const select = document.querySelector('select[name="patient"]');
    const options = select.querySelectorAll('option');

    options.forEach(option => {
        if (option.value === '') return; // Skip the default option
        const text = option.textContent.toLowerCase();
        option.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// QR Code Scanner Functionality
let qrStream = null;
let qrScanner = null;

// QR Scanner Modal Controls
document.getElementById('qr-scanner-btn')?.addEventListener('click', function() {
    document.getElementById('qr-scanner-modal').classList.remove('hidden');
});

document.getElementById('close-qr-scanner')?.addEventListener('click', function() {
    closeQRScanner();
});

document.getElementById('start-qr-scan')?.addEventListener('click', function() {
    startQRScanning();
});

document.getElementById('stop-qr-scan')?.addEventListener('click', function() {
    stopQRScanning();
});

function closeQRScanner() {
    stopQRScanning();
    document.getElementById('qr-scanner-modal').classList.add('hidden');
}

async function startQRScanning() {
    try {
        const video = document.getElementById('qr-video');

        // Check if camera is available
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            showNotification('Camera not available on this device', 'error');
            return;
        }

        // Get camera stream
        qrStream = await navigator.mediaDevices.getUserMedia({
            video: { facingMode: 'environment' } // Use back camera if available
        });

        video.srcObject = qrStream;
        video.play();

        // Import QR scanner library dynamically
        if (!window.QrScanner) {
            // For demo purposes, we'll simulate QR scanning
            simulateQRScanning();
        } else {
            // Use actual QR scanner library if available
            qrScanner = new QrScanner(video, result => handleQRCodeDetected(result));
            qrScanner.start();
        }

        showNotification('QR Scanner started. Position QR code in view.', 'info');

    } catch (error) {
        console.error('Error starting QR scanner:', error);
        showNotification('Failed to start camera. Please check permissions.', 'error');
    }
}

function stopQRScanning() {
    if (qrStream) {
        qrStream.getTracks().forEach(track => track.stop());
        qrStream = null;
    }

    if (qrScanner) {
        qrScanner.stop();
        qrScanner = null;
    }

    const video = document.getElementById('qr-video');
    video.srcObject = null;
}

function simulateQRScanning() {
    // Simulate QR code detection for demo purposes
    setTimeout(() => {
        // Simulate detecting a patient QR code
        const demoPatientId = 'ZH-***********'; // Use existing patient ID
        handleQRCodeDetected(demoPatientId);
    }, 3000);
}

async function handleQRCodeDetected(qrData) {
    closeQRScanner();

    // Extract ZimHealth ID from QR data
    let zimhealthId = extractZimHealthId(qrData);

    if (!zimhealthId) {
        showNotification('Invalid QR code format. Please scan a valid patient QR code.', 'error');
        return;
    }

    showNotification('Looking up patient...', 'info');

    try {
        // Find patient via AJAX
        const response = await fetch(`/api/ajax/scan-qr/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `qr_data=${encodeURIComponent(zimhealthId)}`
        });

        const data = await response.json();

        if (data.success) {
            // Select the patient in the dropdown
            const patientSelect = document.querySelector('select[name="patient"]');
            const patientSearch = document.getElementById('patient-search');

            patientSelect.value = data.patient.zimhealth_id;
            patientSearch.value = data.patient.full_name;

            showNotification(`Patient selected: ${data.patient.full_name}`, 'success');
        } else {
            showNotification(data.error || 'Patient not found', 'error');
        }

    } catch (error) {
        console.error('QR lookup error:', error);
        showNotification('Error looking up patient. Please try again.', 'error');
    }
}

function extractZimHealthId(qrData) {
    // Handle different QR code formats
    if (typeof qrData === 'string') {
        // Direct ZimHealth ID
        if (qrData.match(/^ZH-\d{4}-\d{6}$/)) {
            return qrData;
        }

        // JSON format
        try {
            const parsed = JSON.parse(qrData);
            return parsed.zimhealth_id || parsed.id;
        } catch (e) {
            // Not JSON, try to extract ID pattern
            const match = qrData.match(/ZH-\d{4}-\d{6}/);
            return match ? match[0] : null;
        }
    }

    return null;
}

function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
