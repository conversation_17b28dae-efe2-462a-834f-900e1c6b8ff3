{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Patient - {{ patient.full_name }}{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}">
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Patients Header -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:patients' %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Delete Patient</h1>
                            <p class="text-gray-600 mt-1 text-sm">Confirm patient record deletion</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="add-patient-button flex items-center space-x-2 bg-red-600 hover:bg-red-700">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Delete Confirmation</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <div class="patients-card max-w-2xl mx-auto">
            <div class="patients-card-header">
                <h4 class="patients-card-title text-red-700">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>Confirm Patient Deletion
                </h4>
                <p class="patients-card-subtitle">This action cannot be undone</p>
            </div>
            
            <div class="p-6">
                <!-- Patient Information -->
                <div class="bg-red-50 border-2 border-red-200 rounded-md p-4 mb-6">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-user text-red-600 text-xl mr-3"></i>
                        <div>
                            <h5 class="font-bold text-red-800">{{ patient.full_name }}</h5>
                            <p class="text-red-700 text-sm">ZimHealth ID: {{ patient.zimhealth_id }}</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="text-red-700"><strong>Phone:</strong> {{ patient.phone_number }}</p>
                            <p class="text-red-700"><strong>Gender:</strong> {{ patient.get_gender_display }}</p>
                        </div>
                        <div>
                            <p class="text-red-700"><strong>Blood Type:</strong> {{ patient.blood_type|default:"Not specified" }}</p>
                            <p class="text-red-700"><strong>Registration:</strong> {{ patient.registration_date|date:"M d, Y" }}</p>
                        </div>
                    </div>
                </div>

                <!-- Warning Message -->
                <div class="bg-yellow-50 border-2 border-yellow-200 rounded-md p-4 mb-6">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-xl mr-3 mt-1"></i>
                        <div>
                            <h6 class="font-bold text-yellow-800 mb-2">Warning: Permanent Deletion</h6>
                            <ul class="text-yellow-700 text-sm space-y-1">
                                <li>• This will permanently delete the patient record</li>
                                <li>• All associated medical records will be affected</li>
                                <li>• All appointment history will be affected</li>
                                <li>• This action cannot be undone</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Confirmation Form -->
                <form method="post" class="space-y-4">
                    {% csrf_token %}
                    
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button type="submit" 
                                class="flex-1 bg-red-600 text-white py-3 px-6 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-trash"></i>
                            <span>Yes, Delete Patient</span>
                        </button>
                        
                        <a href="{% url 'api:patients' %}" 
                           class="flex-1 bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-times"></i>
                            <span>Cancel</span>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
