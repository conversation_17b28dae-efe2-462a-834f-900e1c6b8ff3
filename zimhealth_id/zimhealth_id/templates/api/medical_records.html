{% extends 'base.html' %}
{% load static %}

{% block title %}Medical Records Management - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/medical_records.css' %}?v=**********">
{% endblock %}

{% block content %}
<div class="medical-records-container">
    <!-- Professional Medical Records Header -->
    <div class="medical-records-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Medical Records Management</h1>
                        <p class="text-gray-600 mt-1 text-sm">Professional healthcare documentation and medical history management</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:medical_record_create' %}" class="new-medical-record-button flex items-center space-x-2">
                            <i class="fas fa-plus"></i>
                            <span>New Medical Record</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content with Professional Stats -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Professional Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon total">
                        <i class="fas fa-file-medical text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Records</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_records|default:156 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon recent">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Recent (7 days)</p>
                        <p class="text-2xl font-bold text-gray-900">{{ recent_records|default:18 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon critical">
                        <i class="fas fa-exclamation-triangle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Critical Cases</p>
                        <p class="text-2xl font-bold text-gray-900">{{ critical_records|default:4 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon pending">
                        <i class="fas fa-hourglass-half text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Pending Review</p>
                        <p class="text-2xl font-bold text-gray-900">{{ pending_records|default:7 }}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Government-Level Professional Search Controls -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <!-- Left: Enhanced Search Bar with QR Scanner -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" id="medical-record-search" name="search"
                               class="medical-records-search-input w-full pl-10 pr-12 py-2.5"
                               placeholder="Search records by patient, diagnosis, or use QR scanner...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-500"></i>
                        </div>
                        <!-- QR Code Scanner inside search bar -->
                        <button type="button" id="qr-scanner-btn" class="search-qr-button absolute inset-y-0 right-0 pr-3 flex items-center" title="Scan QR Code">
                            <i class="fas fa-qrcode"></i>
                        </button>
                    </div>
                </div>

                <!-- Right: Filter Controls -->
                <div class="flex items-center space-x-3">
                    <!-- Record Type Filter -->
                    <select id="type-filter" name="type" class="medical-records-filter-button">
                        <option value="">All Types</option>
                        <option value="consultation">Consultation</option>
                        <option value="emergency">Emergency</option>
                        <option value="follow_up">Follow-up</option>
                        <option value="surgery">Surgery</option>
                        <option value="screening">Screening</option>
                    </select>

                    <!-- Status Filter -->
                    <select id="status-filter" name="status" class="medical-records-filter-button">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="pending">Pending</option>
                        <option value="archived">Archived</option>
                        <option value="critical">Critical</option>
                    </select>

                    <!-- Facility Filter -->
                    <select id="facility-filter" name="facility" class="medical-records-filter-button">
                        <option value="">All Facilities</option>
                        <option value="central">Central Hospital</option>
                        <option value="medical">Medical Center</option>
                        <option value="clinic">Health Clinic</option>
                    </select>

                    <!-- Date Range Filters -->
                    <input type="date" id="date-from-filter" class="medical-records-filter-button" title="From Date">
                    <span class="text-gray-400">to</span>
                    <input type="date" id="date-to-filter" class="medical-records-filter-button" title="To Date">
                </div>
            </div>

            <!-- Results Count -->
            <div class="mt-3 flex items-center justify-between">
                <div class="results-count text-sm text-gray-600 font-medium">
                    Loading medical records...
                </div>
                <div class="text-xs text-gray-500">
                    Professional Medical Documentation System
                </div>
            </div>
        </div>

        <!-- Professional Medical Records Management -->
        <div class="medical-records-table-card">
            <div class="medical-records-table-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Healthcare Medical Records Registry</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span>{{ medical_records.count|default:"3" }} records</span>
                    </div>
                </div>
            </div>

            <!-- Professional Medical Records Table -->
            <div class="medical-records-list">
                <div class="overflow-x-auto" style="max-width: 100%; overflow-x: auto;">
                    <table class="medical-records-table" style="width: 100%; min-width: 1000px;">
                        <colgroup>
                            <col style="width: 20%;">  <!-- Patient -->
                            <col style="width: 15%;">  <!-- Date & Time -->
                            <col style="width: 15%;">  <!-- Healthcare Provider -->
                            <col style="width: 15%;">  <!-- Record Type -->
                            <col style="width: 20%;">  <!-- Diagnosis -->
                            <col style="width: 8%;">   <!-- Status -->
                            <col style="width: 7%;">   <!-- Actions -->
                        </colgroup>
                        <thead>
                            <tr>
                                <th class="patient-column px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Patient</th>
                                <th class="date-column px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Date & Time</th>
                                <th class="provider-column px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Healthcare Provider</th>
                                <th class="type-column px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Record Type</th>
                                <th class="diagnosis-column px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Diagnosis</th>
                                <th class="status-column px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Status</th>
                                <th class="actions-column px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in medical_records %}
                                <tr class="hover:bg-gray-50" data-patient-id="{{ record.patient.zimhealth_id }}">
                                    <!-- Patient Column -->
                                    <td class="patient-column px-4 py-3 border-b align-middle">
                                        <div class="flex items-center space-x-2">
                                            <div class="record-status-indicator {{ record.status|default:'active' }}"></div>
                                            <div class="medical-record-avatar flex-shrink-0">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <div class="text-sm font-semibold text-gray-900">{{ record.patient.full_name }}</div>
                                                <div class="text-xs text-gray-500 truncate">{{ record.patient.zimhealth_id }}</div>
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Date & Time Column -->
                                    <td class="date-column px-4 py-3 border-b align-middle">
                                        <div class="text-sm font-medium text-gray-900">{{ record.date|date:"M d, Y" }}</div>
                                        <div class="text-xs text-gray-500">{{ record.date|time:"H:i" }}</div>
                                    </td>

                                    <!-- Healthcare Provider Column -->
                                    <td class="provider-column px-4 py-3 border-b align-middle">
                                        <div class="text-sm font-medium text-gray-900">{{ record.doctor_name }}</div>
                                        <div class="text-xs text-gray-500 facility-text">{{ record.facility_name }}</div>
                                    </td>

                                    <!-- Record Type Column -->
                                    <td class="type-column px-4 py-3 border-b align-middle">
                                        <span class="record-type-badge">{{ record.get_record_type_display|default:"Consultation" }}</span>
                                        {% if record.priority and record.priority != 'normal' %}
                                            <div class="priority-indicator {{ record.priority }} mt-1 text-xs">{{ record.get_priority_display }}</div>
                                        {% endif %}
                                    </td>

                                    <!-- Diagnosis Column -->
                                    <td class="diagnosis-column px-4 py-3 border-b align-middle">
                                        <div class="text-sm text-gray-900" title="{{ record.diagnosis }}">
                                            {{ record.diagnosis }}
                                        </div>
                                    </td>

                                    <!-- Status Column -->
                                    <td class="status-column px-4 py-3 border-b align-middle">
                                        <span class="record-status-badge {{ record.status|default:'active' }}">{{ record.get_status_display|default:"Active" }}</span>
                                    </td>

                                    <!-- Actions Column -->
                                    <td class="actions-column px-4 py-3 border-b align-middle">
                                        <div class="flex items-center justify-center space-x-1">
                                            <a href="{% url 'api:medical_record_detail' record_id=record.id %}" class="medical-record-action-button view" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'api:medical_record_edit' record_id=record.id %}" class="medical-record-action-button edit" title="Edit Record">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'api:export_medical_records_pdf' %}?record_id={{ record.id }}" class="medical-record-action-button download" title="Download PDF">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <a href="{% url 'api:prescription_create_for_record' medical_record_id=record.id %}" class="medical-record-action-button prescription" title="Add Prescription">
                                                <i class="fas fa-pills"></i>
                                            </a>
                                            <button onclick="confirmDeleteMedicalRecord('{{ record.id }}', '{{ record.patient.full_name }}', '{{ record.date|date:"M d, Y" }}')" class="medical-record-action-button delete" title="Delete Record">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Medical Record Details Row (if expanded) -->
                                {% if record.notes or record.treatment %}
                                    <tr class="details-row">
                                        <td colspan="7" class="px-6 py-4 bg-gray-50 border-b border-gray-200 space-y-2">
                                            {% if record.diagnosis %}
                                                <div class="text-xs text-gray-600"><strong class="mr-2">Full Diagnosis:</strong>{{ record.diagnosis }}</div>
                                            {% endif %}
                                            {% if record.treatment %}
                                                <div class="text-xs text-gray-600"><strong class="mr-2">Treatment:</strong>{{ record.treatment }}</div>
                                            {% endif %}
                                            {% if record.notes %}
                                                <div class="text-xs text-gray-600"><strong class="mr-2">Notes:</strong>{{ record.notes }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endif %}
                            {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center py-12">
                                        <div class="flex flex-col items-center">
                                            <div class="medical-record-avatar mx-auto mb-4">
                                                <i class="fas fa-file-medical text-gray-400 text-2xl"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No medical records found</h3>
                                            <p class="text-gray-500 mb-4">Get started by creating your first medical record or use the search filters.</p>
                                            <a href="{% url 'api:medical_record_create' %}" class="new-medical-record-button">
                                                <i class="fas fa-plus mr-2"></i>New Medical Record
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- AJAX Pagination Container -->
            <div class="pagination-container px-6 py-4 border-t border-gray-200" style="display: none;">
                <!-- Pagination will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

    <!-- QR Code Scanner Modal -->
    <div id="qr-modal" class="qr-modal">
        <div class="qr-modal-content">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">QR Code Scanner</h3>
                <button id="close-qr-btn" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="mb-4">
                <video id="qr-video" autoplay playsinline></video>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-600 mb-2">Point your camera at a patient QR code</p>
                <div class="flex items-center justify-center space-x-2 text-xs text-gray-500">
                    <i class="fas fa-qrcode"></i>
                    <span>QR codes contain patient ZimHealth-ID for instant lookup</span>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
<!-- jsQR Library for QR Code Detection -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script src="{% static 'assets/js/medical_records.js' %}?v=**********"></script>
{% endblock %}
