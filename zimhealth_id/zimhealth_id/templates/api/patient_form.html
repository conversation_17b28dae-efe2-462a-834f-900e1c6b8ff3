{% extends 'base.html' %}

{% block title %}{% if patient %}Edit Patient{% else %}Add New Patient{% endif %} - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/patient_success_modal.css' %}?v=**********">
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Patients Header - EXACT Mirror of Patients Page -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">
                            {% if patient %}Edit Patient Information{% else %}Add New Patient{% endif %}
                        </h1>
                        <p class="text-gray-600 mt-1 text-sm">
                            {% if patient %}Update patient information and medical details for {{ patient.full_name }}{% else %}Register a new patient in the healthcare system{% endif %}
                        </p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:patients' %}" class="government-filter-button">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Patients
                        </a>
                        <div class="flex items-center space-x-3">
                            {% if not patient %}
                            <button type="button" id="qr-scanner-btn" class="government-filter-button flex items-center space-x-2" title="Scan Patient QR Code">
                                <i class="fas fa-qrcode"></i>
                                <span>Scan QR</span>
                            </button>
                            {% endif %}
                            <div class="add-patient-button flex items-center space-x-2">
                                <i class="fas fa-user-{% if patient %}edit{% else %}plus{% endif %}"></i>
                                <span>{% if patient %}Edit Patient{% else %}New Patient{% endif %}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Registration Content - Layout as per wireframe -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 relative z-10">
        <form id="patient-registration-form" method="post" action="{% if patient and patient.zimhealth_id %}{% url 'api:patient_edit' patient.zimhealth_id %}{% else %}{% url 'api:patient_create' %}{% endif %}" enctype="multipart/form-data">
            {% csrf_token %}

            <!-- Top Row: Personal Information (Left) + Additional Fields (Right) -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">

                <!-- Personal Information - Left Side (2/3 width) -->
                <div class="lg:col-span-2">
                    <div class="patients-table-card">
                        <div class="patients-table-header px-6 py-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Personal Information</h3>
                                <div class="flex items-center space-x-2 text-sm text-gray-500">
                                    <i class="fas fa-user"></i>
                                    <span>Patient Details</span>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="patients-table w-full">
                                <colgroup>
                                    <col style="width: 30%;">
                                    <col style="width: 70%;">
                                </colgroup>
                                <tbody>
                                    <tr>
                                        <td class="font-semibold text-gray-700">First Name</td>
                                        <td>
                                            <input type="text" name="first_name"
                                                   value="{{ form.first_name.value|default:'' }}"
                                                   class="government-search-input-compact w-full"
                                                   placeholder="Enter first name" required>
                                            {% if form.first_name.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.first_name.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Last Name</td>
                                        <td>
                                            <input type="text" name="last_name"
                                                   value="{{ form.last_name.value|default:'' }}"
                                                   class="government-search-input-compact w-full"
                                                   placeholder="Enter last name" required>
                                            {% if form.last_name.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.last_name.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="font-semibold text-gray-700">National ID</td>
                                        <td>
                                            <input type="text" name="national_id"
                                                   value="{{ form.national_id.value|default:'' }}"
                                                   class="government-search-input-compact w-full"
                                                   placeholder="Enter national ID">
                                            {% if form.national_id.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.national_id.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Date of Birth</td>
                                        <td>
                                            <input type="date" name="date_of_birth"
                                                   value="{{ form.date_of_birth.value|default:'' }}"
                                                   class="government-search-input-compact w-full" required>
                                            {% if form.date_of_birth.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.date_of_birth.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="font-semibold text-gray-700">Nationality</td>
                                        <td>
                                            <input type="text" name="nationality"
                                                   value="{{ form.nationality.value|default:'Zimbabwean' }}"
                                                   class="government-search-input-compact w-full"
                                                   placeholder="Enter nationality">
                                            {% if form.nationality.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.nationality.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Marital Status</td>
                                        <td>
                                            <select name="marital_status" class="government-search-input-compact w-full">
                                                <option value="">Select marital status</option>
                                                <option value="Single" {% if form.marital_status.value == 'Single' %}selected{% endif %}>Single</option>
                                                <option value="Married" {% if form.marital_status.value == 'Married' %}selected{% endif %}>Married</option>
                                                <option value="Divorced" {% if form.marital_status.value == 'Divorced' %}selected{% endif %}>Divorced</option>
                                                <option value="Widowed" {% if form.marital_status.value == 'Widowed' %}selected{% endif %}>Widowed</option>
                                            </select>
                                            {% if form.marital_status.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.marital_status.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Additional Fields - Right Side (1/3 width) - Compressed but Same Height -->
                <div class="lg:col-span-1">
                    <div class="patients-table-card h-full compact-additional-info">
                        <div class="patients-table-header px-4 py-3">
                            <div class="flex items-center justify-between">
                                <h3 class="text-base font-semibold text-gray-900">Additional Info</h3>
                                <div class="flex items-center space-x-1 text-xs text-gray-500">
                                    <i class="fas fa-info-circle text-sm"></i>
                                    <span>Details</span>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto flex-1">
                            <table class="patients-table w-full h-full compact-table">
                                <colgroup>
                                    <col style="width: 45%;">
                                    <col style="width: 55%;">
                                </colgroup>
                                <tbody>
                                    <tr>
                                        <td class="font-medium text-gray-700 text-sm py-2">Gender</td>
                                        <td class="py-2">
                                            <select name="gender" class="government-search-input-compact w-full" required>
                                                <option value="">Select</option>
                                                <option value="M" {% if form.gender.value == 'M' %}selected{% endif %}>Male</option>
                                                <option value="F" {% if form.gender.value == 'F' %}selected{% endif %}>Female</option>
                                                <option value="O" {% if form.gender.value == 'O' %}selected{% endif %}>Other</option>
                                            </select>
                                            {% if form.gender.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.gender.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-gray-700 text-sm py-2">Blood Type</td>
                                        <td class="py-2">
                                            <select name="blood_type" class="government-search-input-compact w-full">
                                                <option value="">Select</option>
                                                <option value="A+" {% if form.blood_type.value == 'A+' %}selected{% endif %}>A+</option>
                                                <option value="A-" {% if form.blood_type.value == 'A-' %}selected{% endif %}>A-</option>
                                                <option value="B+" {% if form.blood_type.value == 'B+' %}selected{% endif %}>B+</option>
                                                <option value="B-" {% if form.blood_type.value == 'B-' %}selected{% endif %}>B-</option>
                                                <option value="AB+" {% if form.blood_type.value == 'AB+' %}selected{% endif %}>AB+</option>
                                                <option value="AB-" {% if form.blood_type.value == 'AB-' %}selected{% endif %}>AB-</option>
                                                <option value="O+" {% if form.blood_type.value == 'O+' %}selected{% endif %}>O+</option>
                                                <option value="O-" {% if form.blood_type.value == 'O-' %}selected{% endif %}>O-</option>
                                            </select>
                                            {% if form.blood_type.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.blood_type.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-gray-700 text-sm py-2">Emergency Contact</td>
                                        <td class="py-2">
                                            <input type="tel" name="emergency_contact"
                                                   value="{{ form.emergency_contact.value|default:'' }}"
                                                   class="government-search-input-compact w-full"
                                                   placeholder="Emergency phone">
                                            {% if form.emergency_contact.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.emergency_contact.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-gray-700 text-sm py-1" colspan="2">Known Allergies</td>
                                    </tr>
                                    <tr style="height: 100%;">
                                        <td colspan="2" class="py-1" style="height: 100%; vertical-align: top;">
                                            <textarea name="allergies" id="allergies-input"
                                                      class="government-search-input-compact w-full resize-none text-sm"
                                                      style="height: 100%; min-height: 120px;"
                                                      placeholder="Enter known allergies separated by commas (e.g., Penicillin, Peanuts, Shellfish)">{{ form.allergies.value|default:'' }}</textarea>
                                            {% if form.allergies.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.allergies.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information - Full Width Bottom -->
            <div class="patients-table-card mb-6">
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Contact Information</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-phone"></i>
                            <span>Contact Details</span>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 20%;">
                            <col style="width: 30%;">
                            <col style="width: 20%;">
                            <col style="width: 30%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Phone Number</td>
                                <td>
                                    <input type="tel" name="phone_number"
                                           value="{{ form.phone_number.value|default:'' }}"
                                           class="government-search-input-compact w-full"
                                           placeholder="Enter phone number" required>
                                    {% if form.phone_number.errors %}
                                        <div class="text-red-600 text-xs mt-1">{{ form.phone_number.errors.0 }}</div>
                                    {% endif %}
                                </td>
                                <td class="font-semibold text-gray-700">Email Address</td>
                                <td>
                                    <input type="email" name="email"
                                           value="{{ form.email.value|default:'' }}"
                                           class="government-search-input-compact w-full"
                                           placeholder="Enter email address">
                                    {% if form.email.errors %}
                                        <div class="text-red-600 text-xs mt-1">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Address</td>
                                <td colspan="3">
                                    <textarea name="address" rows="2"
                                              class="government-search-input-compact w-full resize-none"
                                              placeholder="Enter full address">{{ form.address.value|default:'' }}</textarea>
                                    {% if form.address.errors %}
                                        <div class="text-red-600 text-xs mt-1">{{ form.address.errors.0 }}</div>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Submit Section - Mirror of Patients Page Actions -->
            <div class="flex items-center justify-between">
                <div class="results-count text-sm text-gray-600 font-medium">
                    {% if patient %}Update patient information and save changes{% else %}Complete all required fields to register new patient{% endif %}
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'api:patients' %}" class="government-filter-button">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                    <button type="submit" class="add-patient-button flex items-center space-x-2">
                        <i class="fas fa-save"></i>
                        <span>{% if patient %}Update Patient{% else %}Save Patient{% endif %}</span>
                    </button>
                </div>
            </div>

        </form>
    </div>
</div>

<style>
/* Force exact equal heights for both sections */
.lg\:col-span-2 .patients-table-card,
.lg\:col-span-1 .patients-table-card {
    display: flex;
    flex-direction: column;
    height: 100%; /* Force full height */
    min-height: 100%; /* Ensure minimum height matching */
}

.lg\:col-span-2 .patients-table-card .overflow-x-auto,
.lg\:col-span-1 .patients-table-card .overflow-x-auto {
    flex: 1;
    overflow-y: visible; /* Remove scroll */
    display: flex;
    flex-direction: column;
}

.lg\:col-span-1 .patients-table-card .overflow-x-auto table {
    height: 100%;
}

.lg\:col-span-1 .patients-table-card .overflow-x-auto table tbody {
    height: 100%;
    vertical-align: top;
}

/* Perfect equal height grid layout */
@media (min-width: 1024px) {
    .grid.lg\:grid-cols-3 {
        align-items: stretch; /* Force equal heights */
        display: grid; /* Ensure grid behavior */
    }

    .grid.lg\:grid-cols-3 > div {
        display: flex;
        flex-direction: column;
        height: 100%; /* Force equal heights */
    }

    /* Ensure Additional Info fills available space */
    .lg\:col-span-1 {
        display: flex !important;
        flex-direction: column !important;
    }

    .lg\:col-span-1 .compact-additional-info {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
    }
}

/* Compact styling for both sections - substantially reduced */
.patients-table-card .patients-table-header {
    padding: 8px 16px !important; /* Reduced header padding */
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0; /* Don't shrink header */
}

.patients-table td {
    padding: 4px 8px !important; /* Substantially reduced cell padding */
    vertical-align: middle;
    line-height: 1.2 !important;
}

.compact-additional-info .compact-table {
    height: 100% !important;
    display: table !important;
}

.compact-additional-info .compact-table tbody {
    height: 100% !important;
    display: table-row-group !important;
}

.compact-additional-info .compact-table td {
    padding: 3px 6px !important; /* Even more compact for Additional Info */
    vertical-align: top !important;
}

.compact-additional-info .compact-table tr:first-child td {
    padding-top: 4px !important;
}

.compact-additional-info .compact-table tr:last-child td {
    padding-bottom: 4px !important;
}

/* Make allergies textarea fill remaining space */
.compact-additional-info .compact-table tr:last-child {
    height: 100% !important;
}

.compact-additional-info .compact-table tr:last-child td {
    height: 100% !important;
    vertical-align: top !important;
}

.compact-additional-info .compact-table tr:last-child textarea {
    height: 100% !important;
    min-height: 120px !important;
}

/* Substantially reduced input styling */
.government-search-input {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 0;
    font-size: 0.8rem;
    color: #374151;
    padding: 8px 12px !important; /* Substantially reduced padding */
    line-height: 1.2 !important;
    min-height: 32px !important; /* Much smaller height */
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.government-search-input-compact {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 0;
    font-size: 0.75rem;
    color: #374151;
    padding: 6px 10px !important; /* Even more compact */
    line-height: 1.1 !important;
    min-height: 28px !important; /* Very small height */
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.government-search-input:focus,
.government-search-input-compact:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Compact textarea styling */
textarea.government-search-input {
    padding: 8px 12px !important;
    line-height: 1.3 !important;
    min-height: 60px !important; /* Much smaller textarea */
    resize: none;
}

textarea.government-search-input-compact {
    padding: 6px 10px !important;
    line-height: 1.2 !important;
    min-height: 50px !important; /* Very compact textarea */
    resize: none;
}

/* Mobile responsive for compact inputs */
@media (max-width: 768px) {
    .government-search-input {
        font-size: 0.75rem !important;
        padding: 6px 10px !important;
        min-height: 28px !important;
    }

    .government-search-input-compact {
        font-size: 0.7rem !important;
        padding: 4px 8px !important;
        min-height: 24px !important;
    }

    textarea.government-search-input {
        padding: 6px 10px !important;
        min-height: 50px !important;
    }

    textarea.government-search-input-compact {
        padding: 4px 8px !important;
        min-height: 40px !important;
    }

    .patients-table td {
        padding: 2px 4px !important;
    }

    .compact-additional-info .compact-table td {
        padding: 1px 3px !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle allergies field formatting
    const allergiesInput = document.getElementById('allergies-input');

    if (allergiesInput) {
        // Clean up the allergies value on page load
        let currentValue = allergiesInput.value.trim();

        // Check if the value looks like a Python list format ['item1', 'item2']
        if (currentValue.startsWith('[') && currentValue.endsWith(']')) {
            try {
                // Remove brackets and quotes, then split by comma
                let cleanValue = currentValue.slice(1, -1); // Remove [ and ]
                let items = cleanValue.split(',').map(item => {
                    // Remove quotes and trim whitespace
                    return item.trim().replace(/^['"]|['"]$/g, '');
                }).filter(item => item.length > 0); // Remove empty items

                // Join with commas and spaces
                allergiesInput.value = items.join(', ');
            } catch (e) {
                // If parsing fails, just remove the brackets
                allergiesInput.value = currentValue.slice(1, -1).replace(/['"]/g, '');
            }
        }

        // Add input validation to ensure professional format
        allergiesInput.addEventListener('blur', function() {
            let value = this.value.trim();
            if (value) {
                // Split by comma, clean each item, and rejoin
                let items = value.split(',').map(item => {
                    return item.trim().replace(/^['"]+|['"]+$/g, ''); // Remove quotes
                }).filter(item => item.length > 0);

                // Capitalize first letter of each allergy
                items = items.map(item => {
                    return item.charAt(0).toUpperCase() + item.slice(1).toLowerCase();
                });

                this.value = items.join(', ');
            }
        });
    }
});
</script>

<!-- Patient Registration Success System -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script src="{% static 'assets/js/patient_success_modal.js' %}?v=**********"></script>

{% endblock %}
