{% extends 'base.html' %}

{% block title %}Medical History - {{ patient.full_name }} - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/patient_medical_history.css' %}">
{% endblock %}

{% block content %}
<div class="medical-history-container">
    <!-- Professional Header -->
    <div class="history-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="header-title">Complete Medical History</h1>
                    <p class="header-subtitle">{{ patient.full_name }} ({{ patient.zimhealth_id }})</p>
                </div>
                <div class="header-right">
                    <div class="patient-summary">
                        <div class="summary-item">
                            <span class="summary-label">Age:</span>
                            <span class="summary-value">{{ patient.age }} years</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Gender:</span>
                            <span class="summary-value">{{ patient.get_gender_display }}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Blood Type:</span>
                            <span class="summary-value">{{ patient.blood_type|default:"Unknown" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Medical History Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stat-card">
                <div class="stat-icon medical-records">
                    <i class="fas fa-file-medical"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_records }}</h3>
                    <p>Medical Records</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon prescriptions">
                    <i class="fas fa-pills"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_prescriptions }}</h3>
                    <p>Prescriptions</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon appointments">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_appointments }}</h3>
                    <p>Appointments</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon years">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ patient.created_at|timesince }}</h3>
                    <p>Patient Since</p>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main History Content -->
            <div class="lg:col-span-2">
                <!-- Medical Records Timeline -->
                <div class="history-card mb-8">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-file-medical mr-2"></i>Medical Records
                        </h3>
                        <a href="{% url 'api:medical_record_create_for_patient' patient.zimhealth_id %}" class="btn btn-primary">
                            <i class="fas fa-plus mr-2"></i>Add Record
                        </a>
                    </div>
                    <div class="card-content">
                        {% if medical_records %}
                            <div class="timeline">
                                {% for record in medical_records %}
                                    <div class="timeline-item">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <div class="timeline-header">
                                                <h4 class="timeline-title">
                                                    <a href="{% url 'api:medical_record_detail' record.id %}">
                                                        {{ record.diagnosis|default:"General Consultation" }}
                                                    </a>
                                                </h4>
                                                <span class="timeline-date">{{ record.date|date:"M d, Y" }}</span>
                                            </div>
                                            <div class="timeline-details">
                                                <p><strong>Doctor:</strong> {{ record.doctor_name }}</p>
                                                <p><strong>Facility:</strong> {{ record.facility_name }}</p>
                                                {% if record.symptoms %}
                                                    <p><strong>Symptoms:</strong> {{ record.symptoms|truncatewords:15 }}</p>
                                                {% endif %}
                                                {% if record.treatment %}
                                                    <p><strong>Treatment:</strong> {{ record.treatment|truncatewords:15 }}</p>
                                                {% endif %}
                                            </div>
                                            <div class="timeline-actions">
                                                <a href="{% url 'api:medical_record_detail' record.id %}" class="btn btn-sm btn-outline">
                                                    View Details
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Pagination -->
                            {% if medical_records.has_other_pages %}
                                <div class="pagination-container">
                                    <div class="pagination">
                                        {% if medical_records.has_previous %}
                                            <a href="?page=1" class="pagination-button">First</a>
                                            <a href="?page={{ medical_records.previous_page_number }}" class="pagination-button">Previous</a>
                                        {% endif %}

                                        <span class="pagination-info">
                                            Page {{ medical_records.number }} of {{ medical_records.paginator.num_pages }}
                                        </span>

                                        {% if medical_records.has_next %}
                                            <a href="?page={{ medical_records.next_page_number }}" class="pagination-button">Next</a>
                                            <a href="?page={{ medical_records.paginator.num_pages }}" class="pagination-button">Last</a>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        {% else %}
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-file-medical"></i>
                                </div>
                                <h3>No Medical Records</h3>
                                <p>No medical records found for this patient.</p>
                                <a href="{% url 'api:medical_record_create_for_patient' patient.zimhealth_id %}" class="btn btn-primary">
                                    Add First Record
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Patient Information -->
                <div class="history-card mb-6">
                    <div class="card-header">
                        <h3 class="card-title">Patient Information</h3>
                    </div>
                    <div class="card-content">
                        <div class="patient-info">
                            <div class="info-item">
                                <label>Full Name</label>
                                <p>{{ patient.full_name }}</p>
                            </div>
                            <div class="info-item">
                                <label>Date of Birth</label>
                                <p>{{ patient.date_of_birth|date:"F d, Y" }}</p>
                            </div>
                            <div class="info-item">
                                <label>Phone</label>
                                <p>{{ patient.phone_number }}</p>
                            </div>
                            <div class="info-item">
                                <label>Emergency Contact</label>
                                <p>{{ patient.emergency_contact_phone|default:"Not provided" }}</p>
                            </div>
                            {% if patient.allergies %}
                            <div class="info-item">
                                <label>Allergies</label>
                                <p class="allergies-info">{{ patient.allergies }}</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="patient-actions mt-4">
                            <a href="{% url 'api:patient_detail' patient.zimhealth_id %}" class="btn btn-outline btn-block">
                                View Full Profile
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Prescriptions -->
                <div class="history-card mb-6">
                    <div class="card-header">
                        <h3 class="card-title">Recent Prescriptions</h3>
                    </div>
                    <div class="card-content">
                        {% if prescriptions %}
                            <div class="prescription-list">
                                {% for prescription in prescriptions %}
                                    <div class="prescription-item">
                                        <div class="prescription-info">
                                            <h4>{{ prescription.medication }}</h4>
                                            <p class="prescription-details">{{ prescription.dosage }} - {{ prescription.frequency }}</p>
                                            <div class="prescription-meta">
                                                <span class="status-badge {{ prescription.status }}">
                                                    {{ prescription.get_status_display }}
                                                </span>
                                                <span class="prescription-date">
                                                    {{ prescription.start_date|date:"M d, Y" }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                            <div class="mt-4">
                                <a href="{% url 'api:prescriptions' %}?patient={{ patient.zimhealth_id }}" class="btn btn-outline btn-sm">
                                    View All Prescriptions
                                </a>
                            </div>
                        {% else %}
                            <p class="text-gray-500">No prescriptions found</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Recent Appointments -->
                <div class="history-card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Appointments</h3>
                    </div>
                    <div class="card-content">
                        {% if appointments %}
                            <div class="appointment-list">
                                {% for appointment in appointments %}
                                    <div class="appointment-item">
                                        <div class="appointment-info">
                                            <h4>{{ appointment.get_appointment_type_display }}</h4>
                                            <p class="appointment-date">{{ appointment.date|date:"M d, Y" }} at {{ appointment.time|time:"H:i" }}</p>
                                            <span class="status-badge {{ appointment.status }}">
                                                {{ appointment.get_status_display }}
                                            </span>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                            <div class="mt-4">
                                <a href="{% url 'api:appointments' %}?patient={{ patient.zimhealth_id }}" class="btn btn-outline btn-sm">
                                    View All Appointments
                                </a>
                            </div>
                        {% else %}
                            <p class="text-gray-500">No appointments found</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'assets/js/patient_medical_history.js' %}"></script>
{% endblock %}
