{% extends 'base.html' %}
{% load static %}

{% block title %}{{ patient.full_name }} - Patient Details{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}">
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Patients Header - EXACT Mirror of Patients Page -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ patient.full_name }}</h1>
                        <p class="text-gray-600 mt-1 text-sm">Patient details and medical information</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:patients' %}" class="add-patient-button flex items-center space-x-2 bg-gray-600 hover:bg-gray-700">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back to Patients</span>
                        </a>
                        <a href="{% url 'api:patient_edit' patient.zimhealth_id %}" class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-edit"></i>
                            <span>Edit Patient</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Patient Detail Layout - Matching Wireframe -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Top Section: ZimHealth ID and QR Code - Patients Page Theme -->
        <div class="mb-6 flex items-center justify-between">
            <div class="government-filter-button">
                <i class="fas fa-id-card mr-2"></i>ZimHealth ID: {{ patient.zimhealth_id }}
            </div>
            <div class="flex items-center space-x-2">
                <a href="{% url 'api:patient_qr_code' patient.zimhealth_id %}" class="government-filter-button">
                    <i class="fas fa-qrcode mr-2"></i>QR Code
                </a>
            </div>
        </div>

        <!-- Main Layout: Patient Info (Left) and Medical Records/Appointments (Right) -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column: Patient Information -->
            <div class="lg:col-span-1">
                <div class="patients-table-card h-full">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-center">
                            <h3 class="text-lg font-semibold text-gray-900">Patient Info</h3>
                        </div>
                    </div>

                    <div class="p-6">
                        <!-- Patient Avatar -->
                        <div class="flex justify-center mb-6">
                            <div class="w-24 h-24 rounded-full bg-black flex items-center justify-center">
                                <i class="fas fa-user text-white text-2xl"></i>
                            </div>
                        </div>

                        <!-- Patient Details -->
                        <div class="space-y-4">
                            <div class="text-center">
                                <h4 class="text-lg font-bold text-gray-900">{{ patient.full_name }}</h4>
                                <p class="text-sm text-gray-600">{{ patient.age }} years old • {{ patient.get_gender_display }}</p>
                            </div>

                            <div class="border-t pt-4 space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">National ID</span>
                                    <span class="text-sm text-gray-900">{{ patient.national_id|default:"Not Provided" }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">Phone</span>
                                    <span class="text-sm text-gray-900">{{ patient.phone_number }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">Emergency</span>
                                    <span class="text-sm text-gray-900">{{ patient.emergency_contact }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">Blood Type</span>
                                    <span class="text-sm text-gray-900">
                                        {% if patient.blood_type %}
                                            <span class="blood-type-badge blood-type-{{ patient.blood_type|lower }}">{{ patient.blood_type }}</span>
                                        {% else %}
                                            <span class="text-gray-400">Unknown</span>
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-xs font-semibold text-gray-600 uppercase">Status</span>
                                    <span class="text-sm text-gray-900">
                                        {% if patient.is_active %}
                                            <span class="bg-green-100 text-green-800 px-2 py-1 text-xs font-bold uppercase">Active</span>
                                        {% else %}
                                            <span class="bg-red-100 text-red-800 px-2 py-1 text-xs font-bold uppercase">Inactive</span>
                                        {% endif %}
                                    </span>
                                </div>
                            </div>

                            <div class="border-t pt-4">
                                <span class="text-xs font-semibold text-gray-600 uppercase block mb-2">Address</span>
                                <p class="text-sm text-gray-900">{{ patient.address }}</p>
                            </div>

                            <div class="border-t pt-4">
                                <span class="text-xs font-semibold text-gray-600 uppercase block mb-2">Allergies</span>
                                {% if patient.allergies %}
                                    <div class="space-y-1">
                                        {% for allergy in patient.allergies %}
                                            <div class="bg-yellow-100 text-yellow-800 px-2 py-1 text-xs font-medium">
                                                {{ allergy }}
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <p class="text-sm text-gray-500 italic">No known allergies</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column: Medical Records and Appointments -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Medical Records Section -->
                <div class="patients-table-card mb-6">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Medical Records</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-file-medical"></i>
                                <span>Patient Records</span>
                            </div>
                        </div>
                    </div>

                    <div class="p-6" style="min-height: 300px;">
                        {% if recent_records %}
                            <div class="space-y-4">
                                {% for record in recent_records %}
                                    <div class="border-2 border-gray-200 p-4">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-2 mb-2">
                                                    <h5 class="text-sm font-bold text-gray-900 uppercase">{{ record.facility_name }}</h5>
                                                    <span class="text-xs text-gray-500">•</span>
                                                    <span class="text-xs font-semibold text-gray-600 uppercase">Dr. {{ record.doctor_name }}</span>
                                                </div>
                                                <p class="text-sm text-gray-700 mb-2">{{ record.diagnosis|truncatechars:100 }}</p>
                                                <div class="flex items-center space-x-4 text-xs text-gray-600">
                                                    <span class="font-semibold uppercase">{{ record.date|date:"M d, Y" }}</span>
                                                    {% if record.prescriptions.count %}
                                                        <span class="font-semibold uppercase">{{ record.prescriptions.count }} Prescription{{ record.prescriptions.count|pluralize }}</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <button class="action-button view ml-4">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-12">
                                <i class="fas fa-file-medical text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-600 font-semibold uppercase text-sm">No Medical Records Found</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Appointments Section -->
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Appointments</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-calendar"></i>
                                <span>Scheduled Visits</span>
                            </div>
                        </div>
                    </div>

                    <div class="p-6" style="min-height: 250px;">
                        {% if upcoming_appointments %}
                            <div class="space-y-4">
                                {% for appointment in upcoming_appointments %}
                                    <div class="border-2 border-gray-200 p-4">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-8 h-8 bg-gray-100 border border-gray-300 flex items-center justify-center">
                                                    <i class="fas fa-calendar text-gray-600 text-sm"></i>
                                                </div>
                                                <div>
                                                    <p class="text-sm font-bold text-gray-900 uppercase">{{ appointment.appointment_type|title }}</p>
                                                    <p class="text-xs font-semibold text-gray-600 uppercase">Dr. {{ appointment.doctor_name }} • {{ appointment.facility_name }}</p>
                                                    <p class="text-xs font-semibold text-gray-600 uppercase">{{ appointment.date|date:"M d, Y" }} at {{ appointment.time|time:"H:i" }}</p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 text-xs font-bold uppercase border border-blue-300">
                                                    {{ appointment.status|title }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-12">
                                <i class="fas fa-calendar text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-600 font-semibold uppercase text-sm">No Upcoming Appointments</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Action Buttons - Patients Page Theme -->
        <div class="mt-8 flex items-center justify-between">
            <div class="results-count text-sm text-gray-600 font-medium">
                Patient management actions for {{ patient.full_name }}
            </div>
            <div class="flex items-center space-x-4">
                <a href="{% url 'api:medical_record_create_for_patient' patient.zimhealth_id %}" class="add-patient-button flex items-center space-x-2">
                    <i class="fas fa-file-medical"></i>
                    <span>Add Medical Record</span>
                </a>
                <a href="{% url 'api:appointment_create_for_patient' patient.zimhealth_id %}" class="add-patient-button flex items-center space-x-2 bg-gray-600 hover:bg-gray-700">
                    <i class="fas fa-calendar-plus"></i>
                    <span>Schedule Appointment</span>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
