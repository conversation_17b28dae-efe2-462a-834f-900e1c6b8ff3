{% extends 'base.html' %}
{% load static %}

{% block title %}QR Code - {{ patient.full_name }} - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/patient_qr_code.css' %}?v=**********">
{% endblock %}

{% block content %}
<div class="qr-code-container">
    <!-- Professional Patients Header - EXACT Mirror of Other Pages -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Patient QR Code</h1>
                        <p class="text-gray-600 mt-1 text-sm">Digital identification for {{ patient.full_name|title }} ({{ patient.zimhealth_id }})</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:patient_detail' patient.zimhealth_id %}" class="government-filter-button">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Patient
                        </a>
                        <button onclick="window.print()" class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-print"></i>
                            <span>Print QR Code</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content - Compact Professional Layout -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <div class="qr-content-grid">
            <!-- Main QR Code Display -->
            <div class="qr-main-content">
                <!-- QR Code Display Card -->
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Digital QR Code</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-shield-check text-green-600"></i>
                                <span>Verified</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6 text-center">
                        <div class="qr-code-wrapper">
                            {% if patient.qr_code %}
                                <img src="{{ patient.qr_code.url }}" alt="QR Code for {{ patient.full_name }}" class="qr-code-image">
                            {% else %}
                                <div class="qr-code-image bg-gray-100 border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
                                    <div class="text-center">
                                        <i class="fas fa-qrcode text-gray-400 text-6xl mb-2"></i>
                                        <p class="text-gray-500 text-xs">QR Code Not Generated</p>
                                        <p class="text-gray-400 text-xs font-mono">{{ patient.zimhealth_id }}</p>
                                        <button onclick="generateQRCode('{{ patient.zimhealth_id }}')" class="mt-2 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                            Generate QR Code
                                        </button>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        <p class="qr-code-id">{{ patient.zimhealth_id }}</p>
                        <p class="qr-code-description">Scan to access patient profile</p>
                    </div>
                </div>

                <!-- Patient Information Card -->
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Patient Information</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-user-check text-green-600"></i>
                                <span>Verified Patient</span>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="patients-table w-full">
                            <colgroup>
                                <col style="width: 25%;">
                                <col style="width: 25%;">
                                <col style="width: 25%;">
                                <col style="width: 25%;">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Patient Name</td>
                                    <td class="text-sm">{{ patient.full_name|title }}</td>
                                    <td class="font-medium text-gray-700 text-sm">ZimHealth ID</td>
                                    <td class="text-sm font-mono">{{ patient.zimhealth_id }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Age</td>
                                    <td class="text-sm">{{ patient.age }} years</td>
                                    <td class="font-medium text-gray-700 text-sm">Gender</td>
                                    <td class="text-sm">{{ patient.get_gender_display }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Date of Birth</td>
                                    <td class="text-sm">{{ patient.date_of_birth|date:"M d, Y" }}</td>
                                    <td class="font-medium text-gray-700 text-sm">Blood Type</td>
                                    <td class="text-sm">
                                        {% if patient.blood_type %}
                                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                                                {{ patient.blood_type }}
                                            </span>
                                        {% else %}
                                            <span class="text-gray-400">Not specified</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="qr-sidebar">
                <!-- Usage Instructions -->
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">How to Use</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-info-circle text-blue-600"></i>
                                <span>Instructions</span>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="patients-table w-full">
                            <tbody>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm w-8">
                                        <i class="fas fa-mobile-alt text-blue-600"></i>
                                    </td>
                                    <td class="text-sm">Scan with any QR code reader or smartphone camera</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm w-8">
                                        <i class="fas fa-link text-blue-600"></i>
                                    </td>
                                    <td class="text-sm">Links directly to patient's medical profile</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm w-8">
                                        <i class="fas fa-shield-alt text-blue-600"></i>
                                    </td>
                                    <td class="text-sm">Secure access - requires healthcare provider login</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm w-8">
                                        <i class="fas fa-clock text-blue-600"></i>
                                    </td>
                                    <td class="text-sm">Instant access to medical history and records</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Contact Details</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-phone text-green-600"></i>
                                <span>Contact Info</span>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="patients-table w-full">
                            <colgroup>
                                <col style="width: 35%;">
                                <col style="width: 65%;">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Phone</td>
                                    <td class="text-sm">{{ patient.phone_number|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Emergency</td>
                                    <td class="text-sm">{{ patient.emergency_contact|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Address</td>
                                    <td class="text-sm">{{ patient.address|default:"Not provided" }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Medical Information -->
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Medical Info</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-heartbeat text-red-600"></i>
                                <span>Medical Details</span>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="patients-table w-full">
                            <colgroup>
                                <col style="width: 35%;">
                                <col style="width: 65%;">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Allergies</td>
                                    <td class="text-sm">
                                        {% if patient.allergies %}
                                            <span style="color: #ef4444; font-weight: 600;">{{ patient.allergies }}</span>
                                        {% else %}
                                            <span style="color: #9ca3af;">No known allergies</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">National ID</td>
                                    <td class="text-sm font-mono">{{ patient.national_id|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Registered</td>
                                    <td class="text-sm">{{ patient.created_at|date:"M d, Y" }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- System Information -->
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">System Info</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-cog text-gray-600"></i>
                                <span>Generated Info</span>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="patients-table w-full">
                            <colgroup>
                                <col style="width: 35%;">
                                <col style="width: 65%;">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Generated</td>
                                    <td class="text-sm">{{ now|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">System</td>
                                    <td class="text-sm">ZimHealth-ID</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700 text-sm">Type</td>
                                    <td class="text-sm">Patient Digital ID</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Professional Action Buttons - Mirror of Other Pages -->
        <div class="flex items-center justify-between mt-6">
            <div class="results-count text-sm text-gray-600 font-medium">
                QR Code ready for printing and digital sharing
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'api:patient_detail' patient.zimhealth_id %}" class="government-filter-button">
                    <i class="fas fa-user mr-2"></i>View Patient
                </a>
                <button onclick="downloadQR()" class="government-filter-button">
                    <i class="fas fa-download mr-2"></i>Download
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function downloadQR() {
    // In a real implementation, this would generate and download the QR code
    alert('QR Code download functionality would be implemented here with a QR code generation library.');
}

function generateQRCode(patientId) {
    // In a real implementation, this would generate a QR code for the patient
    alert('QR Code generation functionality would be implemented here.');
}

// Initialize QR code page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Ensure proper layout on load
    adjustLayoutForSinglePage();
    
    // Handle window resize
    window.addEventListener('resize', adjustLayoutForSinglePage);
});

function adjustLayoutForSinglePage() {
    // Ensure the layout fits within viewport
    const container = document.querySelector('.qr-code-container');
    if (container) {
        const viewportHeight = window.innerHeight;
        const headerHeight = document.querySelector('.patients-header')?.offsetHeight || 0;
        const availableHeight = viewportHeight - headerHeight;
        
        const contentArea = container.querySelector('.max-w-7xl');
        if (contentArea) {
            contentArea.style.maxHeight = `${availableHeight}px`;
        }
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+P for print
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        window.print();
    }
    
    // Escape to go back
    if (e.key === 'Escape') {
        const backButton = document.querySelector('a[href*="patient_detail"]');
        if (backButton) {
            window.location.href = backButton.href;
        }
    }
});

// Enhanced print function
function printQRCode() {
    // Hide action buttons for printing
    const elementsToHide = document.querySelectorAll('.government-filter-button, .add-patient-button, .results-count');
    const originalDisplay = [];
    
    elementsToHide.forEach((element, index) => {
        originalDisplay[index] = element.style.display;
        element.style.display = 'none';
    });
    
    // Print
    window.print();
    
    // Restore elements after printing
    setTimeout(() => {
        elementsToHide.forEach((element, index) => {
            element.style.display = originalDisplay[index];
        });
    }, 1000);
}
</script>
{% endblock %}

{% block extra_js %}
<script src="{% static 'assets/js/patient_qr_code.js' %}"></script>
{% endblock %}