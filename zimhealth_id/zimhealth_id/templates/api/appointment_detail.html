{% extends 'base.html' %}

{% block title %}Appointment Details - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/appointment_detail.css' %}">
{% endblock %}

{% block content %}
<div class="appointment-detail-container">
    <!-- Professional Header -->
    <div class="detail-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="header-title">Appointment Details</h1>
                    <p class="header-subtitle">{{ appointment.patient.full_name }} - {{ appointment.date|date:"M d, Y" }} at {{ appointment.time|time:"H:i" }}</p>
                </div>
                <div class="header-right">
                    <div class="status-indicator">
                        <span class="status-badge {{ appointment.status }}">
                            {{ appointment.get_status_display }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointment Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Appointment Information -->
            <div class="lg:col-span-2">
                <!-- Patient Information Card -->
                <div class="detail-card mb-6">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-circle mr-2"></i>Patient Information
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="info-item">
                                <label>Full Name</label>
                                <p>{{ appointment.patient.full_name }}</p>
                            </div>
                            <div class="info-item">
                                <label>ZimHealth ID</label>
                                <p>{{ appointment.patient.zimhealth_id }}</p>
                            </div>
                            <div class="info-item">
                                <label>Age</label>
                                <p>{{ appointment.patient.age }} years</p>
                            </div>
                            <div class="info-item">
                                <label>Gender</label>
                                <p>{{ appointment.patient.get_gender_display }}</p>
                            </div>
                            <div class="info-item">
                                <label>Phone</label>
                                <p>{{ appointment.patient.phone_number }}</p>
                            </div>
                            <div class="info-item">
                                <label>Emergency Contact</label>
                                <p>{{ appointment.patient.emergency_contact_phone|default:"Not provided" }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appointment Details -->
                <div class="detail-card mb-6">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calendar-alt mr-2"></i>Appointment Details
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="info-section">
                                <div class="info-item">
                                    <label>Date</label>
                                    <p>{{ appointment.date|date:"F d, Y" }}</p>
                                </div>
                                <div class="info-item">
                                    <label>Time</label>
                                    <p>{{ appointment.time|time:"H:i" }}</p>
                                </div>
                                <div class="info-item">
                                    <label>Duration</label>
                                    <p>{{ appointment.duration|default:"30 minutes" }}</p>
                                </div>
                            </div>
                            <div class="info-section">
                                <div class="info-item">
                                    <label>Type</label>
                                    <p>{{ appointment.get_appointment_type_display }}</p>
                                </div>
                                <div class="info-item">
                                    <label>Status</label>
                                    <p><span class="status-badge {{ appointment.status }}">{{ appointment.get_status_display }}</span></p>
                                </div>
                                <div class="info-item">
                                    <label>Priority</label>
                                    <p>{{ appointment.priority|default:"Normal" }}</p>
                                </div>
                            </div>
                        </div>

                        {% if appointment.reason %}
                        <div class="appointment-reason mt-6">
                            <div class="info-item">
                                <label>Reason for Visit</label>
                                <p>{{ appointment.reason }}</p>
                            </div>
                        </div>
                        {% endif %}

                        {% if appointment.notes %}
                        <div class="appointment-notes mt-6">
                            <div class="info-item">
                                <label>Notes</label>
                                <p>{{ appointment.notes }}</p>
                            </div>
                        </div>
                        {% endif %}

                        {% if appointment.status == 'cancelled' and appointment.cancellation_reason %}
                        <div class="cancellation-info mt-6">
                            <div class="info-item">
                                <label>Cancellation Reason</label>
                                <p>{{ appointment.cancellation_reason }}</p>
                            </div>
                            {% if appointment.cancelled_at %}
                            <div class="info-item">
                                <label>Cancelled At</label>
                                <p>{{ appointment.cancelled_at|date:"M d, Y H:i" }}</p>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Status Update Actions -->
                {% if appointment.status == 'scheduled' %}
                <div class="detail-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tasks mr-2"></i>Update Status
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="status-actions">
                            <button class="btn btn-success" onclick="updateAppointmentStatus('completed')">
                                <i class="fas fa-check mr-2"></i>Mark as Completed
                            </button>
                            <button class="btn btn-warning" onclick="updateAppointmentStatus('cancelled')">
                                <i class="fas fa-times mr-2"></i>Cancel Appointment
                            </button>
                            <button class="btn btn-secondary" onclick="updateAppointmentStatus('no_show')">
                                <i class="fas fa-user-times mr-2"></i>Mark as No Show
                            </button>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Actions -->
                <div class="detail-card mb-6">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    <div class="card-content">
                        <div class="action-list">
                            <a href="{% url 'api:patient_detail' appointment.patient.zimhealth_id %}" class="action-item">
                                <i class="fas fa-user mr-3"></i>View Patient Profile
                            </a>
                            <a href="{% url 'api:medical_record_create_for_patient' appointment.patient.zimhealth_id %}" class="action-item">
                                <i class="fas fa-file-medical-alt mr-3"></i>Create Medical Record
                            </a>
                            <a href="{% url 'api:appointment_create_for_patient' appointment.patient.zimhealth_id %}" class="action-item">
                                <i class="fas fa-calendar-plus mr-3"></i>Schedule New Appointment
                            </a>
                            <a href="{% url 'api:patient_medical_history' appointment.patient.zimhealth_id %}" class="action-item">
                                <i class="fas fa-history mr-3"></i>Medical History
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Appointment History -->
                <div class="detail-card mb-6">
                    <div class="card-header">
                        <h3 class="card-title">Recent Appointments</h3>
                    </div>
                    <div class="card-content">
                        <div class="appointment-history">
                            <!-- This would be populated with recent appointments for this patient -->
                            <p class="text-gray-500 text-sm">Loading recent appointments...</p>
                        </div>
                    </div>
                </div>

                <!-- Appointment Metadata -->
                <div class="detail-card">
                    <div class="card-header">
                        <h3 class="card-title">Appointment Information</h3>
                    </div>
                    <div class="card-content">
                        <div class="metadata-list">
                            <div class="metadata-item">
                                <label>Created</label>
                                <p>{{ appointment.created_at|date:"M d, Y H:i" }}</p>
                            </div>
                            <div class="metadata-item">
                                <label>Last Updated</label>
                                <p>{{ appointment.updated_at|date:"M d, Y H:i" }}</p>
                            </div>
                            <div class="metadata-item">
                                <label>Appointment ID</label>
                                <p>#{{ appointment.id }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div id="status-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Update Appointment Status</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form id="status-form">
                {% csrf_token %}
                <input type="hidden" id="new-status" name="status">
                <div class="form-group">
                    <label for="reason">Reason (optional)</label>
                    <textarea id="reason" name="reason" class="form-control" rows="3"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            <button class="btn btn-primary" onclick="confirmStatusUpdate()">Update Status</button>
        </div>
    </div>
</div>

<script>
function updateAppointmentStatus(status) {
    document.getElementById('new-status').value = status;
    document.getElementById('status-modal').style.display = 'block';
}

function closeModal() {
    document.getElementById('status-modal').style.display = 'none';
}

function confirmStatusUpdate() {
    const form = document.getElementById('status-form');
    const formData = new FormData(form);
    
    fetch(`{% url 'api:update_appointment_status_ajax' appointment_id=appointment.id %}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating status: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the status');
    });
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('status-modal');
    if (event.target == modal) {
        modal.style.display = 'none';
    }
}
</script>
{% endblock %}

{% block extra_js %}
<script src="{% static 'assets/js/appointment_detail.js' %}"></script>
{% endblock %}
