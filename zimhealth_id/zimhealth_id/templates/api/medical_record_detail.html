{% extends 'base.html' %}

{% block title %}Medical Record Details - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}?v=**********">
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Medical Record Header - EXACT Mirror of Patient Form -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Medical Record Details</h1>
                        <p class="text-gray-600 mt-1 text-sm">{{ medical_record.patient.full_name }} - {{ medical_record.date|date:"M d, Y" }}</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:medical_records' %}" class="government-filter-button">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Records
                        </a>
                        <a href="{% url 'api:medical_record_edit' medical_record.id %}" class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-edit"></i>
                            <span>Edit Record</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Medical Record Content - Layout as per wireframe -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Record Information -->
            <div class="lg:col-span-2">
                <!-- Patient Information Card -->
                <div class="patients-table-card mb-6">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Patient Information</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-user-circle"></i>
                                <span>Patient Details</span>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="patients-table w-full">
                            <colgroup>
                                <col style="width: 30%;">
                                <col style="width: 70%;">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td class="font-semibold text-gray-700">Full Name</td>
                                    <td class="text-gray-900">{{ medical_record.patient.full_name }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">ZimHealth ID</td>
                                    <td class="text-gray-900 font-mono">{{ medical_record.patient.zimhealth_id }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Age</td>
                                    <td class="text-gray-900">{{ medical_record.patient.age }} years</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Gender</td>
                                    <td class="text-gray-900">{{ medical_record.patient.get_gender_display }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Blood Type</td>
                                    <td class="text-gray-900">{{ medical_record.patient.blood_type|default:"Not specified" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Phone</td>
                                    <td class="text-gray-900">{{ medical_record.patient.phone_number }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Medical Record Details -->
                <div class="patients-table-card mb-6">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Medical Record Details</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-file-medical"></i>
                                <span>Visit Information</span>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="patients-table w-full">
                            <colgroup>
                                <col style="width: 30%;">
                                <col style="width: 70%;">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td class="font-semibold text-gray-700">Date of Visit</td>
                                    <td class="text-gray-900">{{ medical_record.date|date:"F d, Y" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Doctor</td>
                                    <td class="text-gray-900">{{ medical_record.doctor_name }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Facility</td>
                                    <td class="text-gray-900">{{ medical_record.facility_name }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Visit Type</td>
                                    <td class="text-gray-900">{{ medical_record.visit_type|default:"Regular Consultation" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Record Status</td>
                                    <td><span class="record-status-badge active">Active</span></td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Chief Complaint</td>
                                    <td class="text-gray-900">{{ medical_record.chief_complaint|default:"Not specified" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Symptoms</td>
                                    <td class="text-gray-900">{{ medical_record.symptoms|default:"Not specified" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Diagnosis</td>
                                    <td class="text-gray-900">{{ medical_record.diagnosis|default:"Not specified" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Treatment</td>
                                    <td class="text-gray-900">{{ medical_record.treatment|default:"Not specified" }}</td>
                                </tr>
                                {% if medical_record.notes %}
                                <tr>
                                    <td class="font-semibold text-gray-700">Additional Notes</td>
                                    <td class="text-gray-900">{{ medical_record.notes }}</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Prescriptions -->
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Prescriptions</h3>
                            <a href="{% url 'api:prescription_create_for_record' record_id=medical_record.id %}" class="add-patient-button flex items-center space-x-2">
                                <i class="fas fa-plus"></i>
                                <span>Add Prescription</span>
                            </a>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        {% if prescriptions %}
                            <table class="patients-table w-full">
                                <colgroup>
                                    <col style="width: 25%;">
                                    <col style="width: 30%;">
                                    <col style="width: 20%;">
                                    <col style="width: 15%;">
                                    <col style="width: 10%;">
                                </colgroup>
                                <thead>
                                    <tr>
                                        <th class="px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Medication</th>
                                        <th class="px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Dosage & Frequency</th>
                                        <th class="px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Duration</th>
                                        <th class="px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Status</th>
                                        <th class="px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border-b">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prescription in prescriptions %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 py-3 border-b">
                                                <div class="text-sm font-semibold text-gray-900">{{ prescription.medication }}</div>
                                            </td>
                                            <td class="px-4 py-3 border-b">
                                                <div class="text-sm text-gray-900">{{ prescription.dosage }}</div>
                                                <div class="text-xs text-gray-500">{{ prescription.frequency }}</div>
                                            </td>
                                            <td class="px-4 py-3 border-b">
                                                <div class="text-sm text-gray-900">{{ prescription.duration|default:"Ongoing" }}</div>
                                                <div class="text-xs text-gray-500">
                                                    {{ prescription.start_date|date:"M d" }}
                                                    {% if prescription.end_date %} - {{ prescription.end_date|date:"M d" }}{% endif %}
                                                </div>
                                            </td>
                                            <td class="px-4 py-3 border-b">
                                                <span class="record-status-badge {{ prescription.status }}">{{ prescription.get_status_display }}</span>
                                            </td>
                                            <td class="px-4 py-3 border-b">
                                                <a href="{% url 'api:prescription_detail' prescription.id %}" class="medical-record-action-button view" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        {% else %}
                            <div class="text-center py-12">
                                <div class="flex flex-col items-center">
                                    <div class="medical-record-avatar mx-auto mb-4">
                                        <i class="fas fa-pills text-gray-400 text-2xl"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">No prescriptions found</h3>
                                    <p class="text-gray-500 mb-4">Add the first prescription for this medical record.</p>
                                    <a href="{% url 'api:prescription_create_for_record' record_id=medical_record.id %}" class="add-patient-button">
                                        <i class="fas fa-plus mr-2"></i>Add First Prescription
                                    </a>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Actions -->
                <div class="patients-table-card mb-6">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-bolt"></i>
                                <span>Actions</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <a href="{% url 'api:patient_detail' medical_record.patient.zimhealth_id %}" class="government-filter-button w-full flex items-center justify-start">
                                <i class="fas fa-user mr-3"></i>View Patient Profile
                            </a>
                            <a href="{% url 'api:appointment_create_for_patient' medical_record.patient.zimhealth_id %}" class="government-filter-button w-full flex items-center justify-start">
                                <i class="fas fa-calendar-plus mr-3"></i>Schedule Appointment
                            </a>
                            <a href="{% url 'api:medical_record_create_for_patient' medical_record.patient.zimhealth_id %}" class="government-filter-button w-full flex items-center justify-start">
                                <i class="fas fa-file-medical-alt mr-3"></i>New Medical Record
                            </a>
                            <a href="{% url 'api:patient_medical_history' medical_record.patient.zimhealth_id %}" class="government-filter-button w-full flex items-center justify-start">
                                <i class="fas fa-history mr-3"></i>Medical History
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Record Metadata -->
                <div class="patients-table-card">
                    <div class="patients-table-header px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Record Information</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <i class="fas fa-info-circle"></i>
                                <span>Metadata</span>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="patients-table w-full">
                            <colgroup>
                                <col style="width: 40%;">
                                <col style="width: 60%;">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td class="font-semibold text-gray-700">Created</td>
                                    <td class="text-gray-900">{{ medical_record.created_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Last Updated</td>
                                    <td class="text-gray-900">{{ medical_record.updated_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-gray-700">Record ID</td>
                                    <td class="text-gray-900 font-mono">#{{ medical_record.id }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
