{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}?v=**********">
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Patients Header - EXACT Mirror of Patients Page -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
                        <p class="text-gray-600 mt-1 text-sm">
                            {% if patient %}Schedule appointment for {{ patient.full_name }}{% else %}Schedule a new patient appointment{% endif %}
                        </p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:appointments' %}" class="add-patient-button flex items-center space-x-2 bg-gray-600 hover:bg-gray-700">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back to Appointments</span>
                        </a>
                        <div class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-calendar-plus"></i>
                            <span>New Appointment</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content - EXACT Mirror of Patient Form Structure -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <form id="appointment-registration-form" method="post" action="{% url 'api:appointment_create' %}" enctype="multipart/form-data">
            {% csrf_token %}

            <!-- Grid Layout - Same as Patient Form -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                <!-- Main Appointment Information (Left Side - 2 columns) -->
                <div class="lg:col-span-2">

                    <!-- Patient Selection Card with QR Scanner -->
                    {% if not patient %}
                    <div class="patients-table-card mb-6">
                        <div class="patients-table-header px-6 py-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Patient Selection</h3>
                                <div class="flex items-center space-x-2 text-sm text-gray-500">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Find Patient</span>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="patients-table w-full">
                                <colgroup>
                                    <col style="width: 30%;">
                                    <col style="width: 70%;">
                                </colgroup>
                                <tbody>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Search Patient</td>
                                        <td>
                                            <div class="flex items-center space-x-2">
                                                <div class="flex-1 relative">
                                                    <input type="text" id="appointment-patient-search"
                                                           class="government-search-input-compact w-full pl-10 pr-4"
                                                           placeholder="Search by name, ZimHealth ID, or phone...">
                                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <i class="fas fa-search text-gray-500"></i>
                                                    </div>
                                                </div>
                                                <button type="button" id="appointment-qr-scanner-btn"
                                                        class="government-filter-button flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700"
                                                        title="Scan patient QR code">
                                                    <i class="fas fa-qrcode"></i>
                                                    <span class="hidden sm:inline">Scan QR</span>
                                                </button>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">Search for existing patient or scan their QR code</div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Selected Patient</td>
                                        <td>
                                            <select name="patient" id="appointment-patient-select" class="government-search-input-compact w-full" required>
                                                <option value="">No patient selected - search or scan QR code above</option>
                                            </select>
                                            {% if form.patient.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.patient.errors.0 }}</div>
                                            {% endif %}
                                            <div class="text-xs text-gray-500 mt-1">Patient will be auto-selected when found via search or QR scan</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% else %}
                    <!-- Selected Patient Display Card -->
                    <div class="patients-table-card mb-6">
                        <div class="patients-table-header px-6 py-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Selected Patient</h3>
                                <div class="flex items-center space-x-2 text-sm text-gray-500">
                                    <i class="fas fa-user-check"></i>
                                    <span>Patient Information</span>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="patients-table w-full">
                                <colgroup>
                                    <col style="width: 30%;">
                                    <col style="width: 70%;">
                                </colgroup>
                                <tbody>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Patient Name</td>
                                        <td>{{ patient.full_name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">ZimHealth ID</td>
                                        <td>{{ patient.zimhealth_id }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Age & Gender</td>
                                        <td>{{ patient.age }} years old • {{ patient.get_gender_display }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Phone Number</td>
                                        <td>{{ patient.phone_number }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Appointment Details Card -->
                    <div class="patients-table-card mb-6">
                        <div class="patients-table-header px-6 py-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Appointment Details</h3>
                                <div class="flex items-center space-x-2 text-sm text-gray-500">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Schedule Information</span>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="patients-table w-full">
                                <colgroup>
                                    <col style="width: 30%;">
                                    <col style="width: 70%;">
                                </colgroup>
                                <tbody>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Appointment Date</td>
                                        <td>
                                            <input type="date" name="date"
                                                   value="{{ form.date.value|default:'' }}"
                                                   class="government-search-input-compact w-full" required>
                                            {% if form.date.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.date.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Appointment Time</td>
                                        <td>
                                            <input type="time" name="time"
                                                   value="{{ form.time.value|default:'' }}"
                                                   class="government-search-input-compact w-full" required>
                                            {% if form.time.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.time.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Appointment Type</td>
                                        <td>
                                            <select name="appointment_type" class="government-search-input-compact w-full" required>
                                                <option value="">Select appointment type</option>
                                                <option value="Consultation" {% if form.appointment_type.value == 'Consultation' %}selected{% endif %}>Consultation</option>
                                                <option value="Follow-up" {% if form.appointment_type.value == 'Follow-up' %}selected{% endif %}>Follow-up</option>
                                                <option value="Emergency" {% if form.appointment_type.value == 'Emergency' %}selected{% endif %}>Emergency</option>
                                                <option value="Routine Check-up" {% if form.appointment_type.value == 'Routine Check-up' %}selected{% endif %}>Routine Check-up</option>
                                                <option value="Specialist Referral" {% if form.appointment_type.value == 'Specialist Referral' %}selected{% endif %}>Specialist Referral</option>
                                            </select>
                                            {% if form.appointment_type.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.appointment_type.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Status</td>
                                        <td>
                                            <select name="status" class="government-search-input-compact w-full" required>
                                                <option value="">Select status</option>
                                                <option value="Scheduled" {% if form.status.value == 'Scheduled' %}selected{% endif %}>Scheduled</option>
                                                <option value="Confirmed" {% if form.status.value == 'Confirmed' %}selected{% endif %}>Confirmed</option>
                                                <option value="Pending" {% if form.status.value == 'Pending' %}selected{% endif %}>Pending</option>
                                                <option value="Cancelled" {% if form.status.value == 'Cancelled' %}selected{% endif %}>Cancelled</option>
                                            </select>
                                            {% if form.status.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.status.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Healthcare Provider Card -->
                    <div class="patients-table-card mb-6">
                        <div class="patients-table-header px-6 py-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Healthcare Provider</h3>
                                <div class="flex items-center space-x-2 text-sm text-gray-500">
                                    <i class="fas fa-user-md"></i>
                                    <span>Medical Professional</span>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="patients-table w-full">
                                <colgroup>
                                    <col style="width: 30%;">
                                    <col style="width: 70%;">
                                </colgroup>
                                <tbody>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Doctor Name</td>
                                        <td>
                                            <input type="text" name="doctor_name"
                                                   value="{{ form.doctor_name.value|default:'' }}"
                                                   class="government-search-input-compact w-full"
                                                   placeholder="Enter doctor's full name" required>
                                            {% if form.doctor_name.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.doctor_name.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Facility Name</td>
                                        <td>
                                            <input type="text" name="facility_name"
                                                   value="{{ form.facility_name.value|default:'' }}"
                                                   class="government-search-input-compact w-full"
                                                   placeholder="Enter healthcare facility name" required>
                                            {% if form.facility_name.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.facility_name.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-semibold text-gray-700">Appointment Notes</td>
                                        <td>
                                            <textarea name="notes" rows="4"
                                                      class="government-search-input-compact w-full resize-none"
                                                      placeholder="Enter any additional notes or special instructions for this appointment">{{ form.notes.value|default:'' }}</textarea>
                                            {% if form.notes.errors %}
                                                <div class="text-red-600 text-xs mt-1">{{ form.notes.errors.0 }}</div>
                                            {% endif %}
                                            <div class="text-xs text-gray-500 mt-1">Optional: Add any special instructions or notes</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Right Sidebar - Additional Information (Same as Patient Form) -->
                <div class="lg:col-span-1">
                    <div class="patients-table-card">
                        <div class="patients-table-header px-6 py-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Appointment Info</h3>
                                <div class="flex items-center space-x-2 text-sm text-gray-500">
                                    <i class="fas fa-info-circle"></i>
                                    <span>Guidelines</span>
                                </div>
                            </div>
                        </div>

                        <div class="p-6 bg-medical-50 border-medical-200">
                            <div class="space-y-4">
                                <div>
                                    <h4 class="text-sm font-semibold text-medical-700 mb-2">Appointment Guidelines</h4>
                                    <ul class="text-xs text-medical-600 space-y-2">
                                        <li class="flex items-start">
                                            <span class="w-1 h-1 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                            <span>Select appropriate appointment type for accurate scheduling</span>
                                        </li>
                                        <li class="flex items-start">
                                            <span class="w-1 h-1 bg-green-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                            <span>Verify patient information before confirming appointment</span>
                                        </li>
                                        <li class="flex items-start">
                                            <span class="w-1 h-1 bg-purple-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                            <span>Include relevant notes for healthcare provider preparation</span>
                                        </li>
                                        <li class="flex items-start">
                                            <span class="w-1 h-1 bg-orange-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                            <span>Emergency appointments require immediate attention</span>
                                        </li>
                                    </ul>
                                </div>

                                <div class="border-t border-medical-200 pt-4">
                                    <h4 class="text-sm font-semibold text-medical-700 mb-2">Appointment Types</h4>
                                    <div class="space-y-2 text-xs text-medical-600">
                                        <div class="flex justify-between">
                                            <span>Consultation</span>
                                            <span class="text-blue-600 font-medium">45-60 min</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Follow-up</span>
                                            <span class="text-green-600 font-medium">30 min</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Emergency</span>
                                            <span class="text-red-600 font-medium">Immediate</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Routine Check-up</span>
                                            <span class="text-purple-600 font-medium">30-45 min</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="border-t border-medical-200 pt-4">
                                    <h4 class="text-sm font-semibold text-medical-700 mb-2">Status Information</h4>
                                    <div class="space-y-1 text-xs text-medical-600">
                                        <div><strong>Scheduled:</strong> Appointment booked</div>
                                        <div><strong>Confirmed:</strong> Patient confirmed attendance</div>
                                        <div><strong>Pending:</strong> Awaiting confirmation</div>
                                        <div><strong>Cancelled:</strong> Appointment cancelled</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Action Buttons Section -->
            <div class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-calendar-check text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-gray-900">Ready to Schedule</h4>
                            <p class="text-xs text-gray-600">Complete all required fields to proceed</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:appointments' %}"
                           class="appointment-cancel-btn flex items-center space-x-2 px-6 py-3 border-2 border-gray-300 rounded-lg text-gray-700 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 font-medium">
                            <i class="fas fa-times"></i>
                            <span>Cancel</span>
                        </a>
                        <button type="submit"
                                class="appointment-submit-btn flex items-center space-x-2 px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 font-semibold">
                            <i class="fas fa-calendar-plus"></i>
                            <span>Schedule Appointment</span>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- QR Scanner Modal for Patient Selection -->
<div id="appointment-qr-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Scan Patient QR Code</h3>
            <button type="button" id="appointment-close-qr-scanner" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div class="relative">
            <video id="appointment-qr-video" class="w-full h-64 bg-gray-100 rounded-lg object-cover"></video>
            <div class="absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none">
                <div class="absolute top-4 left-4 w-6 h-6 border-t-2 border-l-2 border-blue-500"></div>
                <div class="absolute top-4 right-4 w-6 h-6 border-t-2 border-r-2 border-blue-500"></div>
                <div class="absolute bottom-4 left-4 w-6 h-6 border-b-2 border-l-2 border-blue-500"></div>
                <div class="absolute bottom-4 right-4 w-6 h-6 border-b-2 border-r-2 border-blue-500"></div>
            </div>
        </div>

        <div class="mt-4 text-center">
            <p class="text-sm text-gray-600">Point camera at patient's QR code</p>
            <p class="text-xs text-gray-500 mt-1">The patient will be automatically selected when detected</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- jsQR Library for QR Code Detection -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script src="{% static 'assets/js/appointment_patient_selection.js' %}?v=**********"></script>
{% endblock %}
