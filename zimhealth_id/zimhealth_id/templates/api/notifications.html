{% extends 'base.html' %}

{% block title %}Notifications - ZimHealth-ID{% endblock %}

{% block content %}
{% csrf_token %}
<div class="min-h-screen bg-gray-50">
    <!-- Professional Notifications Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between py-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Notifications</h1>
                    <p class="text-sm text-gray-600 mt-1">System alerts and updates</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="bg-medical-50 px-3 py-1 rounded-full">
                        <span class="text-sm font-medium text-medical-700">{{ unread_count }} unread</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Notification Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <button id="mark-all-read" class="inline-flex items-center px-4 py-2 bg-medical-600 text-white text-sm font-medium rounded-lg hover:bg-medical-700 transition-colors">
                        <i class="fas fa-check-double mr-2"></i>
                        Mark All Read
                    </button>
                    <div class="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                        <button class="filter-tab px-3 py-2 text-sm font-medium rounded-md bg-white text-medical-600 shadow-sm" data-filter="all">All</button>
                        <button class="filter-tab px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-white transition-colors" data-filter="unread">Unread</button>
                        <button class="filter-tab px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-white transition-colors" data-filter="info">Info</button>
                        <button class="filter-tab px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-white transition-colors" data-filter="warning">Warnings</button>
                    </div>
                </div>
                <div>
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <i class="fas fa-cog mr-2"></i>
                        Settings
                    </button>
                </div>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="space-y-4">
            {% if notifications %}
                {% for notification in notifications %}
                    <div class="notification-item bg-white rounded-lg shadow-sm border border-gray-200 {% if not notification.is_read %}border-l-4 border-l-medical-500 bg-medical-50{% endif %}"
                         data-id="{{ notification.id }}"
                         data-type="{{ notification.type }}">
                        <div class="p-6">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full flex items-center justify-center
                                        {% if notification.type == 'info' %}bg-blue-100 text-blue-600
                                        {% elif notification.type == 'warning' %}bg-yellow-100 text-yellow-600
                                        {% elif notification.type == 'success' %}bg-green-100 text-green-600
                                        {% elif notification.type == 'error' %}bg-red-100 text-red-600
                                        {% else %}bg-gray-100 text-gray-600{% endif %}">
                                        {% if notification.type == 'info' %}
                                            <i class="fas fa-info-circle"></i>
                                        {% elif notification.type == 'warning' %}
                                            <i class="fas fa-exclamation-triangle"></i>
                                        {% elif notification.type == 'success' %}
                                            <i class="fas fa-check-circle"></i>
                                        {% elif notification.type == 'error' %}
                                            <i class="fas fa-times-circle"></i>
                                        {% else %}
                                            <i class="fas fa-bell"></i>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h4 class="text-lg font-semibold text-gray-900 mb-1">{{ notification.title }}</h4>
                                            <p class="text-gray-700 mb-3">{{ notification.message }}</p>
                                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                                <span class="flex items-center">
                                                    <i class="fas fa-clock mr-1"></i>
                                                    {{ notification.created_at|timesince }} ago
                                                </span>
                                                {% if not notification.is_read %}
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-medical-100 text-medical-800">
                                                        New
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 ml-4">
                                            {% if not notification.is_read %}
                                                <button class="mark-read-btn p-2 text-gray-400 hover:text-medical-600 hover:bg-medical-50 rounded-lg transition-colors"
                                                        data-id="{{ notification.id }}" title="Mark as read">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            {% endif %}
                                            <button class="delete-btn p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                                    data-id="{{ notification.id }}" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-bell-slash text-2xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">No notifications</h3>
                    <p class="text-gray-600">You're all caught up! No new notifications at this time.</p>
                </div>
            {% endif %}
        </div>

        <!-- Load More Button -->
        {% if notifications|length >= 10 %}
            <div class="text-center mt-8">
                <button id="load-more" class="inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <i class="fas fa-chevron-down mr-2"></i>
                    Load More Notifications
                </button>
            </div>
        {% endif %}
    </div>
</div>

<script>
// Notification functionality
document.addEventListener('DOMContentLoaded', function() {
    // Mark individual notification as read
    document.querySelectorAll('.mark-read-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const notificationId = this.dataset.id;
            markNotificationRead(notificationId);
        });
    });

    // Mark all notifications as read
    document.getElementById('mark-all-read')?.addEventListener('click', function() {
        // Implementation for marking all as read
        console.log('Mark all as read');
    });

    // Filter notifications
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active styles from all tabs
            document.querySelectorAll('.filter-tab').forEach(t => {
                t.classList.remove('bg-white', 'text-medical-600', 'shadow-sm');
                t.classList.add('text-gray-600', 'hover:text-gray-900', 'hover:bg-white');
            });

            // Add active styles to clicked tab
            this.classList.remove('text-gray-600', 'hover:text-gray-900', 'hover:bg-white');
            this.classList.add('bg-white', 'text-medical-600', 'shadow-sm');

            const filter = this.dataset.filter;
            filterNotifications(filter);
        });
    });
});

function markNotificationRead(notificationId) {
    fetch(`{% url 'api:mark_notification_read_ajax' 0 %}`.replace('0', notificationId), {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationItem = document.querySelector(`[data-id="${notificationId}"]`);
            // Remove unread styling
            notificationItem.classList.remove('border-l-4', 'border-l-medical-500', 'bg-medical-50');
            notificationItem.classList.add('border-gray-200');

            // Remove mark as read button
            notificationItem.querySelector('.mark-read-btn')?.remove();

            // Remove "New" badge
            notificationItem.querySelector('.bg-medical-100')?.remove();

            // Update unread count
            const unreadCountEl = document.querySelector('.text-medical-700');
            if (unreadCountEl) {
                const currentCount = parseInt(unreadCountEl.textContent.match(/\d+/)[0]);
                const newCount = Math.max(0, currentCount - 1);
                unreadCountEl.textContent = `${newCount} unread`;
            }
        }
    })
    .catch(error => console.error('Error:', error));
}

function filterNotifications(filter) {
    const notifications = document.querySelectorAll('.notification-item');

    notifications.forEach(notification => {
        const type = notification.dataset.type;
        const isUnread = notification.classList.contains('border-l-medical-500');

        let show = true;

        if (filter === 'unread' && !isUnread) {
            show = false;
        } else if (filter !== 'all' && filter !== 'unread' && type !== filter) {
            show = false;
        }

        notification.style.display = show ? 'block' : 'none';
    });
}
</script>
{% endblock %}
