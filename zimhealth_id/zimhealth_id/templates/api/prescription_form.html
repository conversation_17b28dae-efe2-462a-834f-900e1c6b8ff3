{% extends 'base.html' %}

{% block title %}{{ title }} - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<link rel="stylesheet" href="{% static 'assets/css/patients.css' %}?v=**********">
{% endblock %}

{% block content %}
<div class="patients-container">
    <!-- Professional Prescription Header - EXACT Mirror of Patient Form -->
    <div class="patients-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
                        <p class="text-gray-600 mt-1 text-sm">
                            {% if medical_record %}For {{ medical_record.patient.full_name }}{% else %}Create a new prescription{% endif %}
                        </p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:prescriptions' %}" class="government-filter-button">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Prescriptions
                        </a>
                        <div class="add-patient-button flex items-center space-x-2">
                            <i class="fas fa-pills"></i>
                            <span>New Prescription</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Prescription Content - Layout as per wireframe -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 relative z-10">
        <form method="post">
            {% csrf_token %}
            
            <!-- Medical Record Selection -->
            {% if not medical_record %}
            <div class="patients-table-card mb-6">
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Medical Record Selection</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-file-medical"></i>
                            <span>Select Record</span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Search Medical Record
                        </label>
                        <div class="relative">
                            <input type="text" id="record-search" 
                                   class="government-search-input-compact w-full pl-10"
                                   placeholder="Search by patient name or record">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    <select name="medical_record" class="government-search-input-compact w-full" required>
                        <option value="">Select a medical record</option>
                        {% for choice in form.medical_record.field.queryset %}
                            <option value="{{ choice.id }}" {% if form.medical_record.value == choice.id %}selected{% endif %}>
                                {{ choice.patient.full_name }} - {{ choice.date|date:"M d, Y" }} - {{ choice.diagnosis|truncatechars:50 }}
                            </option>
                        {% endfor %}
                    </select>
                    {% if form.medical_record.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            {% for error in form.medical_record.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Medication Information -->
            <div class="patients-table-card mb-6">
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Medication Information</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-pills"></i>
                            <span>Drug Details</span>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 30%;">
                            <col style="width: 70%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Medication Name *</td>
                                <td>
                                    <input type="text" name="medication" value="{{ form.medication.value|default:'' }}" class="government-search-input-compact w-full" placeholder="Enter medication name" required>
                                    {% if form.medication.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.medication.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Dosage *</td>
                                <td>
                                    <input type="text" name="dosage" value="{{ form.dosage.value|default:'' }}" class="government-search-input-compact w-full" placeholder="e.g., 500mg" required>
                                    {% if form.dosage.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.dosage.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Frequency *</td>
                                <td>
                                    <select name="frequency" class="government-search-input-compact w-full" required>
                                        <option value="">Select frequency</option>
                                        <option value="once_daily" {% if form.frequency.value == 'once_daily' %}selected{% endif %}>Once Daily</option>
                                        <option value="twice_daily" {% if form.frequency.value == 'twice_daily' %}selected{% endif %}>Twice Daily</option>
                                        <option value="three_times_daily" {% if form.frequency.value == 'three_times_daily' %}selected{% endif %}>Three Times Daily</option>
                                        <option value="four_times_daily" {% if form.frequency.value == 'four_times_daily' %}selected{% endif %}>Four Times Daily</option>
                                        <option value="every_4_hours" {% if form.frequency.value == 'every_4_hours' %}selected{% endif %}>Every 4 Hours</option>
                                        <option value="every_6_hours" {% if form.frequency.value == 'every_6_hours' %}selected{% endif %}>Every 6 Hours</option>
                                        <option value="every_8_hours" {% if form.frequency.value == 'every_8_hours' %}selected{% endif %}>Every 8 Hours</option>
                                        <option value="every_12_hours" {% if form.frequency.value == 'every_12_hours' %}selected{% endif %}>Every 12 Hours</option>
                                        <option value="as_needed" {% if form.frequency.value == 'as_needed' %}selected{% endif %}>As Needed</option>
                                        <option value="before_meals" {% if form.frequency.value == 'before_meals' %}selected{% endif %}>Before Meals</option>
                                        <option value="after_meals" {% if form.frequency.value == 'after_meals' %}selected{% endif %}>After Meals</option>
                                        <option value="at_bedtime" {% if form.frequency.value == 'at_bedtime' %}selected{% endif %}>At Bedtime</option>
                                        <option value="other" {% if form.frequency.value == 'other' %}selected{% endif %}>Other</option>
                                    </select>
                                    {% if form.frequency.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.frequency.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Duration *</td>
                                <td>
                                    <input type="text" name="duration" value="{{ form.duration.value|default:'' }}" class="government-search-input-compact w-full" placeholder="e.g., 7 days" required>
                                    {% if form.duration.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.duration.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Quantity Prescribed</td>
                                <td>
                                    <input type="number" name="quantity_prescribed" value="{{ form.quantity_prescribed.value|default:'' }}" class="government-search-input-compact w-full" placeholder="Quantity">
                                    {% if form.quantity_prescribed.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.quantity_prescribed.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Prescription Details -->
            <div class="patients-table-card mb-6">
                <div class="patients-table-header px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Prescription Details</h3>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-clipboard"></i>
                            <span>Schedule & Status</span>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="patients-table w-full">
                        <colgroup>
                            <col style="width: 30%;">
                            <col style="width: 70%;">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="font-semibold text-gray-700">Start Date *</td>
                                <td>
                                    <input type="date" name="start_date" value="{{ form.start_date.value|default:'' }}" class="government-search-input-compact w-full" required>
                                    {% if form.start_date.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.start_date.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">End Date</td>
                                <td>
                                    <input type="date" name="end_date" value="{{ form.end_date.value|default:'' }}" class="government-search-input-compact w-full">
                                    {% if form.end_date.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.end_date.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Refills Allowed</td>
                                <td>
                                    <input type="number" name="refills_allowed" value="{{ form.refills_allowed.value|default:'0' }}" class="government-search-input-compact w-full" min="0" placeholder="0">
                                    {% if form.refills_allowed.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.refills_allowed.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Status</td>
                                <td>
                                    <select name="status" class="government-search-input-compact w-full">
                                        <option value="active" {% if form.status.value == 'active' %}selected{% endif %}>Active</option>
                                        <option value="completed" {% if form.status.value == 'completed' %}selected{% endif %}>Completed</option>
                                        <option value="discontinued" {% if form.status.value == 'discontinued' %}selected{% endif %}>Discontinued</option>
                                    </select>
                                    {% if form.status.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.status.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-gray-700">Special Instructions</td>
                                <td>
                                    <textarea name="instructions" class="government-search-input-compact w-full" rows="2" placeholder="Special instructions...">{{ form.instructions.value|default:'' }}</textarea>
                                    {% if form.instructions.errors %}
                                        <div class="text-red-600 text-xs mt-1">
                                            {% for error in form.instructions.errors %}
                                                <p>{{ error }}</p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Non-field Errors -->
            {% if form.non_field_errors %}
                <div class="patients-table-card mb-6">
                    <div class="p-6 bg-red-50">
                        {% for error in form.non_field_errors %}
                            <p class="text-red-800 text-sm flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>{{ error }}
                            </p>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6">
                <button type="submit" class="add-patient-button flex-1 flex items-center justify-center space-x-2">
                    <i class="fas fa-prescription-bottle-alt"></i>
                    <span>Create Prescription</span>
                </button>
                
                <a href="{% url 'api:prescriptions' %}" class="government-filter-button flex-1 flex items-center justify-center space-x-2">
                    <i class="fas fa-times"></i>
                    <span>Cancel</span>
                </a>
            </div>
        </form>
    </div>
</div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Medical record search functionality
document.getElementById('record-search')?.addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const select = document.querySelector('select[name="medical_record"]');
    const options = select.querySelectorAll('option');
    
    options.forEach(option => {
        if (option.value === '') return; // Skip the default option
        const text = option.textContent.toLowerCase();
        option.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Auto-calculate end date based on duration
document.querySelector('input[name="duration"]')?.addEventListener('input', function(e) {
    const duration = e.target.value.toLowerCase();
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');
    
    if (startDateInput.value && duration) {
        const startDate = new Date(startDateInput.value);
        let endDate = new Date(startDate);
        
        // Simple duration parsing
        if (duration.includes('day')) {
            const days = parseInt(duration.match(/\d+/)?.[0] || 0);
            endDate.setDate(startDate.getDate() + days);
        } else if (duration.includes('week')) {
            const weeks = parseInt(duration.match(/\d+/)?.[0] || 0);
            endDate.setDate(startDate.getDate() + (weeks * 7));
        } else if (duration.includes('month')) {
            const months = parseInt(duration.match(/\d+/)?.[0] || 0);
            endDate.setMonth(startDate.getMonth() + months);
        }
        
        if (endDate > startDate) {
            endDateInput.value = endDate.toISOString().split('T')[0];
        }
    }
});
</script>
{% endblock %}
