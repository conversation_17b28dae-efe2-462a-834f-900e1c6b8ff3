<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZimHealth-ID Card - {{ patient.full_name }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Print-optimized ID Card Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            padding: 20px;
            color: #333;
        }

        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1e40af;
            padding-bottom: 20px;
        }

        .print-header h1 {
            color: #1e40af;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .print-header p {
            color: #666;
            font-size: 14px;
        }

        /* ID Card Layout */
        .id-card-container {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
        }

        .id-card {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            border-radius: 12px;
            padding: 20px;
            color: white;
            width: 350px;
            height: 220px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        }

        .id-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: cardGlow 3s ease-in-out infinite;
        }

        @keyframes cardGlow {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }

        .card-logo {
            font-size: 24px;
            font-weight: bold;
        }

        .card-type {
            font-size: 12px;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .card-body {
            position: relative;
            z-index: 2;
        }

        .patient-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .patient-id {
            font-size: 16px;
            font-family: 'Courier New', monospace;
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 10px;
            border-radius: 6px;
            display: inline-block;
            margin-bottom: 15px;
        }

        .card-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 12px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .detail-icon {
            width: 14px;
            text-align: center;
        }

        /* QR Code Section */
        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 200px;
        }

        .qr-container {
            background: white;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .qr-code {
            width: 150px;
            height: 150px;
            background: #f3f4f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #e5e7eb;
        }

        .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 6px;
        }

        .qr-placeholder {
            text-align: center;
            color: #6b7280;
            font-size: 12px;
        }

        .qr-label {
            text-align: center;
            font-size: 12px;
            color: #666;
            font-weight: 600;
        }

        /* Patient Details Table */
        .patient-details {
            margin-top: 30px;
        }

        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .details-table th,
        .details-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .details-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            width: 30%;
        }

        .details-table td {
            color: #111827;
        }

        .blood-type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 12px;
            display: inline-block;
            font-family: monospace;
        }

        .blood-type-badge.a-positive {
            background: #fef2f2;
            color: #7f1d1d;
            border: 1px solid #fecaca;
        }

        .blood-type-badge.unknown {
            background: #f3f4f6;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        /* Print Controls */
        .print-controls {
            text-align: center;
            margin-bottom: 20px;
        }

        .print-btn {
            background: linear-gradient(135deg, #1e40af, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.2s ease;
        }

        .print-btn:hover {
            background: linear-gradient(135deg, #1d4ed8, #2563eb);
            transform: translateY(-1px);
        }

        .back-btn {
            background: #6b7280;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            margin: 0 10px;
            transition: all 0.2s ease;
            display: inline-block;
        }

        .back-btn:hover {
            background: #4b5563;
            transform: translateY(-1px);
        }

        /* Print Styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }

            .print-container {
                box-shadow: none;
                padding: 0;
                max-width: none;
            }

            .print-controls {
                display: none;
            }

            .id-card-container {
                page-break-inside: avoid;
            }

            .patient-details {
                page-break-inside: avoid;
            }

            .id-card {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .id-card-container {
                flex-direction: column;
                align-items: center;
            }

            .id-card {
                width: 100%;
                max-width: 350px;
            }

            .qr-section {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <!-- Print Header -->
        <div class="print-header">
            <h1><i class="fas fa-id-card"></i> ZimHealth-ID Card</h1>
            <p>Official Healthcare Identification Document</p>
        </div>

        <!-- Print Controls (hidden when printing) -->
        <div class="print-controls">
            <button onclick="window.print()" class="print-btn">
                <i class="fas fa-print"></i> Print ID Card
            </button>
            <a href="/api/patients/{{ patient.zimhealth_id }}/" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Patient
            </a>
        </div>

        <!-- ID Card Layout -->
        <div class="id-card-container">
            <!-- ID Card -->
            <div class="id-card">
                <div class="card-header">
                    <div class="card-logo">ZimHealth-ID</div>
                    <div class="card-type">Healthcare ID</div>
                </div>
                
                <div class="card-body">
                    <div class="patient-name">{{ patient.full_name }}</div>
                    <div class="patient-id">{{ patient.zimhealth_id }}</div>
                    
                    <div class="card-details">
                        <div class="detail-item">
                            <i class="fas fa-calendar detail-icon"></i>
                            <span>{{ patient.date_of_birth|date:"M d, Y" }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-venus-mars detail-icon"></i>
                            <span>{{ patient.get_gender_display }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-tint detail-icon"></i>
                            <span>{{ patient.blood_type|default:"Unknown" }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-phone detail-icon"></i>
                            <span>{{ patient.phone_number }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-exclamation-triangle detail-icon"></i>
                            <span>{{ patient.emergency_contact|default:"N/A" }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock detail-icon"></i>
                            <span>{{ patient.registration_date|date:"M Y" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="qr-section">
                <div class="qr-container">
                    <div class="qr-code">
                        {% if patient.qr_code %}
                            <img src="{{ patient.qr_code.url }}" alt="Patient QR Code">
                        {% else %}
                            <div class="qr-placeholder">
                                <i class="fas fa-qrcode" style="font-size: 32px; margin-bottom: 8px; display: block;"></i>
                                <div>QR Code</div>
                                <div style="font-size: 10px; margin-top: 4px;">{{ patient.zimhealth_id }}</div>
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="qr-label">Scan for Patient Info</div>
            </div>
        </div>

        <!-- Detailed Patient Information -->
        <div class="patient-details">
            <h2 style="color: #1e40af; margin-bottom: 15px;">
                <i class="fas fa-user-circle"></i> Patient Information
            </h2>
            
            <table class="details-table">
                <tr>
                    <th><i class="fas fa-user"></i> Full Name</th>
                    <td>{{ patient.full_name }}</td>
                </tr>
                <tr>
                    <th><i class="fas fa-id-card"></i> ZimHealth-ID</th>
                    <td style="font-family: 'Courier New', monospace; font-weight: 600;">{{ patient.zimhealth_id }}</td>
                </tr>
                <tr>
                    <th><i class="fas fa-id-badge"></i> National ID</th>
                    <td>{{ patient.national_id|default:"Not provided" }}</td>
                </tr>
                <tr>
                    <th><i class="fas fa-calendar"></i> Date of Birth</th>
                    <td>{{ patient.date_of_birth|date:"F d, Y" }} (Age: {{ patient.age }})</td>
                </tr>
                <tr>
                    <th><i class="fas fa-venus-mars"></i> Gender</th>
                    <td>{{ patient.get_gender_display }}</td>
                </tr>
                <tr>
                    <th><i class="fas fa-tint"></i> Blood Type</th>
                    <td>
                        {% if patient.blood_type %}
                            <span class="blood-type-badge {{ patient.blood_type|lower|slugify }}">{{ patient.blood_type }}</span>
                        {% else %}
                            <span class="blood-type-badge unknown">Unknown</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th><i class="fas fa-phone"></i> Phone Number</th>
                    <td>{{ patient.phone_number }}</td>
                </tr>
                <tr>
                    <th><i class="fas fa-envelope"></i> Email</th>
                    <td>{{ patient.email|default:"Not provided" }}</td>
                </tr>
                <tr>
                    <th><i class="fas fa-map-marker-alt"></i> Address</th>
                    <td>{{ patient.address|default:"Not provided" }}</td>
                </tr>
                <tr>
                    <th><i class="fas fa-exclamation-triangle"></i> Emergency Contact</th>
                    <td>{{ patient.emergency_contact|default:"Not provided" }}</td>
                </tr>
                <tr>
                    <th><i class="fas fa-allergies"></i> Known Allergies</th>
                    <td>
                        {% if patient.allergies %}
                            {{ patient.allergies|join:", " }}
                        {% else %}
                            None reported
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th><i class="fas fa-clock"></i> Registration Date</th>
                    <td>{{ patient.registration_date|date:"F d, Y \a\t g:i A" }}</td>
                </tr>
            </table>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 12px;">
            <p><strong>ZimHealth-ID</strong> - Zimbabwe Healthcare Identification System</p>
            <p>This document contains confidential medical information. Handle with care.</p>
            <p>Generated on {{ "now"|date:"F d, Y \a\t g:i A" }}</p>
        </div>
    </div>

    <script>
        // Auto-print functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Check if this was opened for printing (from the modal)
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('print') === 'true') {
                setTimeout(() => {
                    window.print();
                }, 1000);
            }
        });

        // Handle print button
        function printCard() {
            window.print();
        }

        // Handle after print
        window.addEventListener('afterprint', function() {
            // Optional: Close window after printing if opened from modal
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('print') === 'true') {
                setTimeout(() => {
                    window.close();
                }, 2000);
            }
        });
    </script>
</body>
</html>