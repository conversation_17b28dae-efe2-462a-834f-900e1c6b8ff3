{% extends 'base.html' %}
{% load static %}

{% block title %}Reschedule Appointment - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/appointments.css' %}?v=3">
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="{% url 'api:appointments' %}" class="text-gray-600 hover:text-gray-900 transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </a>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Reschedule Appointment</h1>
                        <p class="text-sm text-gray-600">{{ appointment.patient.full_name }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-gray-500">Current: {{ appointment.date|date:"M d, Y" }} at {{ appointment.time|time:"H:i" }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Current Appointment Info -->
            <div class="bg-blue-50 px-6 py-4 border-b border-blue-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-blue-900">Current Appointment</h3>
                        <div class="mt-2 space-y-1">
                            <p class="text-sm text-blue-700">
                                <i class="fas fa-user mr-2"></i>{{ appointment.patient.full_name }} ({{ appointment.patient.zimhealth_id }})
                            </p>
                            <p class="text-sm text-blue-700">
                                <i class="fas fa-calendar mr-2"></i>{{ appointment.date|date:"l, F d, Y" }}
                            </p>
                            <p class="text-sm text-blue-700">
                                <i class="fas fa-clock mr-2"></i>{{ appointment.time|time:"H:i" }}
                            </p>
                            <p class="text-sm text-blue-700">
                                <i class="fas fa-user-md mr-2"></i>{{ appointment.doctor_name }}
                            </p>
                            <p class="text-sm text-blue-700">
                                <i class="fas fa-hospital mr-2"></i>{{ appointment.facility_name }}
                            </p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ appointment.get_appointment_type_display }}
                        </span>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                {{ appointment.get_priority_display }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reschedule Form -->
            <div class="p-6">
                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- New Date -->
                        <div>
                            <label for="new_date" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-alt mr-2 text-blue-600"></i>New Date
                            </label>
                            <input type="date" 
                                   id="new_date" 
                                   name="new_date" 
                                   required
                                   min="{{ today|date:'Y-m-d' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>

                        <!-- New Time -->
                        <div>
                            <label for="new_time" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-clock mr-2 text-blue-600"></i>New Time
                            </label>
                            <input type="time" 
                                   id="new_time" 
                                   name="new_time" 
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>
                    </div>

                    <!-- Reason for Rescheduling -->
                    <div>
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-comment-alt mr-2 text-blue-600"></i>Reason for Rescheduling
                        </label>
                        <textarea id="reason" 
                                  name="reason" 
                                  rows="3" 
                                  placeholder="Please provide a reason for rescheduling this appointment..."
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"></textarea>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                        <a href="{% url 'api:appointments' %}" 
                           class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </a>
                        
                        <button type="submit" 
                                class="inline-flex items-center px-8 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            <i class="fas fa-calendar-check mr-2"></i>Reschedule Appointment
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Time Slots -->
        <div class="mt-8 bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-clock mr-2 text-blue-600"></i>Quick Time Slots
                </h3>
                <p class="text-sm text-gray-600 mt-1">Click on a time slot to quickly select it</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-3 md:grid-cols-6 gap-3">
                    <button type="button" onclick="setTime('08:00')" class="time-slot-btn">08:00</button>
                    <button type="button" onclick="setTime('09:00')" class="time-slot-btn">09:00</button>
                    <button type="button" onclick="setTime('10:00')" class="time-slot-btn">10:00</button>
                    <button type="button" onclick="setTime('11:00')" class="time-slot-btn">11:00</button>
                    <button type="button" onclick="setTime('14:00')" class="time-slot-btn">14:00</button>
                    <button type="button" onclick="setTime('15:00')" class="time-slot-btn">15:00</button>
                    <button type="button" onclick="setTime('16:00')" class="time-slot-btn">16:00</button>
                    <button type="button" onclick="setTime('17:00')" class="time-slot-btn">17:00</button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.time-slot-btn {
    @apply px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors;
}

.time-slot-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>

<script>
function setTime(time) {
    document.getElementById('new_time').value = time;
    
    // Visual feedback
    document.querySelectorAll('.time-slot-btn').forEach(btn => {
        btn.classList.remove('bg-blue-100', 'border-blue-300', 'text-blue-700');
        btn.classList.add('bg-gray-100', 'border-gray-300', 'text-gray-700');
    });
    
    event.target.classList.remove('bg-gray-100', 'border-gray-300', 'text-gray-700');
    event.target.classList.add('bg-blue-100', 'border-blue-300', 'text-blue-700');
}

// Set minimum date to today
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('new_date').min = today;
});
</script>
{% endblock %}

{% block extra_js %}
<script src="{% static 'assets/js/appointments.js' %}?v=3"></script>
{% endblock %}
