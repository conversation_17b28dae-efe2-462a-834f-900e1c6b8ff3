{% extends 'base.html' %}

{% block title %}Set New Password - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-health-50 to-medical-50">
    <div class="max-w-md w-full space-y-8">
        {% if validlink %}
            <!-- Header -->
            <div class="text-center">
                <div class="mx-auto h-16 w-16 bg-health-100 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-lock text-health-600 text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Set New Password</h2>
                <p class="text-gray-600">Create a strong password for your account</p>
            </div>

            <!-- Password Form -->
            <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
                <form method="post" class="space-y-6">
                    {% csrf_token %}

                    <!-- New Password Field -->
                    <div>
                        <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-key text-health-500 mr-2"></i>New Password
                        </label>
                        <div class="relative">
                            <input type="password" name="{{ form.new_password1.name }}" id="{{ form.new_password1.id_for_label }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                                   placeholder="Enter your new password" required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" onclick="togglePassword('{{ form.new_password1.id_for_label }}')" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        {% if form.new_password1.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.new_password1.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Confirm Password Field -->
                    <div>
                        <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-key text-health-500 mr-2"></i>Confirm New Password
                        </label>
                        <div class="relative">
                            <input type="password" name="{{ form.new_password2.name }}" id="{{ form.new_password2.id_for_label }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                                   placeholder="Confirm your new password" required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" onclick="togglePassword('{{ form.new_password2.id_for_label }}')" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        {% if form.new_password2.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.new_password2.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Non-field Errors -->
                    {% if form.non_field_errors %}
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            {% for error in form.non_field_errors %}
                                <p class="text-red-800 text-sm flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Password Requirements -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-3"></i>
                            <div>
                                <p class="text-blue-800 font-medium text-sm mb-2">Password Requirements:</p>
                                <ul class="text-blue-700 text-sm space-y-1">
                                    <li class="flex items-center"><i class="fas fa-check text-blue-500 mr-2 text-xs"></i>At least 8 characters long</li>
                                    <li class="flex items-center"><i class="fas fa-check text-blue-500 mr-2 text-xs"></i>Not similar to personal information</li>
                                    <li class="flex items-center"><i class="fas fa-check text-blue-500 mr-2 text-xs"></i>Not a commonly used password</li>
                                    <li class="flex items-center"><i class="fas fa-check text-blue-500 mr-2 text-xs"></i>Not entirely numeric</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                                class="w-full bg-health-600 text-white py-3 px-4 rounded-lg hover:bg-health-700 focus:ring-2 focus:ring-health-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-save"></i>
                            <span>Set New Password</span>
                        </button>
                    </div>
                </form>
            </div>
        {% else %}
            <!-- Invalid Link -->
            <div class="text-center">
                <div class="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Invalid Reset Link</h2>
                <p class="text-gray-600">This password reset link is no longer valid</p>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100 text-center">
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <i class="fas fa-times-circle text-red-500 mt-0.5 mr-3"></i>
                        <div class="text-left">
                            <p class="text-red-800 font-medium text-sm mb-2">This link is invalid because:</p>
                            <ul class="text-red-700 text-sm space-y-1">
                                <li>• The link has expired</li>
                                <li>• The link has already been used</li>
                                <li>• The link was malformed</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <a href="{% url 'zhid_auth:password_reset' %}"
                   class="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                    <i class="fas fa-redo"></i>
                    <span>Request New Reset Link</span>
                </a>
            </div>
        {% endif %}

        <!-- Back to Login -->
        <div class="text-center">
            <p class="text-sm text-gray-600">
                Remember your password?
                <a href="{% url 'zhid_auth:login' %}" class="text-medical-600 hover:text-medical-700 font-medium">
                    Sign in here
                </a>
            </p>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const toggleIcon = passwordField.parentElement.querySelector('i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}
</script>
{% endblock %}
