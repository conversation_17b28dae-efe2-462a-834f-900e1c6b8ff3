{% extends 'base.html' %}

{% block title %}Verify Email - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-health-50 to-medical-50">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-health-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-envelope-circle-check text-health-600 text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Verify Your Email</h2>
            <p class="text-gray-600">Resend verification link to secure your account</p>
        </div>

        <!-- Verification Form -->
        <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Email Field -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope text-health-500 mr-2"></i>Email Address
                    </label>
                    <input type="email" name="{{ form.email.name }}" id="{{ form.email.id_for_label }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                           placeholder="Enter your email address" required>
                    {% if form.email.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            {% for error in form.email.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Non-field Errors -->
                {% if form.non_field_errors %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        {% for error in form.non_field_errors %}
                            <p class="text-red-800 text-sm flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>{{ error }}
                            </p>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Benefits Info -->
                <div class="bg-health-50 border border-health-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-shield-check text-health-500 mt-0.5 mr-3"></i>
                        <div>
                            <p class="text-health-800 font-medium text-sm mb-2">Why verify your email?</p>
                            <ul class="text-health-700 text-sm space-y-1">
                                <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2 text-xs"></i>Secure your account</li>
                                <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2 text-xs"></i>Receive important notifications</li>
                                <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2 text-xs"></i>Enable password reset</li>
                                <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2 text-xs"></i>Access all features</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div>
                    <button type="submit"
                            class="w-full bg-health-600 text-white py-3 px-4 rounded-lg hover:bg-health-700 focus:ring-2 focus:ring-health-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-paper-plane"></i>
                        <span>Send Verification Email</span>
                    </button>
                </div>

                <!-- Additional Links -->
                <div class="space-y-4">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Already verified?</span>
                        </div>
                    </div>

                    <div class="text-center">
                        <a href="{% url 'zhid_auth:login' %}"
                           class="w-full bg-medical-600 text-white py-3 px-4 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Sign In</span>
                        </a>
                    </div>

                    <div class="text-center">
                        <p class="text-sm text-gray-600">
                            Need to create an account?
                            <a href="{% url 'zhid_auth:register' %}" class="text-health-600 hover:text-health-700 font-medium">
                                Register here
                            </a>
                        </p>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
