{% extends 'base.html' %}

{% block title %}Register - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-health-50 to-medical-50">
    <div class="max-w-2xl w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-health-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-user-plus text-health-600 text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Create Your Account</h2>
            <p class="text-gray-600">Join <PERSON>-<PERSON> to manage your healthcare identity</p>
        </div>

        <!-- Registration Form -->
        <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Name Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user text-health-500 mr-2"></i>First Name
                        </label>
                        <input type="text" name="{{ form.first_name.name }}" id="{{ form.first_name.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                               placeholder="Enter your first name" required>
                        {% if form.first_name.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.first_name.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user text-health-500 mr-2"></i>Last Name
                        </label>
                        <input type="text" name="{{ form.last_name.name }}" id="{{ form.last_name.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                               placeholder="Enter your last name" required>
                        {% if form.last_name.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.last_name.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Username and Email -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-at text-health-500 mr-2"></i>Username
                        </label>
                        <input type="text" name="{{ form.username.name }}" id="{{ form.username.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                               placeholder="Choose a username" required>
                        {% if form.username.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.username.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-envelope text-health-500 mr-2"></i>Email Address
                        </label>
                        <input type="email" name="{{ form.email.name }}" id="{{ form.email.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                               placeholder="Enter your email address" required>
                        {% if form.email.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.email.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Phone Number -->
                <div>
                    <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-phone text-health-500 mr-2"></i>Phone Number <span class="text-gray-500">(Optional)</span>
                    </label>
                    <input type="tel" name="{{ form.phone_number.name }}" id="{{ form.phone_number.id_for_label }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                           placeholder="Enter your phone number">
                    {% if form.phone_number.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            {% for error in form.phone_number.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Password Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock text-health-500 mr-2"></i>Password
                        </label>
                        <div class="relative">
                            <input type="password" name="{{ form.password1.name }}" id="{{ form.password1.id_for_label }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                                   placeholder="Create a strong password" required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" onclick="togglePassword('{{ form.password1.id_for_label }}')" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        {% if form.password1.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.password1.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock text-health-500 mr-2"></i>Confirm Password
                        </label>
                        <div class="relative">
                            <input type="password" name="{{ form.password2.name }}" id="{{ form.password2.id_for_label }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-health-500 focus:border-health-500 transition-colors placeholder-gray-400"
                                   placeholder="Confirm your password" required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" onclick="togglePassword('{{ form.password2.id_for_label }}')" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        {% if form.password2.errors %}
                            <div class="mt-2 text-sm text-red-600">
                                {% for error in form.password2.errors %}
                                    <p class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Password Requirements -->
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Password Requirements:</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2"></i>At least 8 characters long</li>
                        <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2"></i>Cannot be too similar to your personal information</li>
                        <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2"></i>Cannot be a commonly used password</li>
                        <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2"></i>Cannot be entirely numeric</li>
                    </ul>
                </div>

                <!-- Non-field Errors -->
                {% if form.non_field_errors %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        {% for error in form.non_field_errors %}
                            <p class="text-red-800 text-sm flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>{{ error }}
                            </p>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Submit Button -->
                <div>
                    <button type="submit"
                            class="w-full bg-health-600 text-white py-3 px-4 rounded-lg hover:bg-health-700 focus:ring-2 focus:ring-health-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-user-plus"></i>
                        <span>Create Account</span>
                    </button>
                </div>

                <!-- Login Link -->
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">Already have an account?</span>
                    </div>
                </div>

                <div class="text-center">
                    <a href="{% url 'zhid_auth:login' %}"
                       class="w-full bg-medical-600 text-white py-3 px-4 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Sign In Instead</span>
                    </a>
                </div>
            </form>
        </div>

        <!-- Terms Notice -->
        <div class="text-center text-sm text-gray-500">
            <p>By creating an account, you agree to our
                <a href="#" class="text-health-600 hover:text-health-700">Terms of Service</a> and
                <a href="#" class="text-health-600 hover:text-health-700">Privacy Policy</a>
            </p>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const toggleIcon = passwordField.parentElement.querySelector('i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}
</script>
{% endblock %}
