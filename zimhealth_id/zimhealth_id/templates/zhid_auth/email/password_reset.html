<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your ZimHealth-ID Password</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #dc3545;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 5px 5px;
        }
        .button {
            display: inline-block;
            background-color: #dc3545;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .security-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏥 ZimHealth-ID</h1>
        <h2>Password Reset Request</h2>
    </div>
    
    <div class="content">
        <h3>Hello {{ user.first_name|default:user.username }}!</h3>
        
        <p>We received a request to reset the password for your ZimHealth-ID account associated with this email address.</p>
        
        <p>If you made this request, click the button below to reset your password:</p>
        
        <div style="text-align: center;">
            <a href="{{ protocol }}://{{ domain }}{% url 'zhid_auth:password_reset_confirm' uidb64=uid token=token %}" class="button">Reset My Password</a>
        </div>
        
        <p>Or copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background-color: #e9ecef; padding: 10px; border-radius: 3px;">
            {{ protocol }}://{{ domain }}{% url 'zhid_auth:password_reset_confirm' uidb64=uid token=token %}
        </p>
        
        <div class="warning">
            <strong>⚠️ Important Security Information:</strong>
            <ul>
                <li>This password reset link will expire soon for security reasons</li>
                <li>If you didn't request this password reset, please ignore this email</li>
                <li>Your current password will remain unchanged until you create a new one</li>
                <li>For security reasons, do not share this link with anyone</li>
            </ul>
        </div>
        
        <div class="security-info">
            <strong>🔒 Security Tips:</strong>
            <ul>
                <li>Choose a strong, unique password</li>
                <li>Use a combination of letters, numbers, and symbols</li>
                <li>Don't reuse passwords from other accounts</li>
                <li>Consider using a password manager</li>
            </ul>
        </div>
        
        <p><strong>Didn't request this reset?</strong><br>
        If you didn't request a password reset, your account is still secure. You can safely ignore this email, and your password will not be changed.</p>
        
        <p>If you have any concerns about your account security or need assistance, please contact our support team immediately.</p>
        
        <p>Best regards,<br>
        The ZimHealth-ID Security Team</p>
    </div>
    
    <div class="footer">
        <p>© 2024 ZimHealth-ID. All rights reserved.</p>
        <p>This is an automated security message. Please do not reply to this email.</p>
    </div>
</body>
</html>
