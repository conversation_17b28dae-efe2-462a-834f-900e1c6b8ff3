# Authentication Templates Directory

This directory contains Django templates for authentication-related pages in the ZimHealth-ID application.

## Purpose
- Store HTML templates for user authentication flows
- Organize login, registration, and password management pages
- Include email templates for authentication processes
- Provide consistent UI/UX for auth-related functionality

## Template Organization
```
zhid_auth/
├── login.html              # User login page
├── register.html           # User registration page
├── logout.html             # Logout confirmation page
├── password_reset.html     # Password reset request page
├── password_reset_done.html # Password reset email sent confirmation
├── password_reset_confirm.html # Password reset form
├── password_reset_complete.html # Password reset success page
├── password_change.html    # Password change form (for logged-in users)
├── password_change_done.html # Password change success page
├── profile.html           # User profile page
├── verify_email.html      # Email verification page
└── email/                 # Email templates subdirectory
    ├── verification.html  # Email verification template
    └── password_reset.html # Password reset email template
```

## Template Context
These templates typically receive context variables such as:
- `form`: Authentication forms (login, registration, password reset)
- `user`: Current user object (when authenticated)
- `next`: Redirect URL after successful authentication
- `site_name`: Application name for branding
- `error_messages`: Form validation errors

## Usage Example
```python
# In views.py
from django.shortcuts import render
from django.contrib.auth.forms import AuthenticationForm

def login_view(request):
    form = AuthenticationForm()
    return render(request, 'zhid_auth/login.html', {'form': form})
```

## Template Inheritance
- Extend from a base template for consistent layout
- Use template blocks for customizable content areas
- Include common authentication-related partials

## Security Considerations
- Always use CSRF protection in forms
- Validate and sanitize user inputs
- Implement proper error handling
- Use HTTPS for authentication pages
- Follow Django's security best practices
