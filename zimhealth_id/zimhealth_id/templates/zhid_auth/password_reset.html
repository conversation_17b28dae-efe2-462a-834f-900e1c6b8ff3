{% extends 'base.html' %}

{% block title %}Password Reset - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-red-50 to-medical-50">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-key text-red-600 text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Reset Your Password</h2>
            <p class="text-gray-600">Enter your email address and we'll send you a secure reset link</p>
        </div>

        <!-- Reset Form -->
        <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Email Field -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope text-red-500 mr-2"></i>Email Address
                    </label>
                    <div class="relative">
                        <input type="email" name="{{ form.email.name }}" id="{{ form.email.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors placeholder-gray-400"
                               placeholder="Enter your email address" required>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                    </div>
                    {% if form.email.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            {% for error in form.email.errors %}
                                <p class="flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                </p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Non-field Errors -->
                {% if form.non_field_errors %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        {% for error in form.non_field_errors %}
                            <p class="text-red-800 text-sm flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>{{ error }}
                            </p>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Info Box -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-3"></i>
                        <div class="text-blue-800 text-sm">
                            <p class="font-medium mb-1">How password reset works:</p>
                            <ul class="list-disc list-inside space-y-1 text-blue-700">
                                <li>We'll send a secure link to your email</li>
                                <li>Click the link to create a new password</li>
                                <li>The link expires after a short time for security</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div>
                    <button type="submit"
                            class="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-paper-plane"></i>
                        <span>Send Reset Link</span>
                    </button>
                </div>

                <!-- Additional Links -->
                <div class="space-y-4">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Remember your password?</span>
                        </div>
                    </div>

                    <div class="text-center">
                        <a href="{% url 'zhid_auth:login' %}"
                           class="w-full bg-medical-600 text-white py-3 px-4 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Back to Sign In</span>
                        </a>
                    </div>

                    <div class="text-center">
                        <p class="text-sm text-gray-600">
                            Don't have an account?
                            <a href="{% url 'zhid_auth:register' %}" class="text-health-600 hover:text-health-700 font-medium">
                                Create one here
                            </a>
                        </p>
                    </div>
                </div>
            </form>
        </div>

        <!-- Security Notice -->
        <div class="text-center text-sm text-gray-500">
            <p class="flex items-center justify-center space-x-2">
                <i class="fas fa-shield-alt text-red-500"></i>
                <span>Password reset links are secure and expire quickly for your protection</span>
            </p>
        </div>
    </div>
</div>
{% endblock %}
