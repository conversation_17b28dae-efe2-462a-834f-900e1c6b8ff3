<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Sign In - ZimHealth-ID</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom Authentication CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'assets/css/auth.css' %}">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'medical': {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        'health': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans antialiased">
    <!-- Back Navigation -->
    <div class="back-nav">
        <a href="{% url 'landing' %}" class="back-nav-link">
            <i class="fas fa-arrow-left"></i>
            <span>Back to ZimHealth-ID</span>
        </a>
    </div>

    <div class="auth-container flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="auth-form-container">
            <!-- Modern Enhanced Header -->
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="text-lg font-bold text-gray-900 mb-1">Secure Access Portal</h1>
                <p class="text-gray-600 text-xs mb-2">ZimHealth-ID Healthcare Identity System</p>
            </div>

            <!-- Modern Organized Login Form -->
            <form method="post" class="auth-form">
                {% csrf_token %}

                <!-- Credentials Section -->
                <div class="auth-section credentials">
                    <!-- Username/Email Field -->
                    <div class="auth-input-group ultra-compact">
                        <label for="{{ form.username.id_for_label }}" class="auth-label">
                            Username or Email
                        </label>
                        <div class="auth-input-wrapper">
                            <input type="text"
                                   name="{{ form.username.name }}"
                                   id="{{ form.username.id_for_label }}"
                                   class="auth-input"
                                   placeholder="Enter your credentials"
                                   value="{{ form.username.value|default:'' }}"
                                   required>
                            <i class="fas fa-user auth-input-icon"></i>
                        </div>
                        {% if form.username.errors %}
                            <div class="auth-error">
                                {% for error in form.username.errors %}
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Password Field -->
                    <div class="auth-input-group ultra-compact">
                        <label for="{{ form.password.id_for_label }}" class="auth-label">
                            Password
                        </label>
                        <div class="auth-input-wrapper">
                            <input type="password"
                                   name="{{ form.password.name }}"
                                   id="{{ form.password.id_for_label }}"
                                   class="auth-input"
                                   placeholder="Enter your password"
                                   required>
                            <i class="fas fa-lock auth-input-icon"></i>
                            <button type="button" class="auth-input-action password-toggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        {% if form.password.errors %}
                            <div class="auth-error">
                                {% for error in form.password.errors %}
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Options Section -->
                <div class="auth-section options">
                    <!-- Remember Me -->
                    <div class="auth-input-group checkbox-compact">
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" id="remember-me" class="w-3 h-3 text-medical-600 border-gray-300 rounded focus:ring-medical-500">
                            <span class="text-xs text-gray-700">Keep me signed in</span>
                        </label>
                    </div>
                </div>

                <!-- Non-field Errors -->
                {% if form.non_field_errors %}
                    <div class="auth-error">
                        {% for error in form.non_field_errors %}
                            <i class="fas fa-exclamation-triangle mr-1"></i>{{ error }}
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Actions Section -->
                <div class="auth-section actions">
                    <!-- Submit Button -->
                    <div class="auth-input-group ultra-compact">
                        <button type="submit" class="auth-button">
                            <i class="fas fa-shield-alt mr-1"></i>
                            <span>Secure Sign In</span>
                        </button>
                    </div>
                </div>

                <!-- Section Divider -->
                <div class="section-divider"></div>

                <!-- Additional Links Section -->
                <div class="auth-links">
                    <div class="flex justify-center space-x-4 text-xs">
                        <a href="{% url 'zhid_auth:password_reset' %}" class="auth-link">
                            <i class="fas fa-key mr-1"></i>Forgot password?
                        </a>
                        <a href="{% url 'zhid_auth:resend_verification' %}" class="auth-link">
                            <i class="fas fa-envelope mr-1"></i>Resend email
                        </a>
                    </div>

                    <div class="auth-links-divider">
                        <div class="flex justify-center">
                            <span>New to ZimHealth-ID?</span>
                        </div>
                    </div>

                    <div class="text-center">
                        <a href="{% url 'zhid_auth:register' %}"
                           class="inline-flex items-center justify-center px-3 py-1.5 border-2 border-health-600 text-health-600 rounded-md hover:bg-health-50 transition-all duration-300 font-medium text-xs">
                            <i class="fas fa-user-plus mr-1"></i>
                            Create Account
                        </a>
                    </div>
                </div>
            </form>

            <!-- Ultra-Compact Security Footer -->
            <div class="security-footer text-center">
                <div class="security-badges">
                    <div class="security-badge-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>SSL</span>
                    </div>
                    <div class="security-badge-item">
                        <i class="fas fa-lock"></i>
                        <span>HIPAA</span>
                    </div>
                    <div class="security-badge-item">
                        <i class="fas fa-certificate"></i>
                        <span>ISO 27001</span>
                    </div>
                </div>
                <p class="text-xs text-gray-400">
                    Enterprise security
                </p>
            </div>
        </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
        // Enhanced password toggle with smooth transitions
        function initPasswordToggle() {
            const passwordField = document.getElementById('id_password');
            const toggleButton = document.querySelector('.password-toggle');

            if (passwordField && toggleButton) {
                toggleButton.addEventListener('click', function(e) {
                    e.preventDefault();

                    const icon = this.querySelector('i');
                    const isPassword = passwordField.type === 'password';

                    // Toggle password visibility
                    passwordField.type = isPassword ? 'text' : 'password';

                    // Update icon with smooth transition
                    icon.style.transform = 'scale(0.8)';

                    setTimeout(() => {
                        if (isPassword) {
                            icon.classList.remove('fa-eye');
                            icon.classList.add('fa-eye-slash');
                        } else {
                            icon.classList.remove('fa-eye-slash');
                            icon.classList.add('fa-eye');
                        }
                        icon.style.transform = 'scale(1)';
                    }, 150);
                });
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initPasswordToggle();

            // Add loading state to form submission
            const form = document.querySelector('.auth-form');
            const submitButton = form.querySelector('.auth-button');

            form.addEventListener('submit', function() {
                submitButton.classList.add('loading');
                submitButton.disabled = true;
            });
        });
    </script>
</body>
</html>
