{% extends 'base.html' %}

{% block title %}Security Settings - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/security.css' %}">
{% endblock %}

{% block content %}
<div class="security-container">
    <!-- Professional Security Header -->
    <div class="security-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'zhid_auth:profile' %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Security Settings</h1>
                            <p class="text-gray-600 mt-1 text-sm">Manage your account security and authentication settings</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="security-status-indicator">
                            <i class="fas fa-shield-alt text-health-600"></i>
                            <span class="text-sm text-health-600 font-medium">Account Secured</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Security Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Security Navigation Sidebar -->
            <div class="lg:col-span-1">
                <div class="security-nav-card">
                    <div class="security-section-header">
                        <h4 class="security-section-title">
                            <i class="fas fa-shield-alt"></i>Security Center
                        </h4>
                        <p class="security-section-subtitle">Manage your account protection</p>
                    </div>
                    <div class="p-6 space-y-4">
                        <a href="{% url 'zhid_auth:password_change' %}" class="security-nav-item active">
                            <i class="fas fa-key mr-3"></i>
                            <div>
                                <span class="security-nav-title">Password Security</span>
                                <span class="security-nav-desc">Change your password</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Password Change Form -->
            <div class="lg:col-span-2">
                <div class="security-card">
                    <div class="security-section-header">
                        <h4 class="security-section-title">
                            <i class="fas fa-key"></i>Update Password
                        </h4>
                        <p class="security-section-subtitle">Choose a strong password to keep your account secure</p>
                    </div>

                    <div class="p-6">
                        <form method="post" class="space-y-6">
                            {% csrf_token %}

                            <!-- Current Password -->
                            <div class="security-form-group">
                                <label for="{{ form.old_password.id_for_label }}" class="security-form-label">
                                    <i class="fas fa-lock text-medical-500 mr-2"></i>Current Password
                                </label>
                                <div class="relative">
                                    <input type="password" name="{{ form.old_password.name }}" id="{{ form.old_password.id_for_label }}"
                                           class="security-form-input"
                                           placeholder="Enter your current password" required>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <button type="button" onclick="togglePassword('{{ form.old_password.id_for_label }}')" class="password-toggle-btn">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                {% if form.old_password.errors %}
                                    <div class="security-form-error">
                                        {% for error in form.old_password.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- New Password -->
                            <div class="security-form-group">
                                <label for="{{ form.new_password1.id_for_label }}" class="security-form-label">
                                    <i class="fas fa-key text-medical-500 mr-2"></i>New Password
                                </label>
                                <div class="relative">
                                    <input type="password" name="{{ form.new_password1.name }}" id="{{ form.new_password1.id_for_label }}"
                                           class="security-form-input"
                                           placeholder="Enter your new password" required>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <button type="button" onclick="togglePassword('{{ form.new_password1.id_for_label }}')" class="password-toggle-btn">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                {% if form.new_password1.errors %}
                                    <div class="security-form-error">
                                        {% for error in form.new_password1.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Confirm New Password -->
                            <div class="security-form-group">
                                <label for="{{ form.new_password2.id_for_label }}" class="security-form-label">
                                    <i class="fas fa-key text-medical-500 mr-2"></i>Confirm New Password
                                </label>
                                <div class="relative">
                                    <input type="password" name="{{ form.new_password2.name }}" id="{{ form.new_password2.id_for_label }}"
                                           class="security-form-input"
                                           placeholder="Confirm your new password" required>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <button type="button" onclick="togglePassword('{{ form.new_password2.id_for_label }}')" class="password-toggle-btn">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                {% if form.new_password2.errors %}
                                    <div class="security-form-error">
                                        {% for error in form.new_password2.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Non-field Errors -->
                            {% if form.non_field_errors %}
                                <div class="security-form-error-global">
                                    {% for error in form.non_field_errors %}
                                        <p class="text-red-800 text-sm flex items-center">
                                            <i class="fas fa-exclamation-triangle mr-2"></i>{{ error }}
                                        </p>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <!-- Password Requirements -->
                            <div class="security-requirements-card">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-medical-500 mt-0.5 mr-3"></i>
                                    <div>
                                        <p class="text-medical-800 font-medium text-sm mb-2">Password Requirements:</p>
                                        <ul class="text-medical-700 text-sm space-y-1">
                                            <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2 text-xs"></i>At least 8 characters long</li>
                                            <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2 text-xs"></i>Not similar to personal information</li>
                                            <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2 text-xs"></i>Not a commonly used password</li>
                                            <li class="flex items-center"><i class="fas fa-check text-health-500 mr-2 text-xs"></i>Not entirely numeric</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="security-form-actions">
                                <button type="submit" class="security-action-button primary">
                                    <i class="fas fa-save"></i>
                                    <span>Update Password</span>
                                </button>

                                <a href="{% url 'zhid_auth:profile' %}" class="security-action-button secondary">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Back to Profile</span>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- Security Tips Card -->
        <div class="mt-8">
            <div class="security-tips-card">
                <div class="security-section-header">
                    <h4 class="security-section-title">
                        <i class="fas fa-shield-alt"></i>Security Best Practices
                    </h4>
                    <p class="security-section-subtitle">Follow these guidelines to keep your account secure</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="security-tip-item">
                            <i class="fas fa-key text-medical-500"></i>
                            <div>
                                <h5 class="security-tip-title">Unique Password</h5>
                                <p class="security-tip-desc">Use a password you don't use elsewhere</p>
                            </div>
                        </div>
                        <div class="security-tip-item">
                            <i class="fas fa-lock text-medical-500"></i>
                            <div>
                                <h5 class="security-tip-title">Password Manager</h5>
                                <p class="security-tip-desc">Consider using a password manager</p>
                            </div>
                        </div>
                        <div class="security-tip-item">
                            <i class="fas fa-random text-medical-500"></i>
                            <div>
                                <h5 class="security-tip-title">Mix Characters</h5>
                                <p class="security-tip-desc">Include letters, numbers, and symbols</p>
                            </div>
                        </div>
                        <div class="security-tip-item">
                            <i class="fas fa-user-shield text-medical-500"></i>
                            <div>
                                <h5 class="security-tip-title">Avoid Personal Info</h5>
                                <p class="security-tip-desc">Don't use personal information</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const toggleIcon = passwordField.parentElement.querySelector('i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}
</script>
{% endblock %}
