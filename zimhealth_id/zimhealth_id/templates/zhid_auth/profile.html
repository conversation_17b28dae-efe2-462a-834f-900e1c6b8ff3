{% extends 'base.html' %}
{% load static %}

{% block title %}Professional Profile Management - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/profile.css' %}">
{% endblock %}

{% block content %}
<div class="profile-container">
    <!-- Professional Profile Header -->
    <div class="profile-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Professional Profile Management</h1>
                        <p class="text-gray-600 mt-1 text-sm">Manage your ZimHealth-ID professional account and security settings</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:dashboard' %}" class="profile-action-button secondary">
                            <i class="fas fa-arrow-left mr-2"></i>
                            <span>Back to Dashboard</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content with Professional Layout -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Enhanced Profile Summary Card -->
            <div class="lg:col-span-1">
                <div class="profile-card overflow-hidden">
                    <!-- Professional Header with Teal/Cyan/Blue Gradient -->
                    <div class="bg-gradient-to-r from-teal-500 to-blue-600 h-20 rounded-t-xl"></div>

                    <div class="px-4 pb-3">
                        <div class="flex flex-col items-center -mt-10">
                            <!-- Enhanced Professional Avatar - Double Size -->
                            {% if profile.avatar %}
                                <img src="{{ profile.avatar.url }}" alt="Profile Picture" class="profile-avatar-proper">
                            {% else %}
                                <div class="profile-avatar-placeholder-proper">
                                    <i class="fas fa-user"></i>
                                </div>
                            {% endif %}

                            <!-- Professional Typography Hierarchy -->
                            <div class="text-center mt-2.5 space-y-0.5">
                                <h3 class="text-lg font-bold text-gray-900">{{ user.get_full_name|default:user.username }}</h3>
                                <p class="text-sm text-gray-600">@{{ user.username }}</p>
                            </div>

                            <!-- Professional Email Verification Status -->
                            <div class="mt-2">
                                {% if profile.is_email_verified %}
                                    <div class="profile-status-badge verified">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        <span>Verified Account</span>
                                    </div>
                                {% else %}
                                    <div class="text-center space-y-1.5">
                                        <div class="profile-status-badge unverified">
                                            <i class="fas fa-exclamation-circle mr-2"></i>
                                            <span>Email Not Verified</span>
                                        </div>
                                        <div>
                                            <a href="{% url 'zhid_auth:resend_verification' %}"
                                               class="profile-action-button secondary text-xs">
                                                <i class="fas fa-envelope mr-1"></i>Verify Email
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Professional Profile Details - Patients Page Style -->
                        <div class="mt-3 space-y-2">
                            <div class="profile-info-item">
                                <i class="fas fa-envelope profile-info-icon"></i>
                                <span class="profile-info-text">{{ user.email }}</span>
                            </div>
                            {% if profile.hospital_name %}
                                <div class="profile-info-item">
                                    <i class="fas fa-hospital profile-info-icon"></i>
                                    <span class="profile-info-text">{{ profile.hospital_name }}</span>
                                </div>
                            {% endif %}
                            {% if profile.phone_number %}
                                <div class="profile-info-item">
                                    <i class="fas fa-phone profile-info-icon"></i>
                                    <span class="profile-info-text">{{ profile.phone_number }}</span>
                                </div>
                            {% endif %}
                            {% if profile.professional_role %}
                                <div class="profile-info-item">
                                    <i class="fas fa-user-md profile-info-icon"></i>
                                    <span class="profile-info-text">{{ profile.get_professional_role_display }}</span>
                                </div>
                            {% endif %}
                            <div class="profile-info-item">
                                <i class="fas fa-user-plus profile-info-icon"></i>
                                <span class="profile-info-text">Joined {{ user.date_joined|date:"M Y" }}</span>
                            </div>
                            {% if user.last_login %}
                                <div class="profile-info-item">
                                    <i class="fas fa-clock profile-info-icon"></i>
                                    <span class="profile-info-text">Active {{ user.last_login|timesince }} ago</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Enhanced Quick Actions -->
                <div class="profile-card mt-6">
                    <div class="profile-section-header">
                        <h4 class="profile-section-title">
                            <i class="fas fa-bolt"></i>Quick Actions
                        </h4>
                        <p class="profile-section-subtitle">Manage your account security and settings</p>
                    </div>
                    <div class="p-6 space-y-4">
                        <a href="{% url 'zhid_auth:password_change' %}" class="profile-action-button secondary w-full">
                            <i class="fas fa-key mr-2"></i>
                            <span>Change Password</span>
                        </a>

                        {% if not profile.is_email_verified %}
                            <a href="{% url 'zhid_auth:resend_verification' %}" class="profile-action-button secondary w-full">
                                <i class="fas fa-envelope-open mr-2"></i>
                                <span>Verify Email</span>
                            </a>
                        {% endif %}

                        <form method="post" action="{% url 'zhid_auth:logout' %}" class="w-full">
                            {% csrf_token %}
                            <button type="submit" class="profile-action-button danger w-full">
                                <i class="fas fa-sign-out-alt mr-2"></i>
                                <span>Sign Out</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Enhanced Profile Edit Form -->
            <div class="lg:col-span-2">
                <div class="profile-card">
                    <div class="profile-section-header">
                        <h4 class="profile-section-title">
                            <i class="fas fa-user-edit"></i>Profile Information
                        </h4>
                        <p class="profile-section-subtitle">Update your personal details</p>
                    </div>
                    <div class="p-6">
                        <form method="post" enctype="multipart/form-data" class="space-y-6">
                            {% csrf_token %}

                            <!-- Enhanced Name Fields -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="{{ form.first_name.id_for_label }}" class="profile-form-label">
                                        <i class="fas fa-user"></i>First Name
                                    </label>
                                    <input type="text" name="{{ form.first_name.name }}" id="{{ form.first_name.id_for_label }}"
                                           value="{{ form.first_name.value|default:'' }}"
                                           class="profile-form-input"
                                           placeholder="Enter your first name">
                                    {% if form.first_name.errors %}
                                        <div class="mt-2 text-sm text-red-600">
                                            {% for error in form.first_name.errors %}
                                                <p class="flex items-center">
                                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                                </p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div>
                                    <label for="{{ form.last_name.id_for_label }}" class="profile-form-label">
                                        <i class="fas fa-user"></i>Last Name
                                    </label>
                                    <input type="text" name="{{ form.last_name.name }}" id="{{ form.last_name.id_for_label }}"
                                           value="{{ form.last_name.value|default:'' }}"
                                           class="profile-form-input"
                                           placeholder="Enter your last name">
                                    {% if form.last_name.errors %}
                                        <div class="mt-2 text-sm text-red-600">
                                            {% for error in form.last_name.errors %}
                                                <p class="flex items-center">
                                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                                </p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Enhanced Email Field -->
                            <div>
                                <label for="{{ form.email.id_for_label }}" class="profile-form-label">
                                    <i class="fas fa-envelope"></i>Email Address
                                </label>
                                <input type="email" name="{{ form.email.name }}" id="{{ form.email.id_for_label }}"
                                       value="{{ form.email.value|default:'' }}"
                                       class="profile-form-input"
                                       placeholder="Enter your email address">
                                {% if form.email.errors %}
                                    <div class="mt-2 text-sm text-red-600">
                                        {% for error in form.email.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Enhanced Hospital Name Field -->
                            <div>
                                <label for="{{ form.hospital_name.id_for_label }}" class="profile-form-label">
                                    <i class="fas fa-hospital"></i>Hospital / Medical Facility
                                </label>
                                <input type="text" name="{{ form.hospital_name.name }}" id="{{ form.hospital_name.id_for_label }}"
                                       value="{{ form.hospital_name.value|default:'' }}"
                                       class="profile-form-input"
                                       placeholder="Enter your hospital or medical facility name">
                                {% if form.hospital_name.errors %}
                                    <div class="mt-2 text-sm text-red-600">
                                        {% for error in form.hospital_name.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Enhanced Phone and Professional Role -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="{{ form.phone_number.id_for_label }}" class="profile-form-label">
                                        <i class="fas fa-phone"></i>Phone Number
                                    </label>
                                    <input type="tel" name="{{ form.phone_number.name }}" id="{{ form.phone_number.id_for_label }}"
                                           value="{{ form.phone_number.value|default:'' }}"
                                           class="profile-form-input"
                                           placeholder="Enter your phone number">
                                    {% if form.phone_number.errors %}
                                        <div class="mt-2 text-sm text-red-600">
                                            {% for error in form.phone_number.errors %}
                                                <p class="flex items-center">
                                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                                </p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div>
                                    <label for="{{ form.professional_role.id_for_label }}" class="profile-form-label">
                                        <i class="fas fa-user-md"></i>Professional Role
                                    </label>
                                    <select name="{{ form.professional_role.name }}" id="{{ form.professional_role.id_for_label }}"
                                            class="profile-form-input">
                                        <option value="">Select your professional role</option>
                                        {% for value, label in form.professional_role.field.choices %}
                                            <option value="{{ value }}" {% if form.professional_role.value == value %}selected{% endif %}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                    {% if form.professional_role.errors %}
                                        <div class="mt-2 text-sm text-red-600">
                                            {% for error in form.professional_role.errors %}
                                                <p class="flex items-center">
                                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                                </p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Enhanced Address Field -->
                            <div>
                                <label for="{{ form.address.id_for_label }}" class="profile-form-label">
                                    <i class="fas fa-map-marker-alt"></i>Address
                                </label>
                                <textarea name="{{ form.address.name }}" id="{{ form.address.id_for_label }}" rows="3"
                                          class="profile-form-input resize-none"
                                          placeholder="Enter your complete address">{{ form.address.value|default:'' }}</textarea>
                                {% if form.address.errors %}
                                    <div class="mt-2 text-sm text-red-600">
                                        {% for error in form.address.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Enhanced Bio Field -->
                            <div>
                                <label for="{{ form.bio.id_for_label }}" class="profile-form-label">
                                    <i class="fas fa-info-circle"></i>Professional Bio
                                </label>
                                <textarea name="{{ form.bio.name }}" id="{{ form.bio.id_for_label }}" rows="4"
                                          class="profile-form-input resize-none"
                                          placeholder="Tell us about your professional background and experience">{{ form.bio.value|default:'' }}</textarea>
                                {% if form.bio.errors %}
                                    <div class="mt-2 text-sm text-red-600">
                                        {% for error in form.bio.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Enhanced Avatar Upload -->
                            <div>
                                <label for="{{ form.avatar.id_for_label }}" class="profile-form-label">
                                    <i class="fas fa-camera"></i>Professional Profile Picture
                                </label>
                                <div class="flex items-center space-x-6">
                                    <div class="flex-shrink-0">
                                        {% if profile.avatar %}
                                            <img src="{{ profile.avatar.url }}" alt="Current avatar" class="w-20 h-20 rounded-full object-cover border-3 border-medical-200 shadow-lg">
                                        {% else %}
                                            <div class="w-20 h-20 rounded-full bg-gradient-to-br from-medical-100 to-medical-200 flex items-center justify-center border-3 border-medical-200 shadow-lg">
                                                <i class="fas fa-user text-medical-600 text-lg"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="flex-1">
                                        <div class="profile-file-upload">
                                            <input type="file" name="{{ form.avatar.name }}" id="{{ form.avatar.id_for_label }}" accept="image/*">
                                            <div class="profile-file-upload-label">
                                                <i class="fas fa-cloud-upload-alt text-medical-600 mr-3"></i>
                                                <div>
                                                    <div class="font-medium text-gray-700">Choose profile picture</div>
                                                    <div class="text-sm text-gray-500">PNG, JPG, GIF up to 10MB</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% if form.avatar.errors %}
                                    <div class="mt-2 text-sm text-red-600">
                                        {% for error in form.avatar.errors %}
                                            <p class="flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i>{{ error }}
                                            </p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Enhanced Submit Button -->
                            <div class="pt-6 border-t border-gray-200">
                                <div class="flex space-x-4">
                                    <button type="submit" class="profile-action-button flex-1">
                                        <i class="fas fa-save mr-2"></i>
                                        <span>Update Professional Profile</span>
                                    </button>
                                    <a href="{% url 'api:dashboard' %}" class="profile-action-button secondary">
                                        <i class="fas fa-times mr-2"></i>
                                        <span>Cancel</span>
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
