{% extends 'base.html' %}

{% block title %}Server Error - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-red-50 to-medical-50">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto h-32 w-32 bg-red-100 rounded-full flex items-center justify-center mb-8">
            <i class="fas fa-exclamation-triangle text-red-600 text-5xl"></i>
        </div>

        <!-- Error Message -->
        <div>
            <h1 class="text-6xl font-bold text-gray-900 mb-4">500</h1>
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Server Error</h2>
            <p class="text-gray-600 mb-8">
                We're experiencing some technical difficulties. Our team has been notified and is working to resolve the issue.
            </p>
        </div>

        <!-- Error Card -->
        <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
            <div class="space-y-6">
                <!-- Status Message -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
                        <div class="text-left">
                            <h3 class="text-sm font-medium text-red-800">Service Temporarily Unavailable</h3>
                            <p class="text-sm text-red-700 mt-1">
                                We're working to restore normal service as quickly as possible.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- What happened -->
                <div class="text-left">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">What happened?</h3>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li class="flex items-start">
                            <i class="fas fa-circle text-gray-400 mr-3 mt-1.5 text-xs"></i>
                            <span>An unexpected error occurred on our servers</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-circle text-gray-400 mr-3 mt-1.5 text-xs"></i>
                            <span>Our technical team has been automatically notified</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-circle text-gray-400 mr-3 mt-1.5 text-xs"></i>
                            <span>We're working to resolve this issue immediately</span>
                        </li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-4">
                    <button onclick="location.reload()" 
                            class="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-redo"></i>
                        <span>Try Again</span>
                    </button>

                    <button onclick="history.back()" 
                            class="w-full bg-medical-600 text-white py-3 px-4 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-arrow-left"></i>
                        <span>Go Back</span>
                    </button>

                    <a href="{% if user.is_authenticated %}{% url 'zhid_auth:profile' %}{% else %}{% url 'zhid_auth:login' %}{% endif %}" 
                       class="w-full bg-health-600 text-white py-3 px-4 rounded-lg hover:bg-health-700 focus:ring-2 focus:ring-health-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-home"></i>
                        <span>Go to Homepage</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Support Information -->
        <div class="bg-gray-100 rounded-lg p-4">
            <h3 class="text-sm font-semibold text-gray-900 mb-2">Need immediate assistance?</h3>
            <div class="space-y-2 text-sm text-gray-600">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-phone text-medical-500"></i>
                    <span>Emergency Support: +263 4 123 4567</span>
                </div>
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-envelope text-medical-500"></i>
                    <span><EMAIL></span>
                </div>
            </div>
        </div>

        <!-- Additional Info -->
        <div class="text-center text-sm text-gray-500">
            <p>Error Code: 500 | Timestamp: {{ now|date:"Y-m-d H:i:s" }}</p>
            <p class="mt-2">
                This incident has been logged and our team will investigate.
            </p>
        </div>
    </div>
</div>

<script>
// Auto-refresh after 30 seconds
setTimeout(function() {
    if (confirm('Would you like to try loading the page again?')) {
        location.reload();
    }
}, 30000);
</script>
{% endblock %}
