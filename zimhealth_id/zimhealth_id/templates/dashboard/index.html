{% extends 'base.html' %}

{% block title %}Healthcare Dashboard - ZimHealth-ID{% endblock %}

{# Override messages block to disable Django messages on dashboard #}
{% block messages %}
{# No messages displayed on dashboard to prevent interference with command center #}
{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}?v=**********">
<style>
    /* Ensure quick action links are clickable */
    .quick-action {
        display: block !important;
        text-decoration: none !important;
        position: relative;
        z-index: 10;
        cursor: pointer;
        border-radius: 0.5rem;
        background: white;
        border: 1px solid #e5e7eb;
    }

    .quick-action:hover {
        text-decoration: none !important;
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    /* Only prevent pointer events on child elements for buttons, not links */
    button.quick-action * {
        pointer-events: none;
    }

    .quick-action:active {
        transform: translateY(0);
    }

    /* Ensure links are properly styled */
    a.quick-action {
        color: inherit;
    }

    a.quick-action:visited {
        color: inherit;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Sleek Professional Dashboard Header -->
    <div class="dashboard-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="header-content">
                <!-- Left Section: Clean Professional Title -->
                <div class="header-left">
                    <h1 class="header-title">Healthcare Dashboard</h1>
                    <p class="header-subtitle">Patient Care Management</p>
                </div>

                <!-- Right Section: User Information -->
                <div class="header-right">
                    <div class="user-info">
                        <p class="user-name">{{ user.first_name|default:user.username|title }}</p>
                        <p class="user-timestamp">{{ user.last_login|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="user-avatar">
                        <i class="fas fa-user-md"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Patients -->
            <div class="stat-card patients p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon medical">
                            <i class="fas fa-users text-medical-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p>Total Patients</p>
                        <p>{{ total_patients }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-health-600 font-medium">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>+12% from last month</span>
                    </div>
                </div>
            </div>

            <!-- Today's Appointments -->
            <div class="stat-card appointments p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon health">
                            <i class="fas fa-calendar-check text-health-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p>Today's Appointments</p>
                        <p>{{ todays_appointments }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-medical-600 font-medium">
                        <i class="fas fa-clock mr-1"></i>
                        <span>{{ pending_appointments }} pending</span>
                    </div>
                </div>
            </div>

            <!-- Medical Records -->
            <div class="stat-card records p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon blue">
                            <i class="fas fa-file-medical text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p>Medical Records</p>
                        <p>{{ total_records }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-blue-600 font-medium">
                        <i class="fas fa-plus mr-1"></i>
                        <span>{{ new_records }} this week</span>
                    </div>
                </div>
            </div>

            <!-- Active Prescriptions -->
            <div class="stat-card prescriptions p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon purple">
                            <i class="fas fa-pills text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p>Active Prescriptions</p>
                        <p>{{ active_prescriptions }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-purple-600 font-medium">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <span>{{ expiring_prescriptions }} expiring soon</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unified Quick Actions Ribbon -->
        <div class="mb-8">
            <div class="quick-actions-ribbon">
                <div class="px-8 py-6">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Healthcare Command Center</h3>
                        <p class="text-sm text-gray-600">Essential functions for comprehensive patient care management</p>

                    </div>
                    <!-- Primary Actions Row -->
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-6">
                        <!-- Primary Actions -->
                        <a href="{% url 'api:patient_create' %}" class="quick-action medical" title="Click to create a new patient">
                            <div class="stat-icon medical">
                                <i class="fas fa-user-plus text-medical-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Register Patient</span>
                            <span class="text-xs text-gray-500">New enrollment</span>
                        </a>
                        <a href="{% url 'api:appointment_create' %}" class="quick-action health" title="Click to schedule a new appointment">
                            <div class="stat-icon health">
                                <i class="fas fa-calendar-plus text-health-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Schedule Visit</span>
                            <span class="text-xs text-gray-500">Book appointment</span>
                        </a>
                        <a href="{% url 'api:medical_record_create' %}" class="quick-action blue" title="Click to create a new medical record">
                            <div class="stat-icon blue">
                                <i class="fas fa-file-medical-alt text-blue-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Medical Record</span>
                            <span class="text-xs text-gray-500">Create new</span>
                        </a>
                        <a href="{% url 'api:prescription_create' %}" class="quick-action purple" title="Click to create a new prescription">
                            <div class="stat-icon purple">
                                <i class="fas fa-prescription-bottle text-purple-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Prescribe</span>
                            <span class="text-xs text-gray-500">Issue medication</span>
                        </a>
                        <!-- Additional Unique Actions -->
                        <button onclick="openPatientSearch()" class="quick-action orange">
                            <div class="stat-icon orange">
                                <i class="fas fa-search text-orange-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Patient Search</span>
                            <span class="text-xs text-gray-500">Find records</span>
                        </button>
                        <button onclick="openEmergencyPanel()" class="quick-action red">
                            <div class="stat-icon red">
                                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Emergency</span>
                            <span class="text-xs text-gray-500">Urgent care</span>
                        </button>
                    </div>

                    <!-- Secondary Actions Row - Centered -->
                    <div class="flex justify-center">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl">
                            <!-- Additional Healthcare Actions -->
                            <a href="{% url 'api:analytics' %}" class="quick-action indigo" title="Click to view analytics dashboard">
                                <div class="stat-icon indigo">
                                    <i class="fas fa-chart-bar text-indigo-600 text-xl"></i>
                                </div>
                                <span class="text-sm">Analytics</span>
                                <span class="text-xs">View reports</span>
                            </a>

                            <a href="{% url 'api:notifications' %}" class="quick-action yellow" title="Click to view notifications">
                                <div class="stat-icon yellow">
                                    <i class="fas fa-bell text-yellow-600 text-xl"></i>
                                </div>
                                <span class="text-sm">Notifications</span>
                                <span class="text-xs">View alerts</span>
                                <span id="command-notification-badge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-2 py-1 hidden">0</span>
                            </a>

                            <button onclick="openBulkActions()" class="quick-action teal">
                                <div class="stat-icon teal">
                                    <i class="fas fa-tasks text-teal-600 text-xl"></i>
                                </div>
                                <span class="text-sm">Bulk Actions</span>
                                <span class="text-xs">Mass operations</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Enhanced Recent Activity Card -->
            <div class="lg:col-span-2">
                <div class="activity-card">
                    <div class="activity-header">
                        <h3 class="activity-title">
                            <div class="activity-title-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            Recent Healthcare Activity
                        </h3>
                        <p class="activity-subtitle">Latest patient interactions and system updates</p>
                    </div>
                    <div class="activity-content">
                        {% if recent_activities %}
                            <div class="space-y-0">
                                {% for activity in recent_activities %}
                                    <div class="activity-item flex items-start space-x-4">
                                        <div class="flex-shrink-0">
                                            <div class="activity-icon">
                                                <i class="fas fa-{{ activity.icon }} text-medical-600 text-sm"></i>
                                            </div>
                                        </div>
                                        <div class="activity-details">
                                            <p class="activity-title-text">{{ activity.title }}</p>
                                            <p class="activity-description">{{ activity.description }}</p>
                                            <p class="activity-timestamp">{{ activity.timestamp|timesince }} ago</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="activity-empty">
                                <div class="activity-empty-icon">
                                    <i class="fas fa-history text-gray-400 text-xl"></i>
                                </div>
                                <p class="activity-empty-title">No recent activity</p>
                                <p class="activity-empty-subtitle">Patient interactions will appear here</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Enhanced Sidebar -->
            <div class="space-y-6">
                <!-- System Status & Alerts -->
                <div class="content-card">
                    <div class="card-header px-6 py-4">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-heartbeat text-health-500 mr-2"></i>System Status
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">Real-time healthcare system monitoring</p>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-health-500 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-gray-900">Database Connection</span>
                            </div>
                            <span class="status-badge">Online</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-health-500 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-gray-900">Patient Records</span>
                            </div>
                            <span class="status-badge">Synced</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                                <span class="text-sm font-medium text-gray-900">Backup Status</span>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                Pending
                            </span>
                        </div>
                        <div class="pt-3 border-t border-gray-100">
                            <div class="text-center">
                                <p class="text-xs text-gray-500">Last system check</p>
                                <p class="text-sm font-semibold text-gray-900">2 minutes ago</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Upcoming Appointments -->
                <div class="content-card">
                    <div class="card-header px-6 py-4">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-calendar-alt text-health-500 mr-2"></i>Upcoming Appointments
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">Next scheduled patient visits</p>
                    </div>
                    <div class="p-6">
                        {% if upcoming_appointments %}
                            <div class="space-y-3">
                                {% for appointment in upcoming_appointments %}
                                    <div class="appointment-card p-3">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <p class="font-semibold text-gray-900">{{ appointment.patient.full_name }}</p>
                                                <p class="text-sm text-gray-600">{{ appointment.date|date:"M d" }} at {{ appointment.time|time:"H:i" }}</p>
                                            </div>
                                            <div class="text-right">
                                                <span class="status-badge">
                                                    {{ appointment.appointment_type|title }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-8">
                                <div class="activity-icon mx-auto mb-4">
                                    <i class="fas fa-calendar-times text-gray-400 text-xl"></i>
                                </div>
                                <p class="text-gray-500 font-medium">No upcoming appointments</p>
                                <p class="text-gray-400 text-sm mt-1">Schedule is clear</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Patient Search Modal -->
<div id="patient-search-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">Quick Patient Search</h3>
                <button onclick="closePatientSearch()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <input type="text" id="quick-search-input"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500"
                           placeholder="Search by name, ZimHealth ID, or phone number...">
                </div>
                <div id="search-results" class="max-h-96 overflow-y-auto">
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-search text-4xl mb-4"></i>
                        <p>Start typing to search for patients</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Emergency Panel Modal -->
<div id="emergency-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="flex items-center justify-between p-6 border-b bg-red-50">
                <h3 class="text-lg font-semibold text-red-900 flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                    Emergency Actions
                </h3>
                <button onclick="closeEmergencyPanel()" class="text-red-400 hover:text-red-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <a href="{% url 'api:patient_create' %}?emergency=true"
                       class="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
                        <i class="fas fa-user-plus text-red-600 text-xl mr-4"></i>
                        <div>
                            <h4 class="font-semibold text-red-900">Emergency Registration</h4>
                            <p class="text-sm text-red-700">Register walk-in emergency patient</p>
                        </div>
                    </a>
                    <a href="{% url 'api:appointment_create' %}?priority=urgent"
                       class="flex items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                        <i class="fas fa-calendar-plus text-orange-600 text-xl mr-4"></i>
                        <div>
                            <h4 class="font-semibold text-orange-900">Urgent Appointment</h4>
                            <p class="text-sm text-orange-700">Schedule urgent care visit</p>
                        </div>
                    </a>
                    <button onclick="openQRScanner()"
                            class="w-full flex items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-qrcode text-blue-600 text-xl mr-4"></i>
                        <div class="text-left">
                            <h4 class="font-semibold text-blue-900">QR Code Scanner</h4>
                            <p class="text-sm text-blue-700">Quick patient lookup via QR</p>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div id="bulk-actions-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-tasks text-teal-600 mr-2"></i>
                    Bulk Operations
                </h3>
                <button onclick="closeBulkActions()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <a href="{% url 'api:patients' %}?bulk=true"
                       class="flex items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-users text-blue-600 text-xl mr-4"></i>
                        <div>
                            <h4 class="font-semibold text-blue-900">Patient Management</h4>
                            <p class="text-sm text-blue-700">Bulk update patient records</p>
                        </div>
                    </a>
                    <a href="{% url 'api:appointments' %}?bulk=true"
                       class="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                        <i class="fas fa-calendar text-green-600 text-xl mr-4"></i>
                        <div>
                            <h4 class="font-semibold text-green-900">Appointment Operations</h4>
                            <p class="text-sm text-green-700">Mass schedule or update appointments</p>
                        </div>
                    </a>
                    <button onclick="exportData()"
                            class="w-full flex items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                        <i class="fas fa-download text-purple-600 text-xl mr-4"></i>
                        <div class="text-left">
                            <h4 class="font-semibold text-purple-900">Data Export</h4>
                            <p class="text-sm text-purple-700">Export patient data and reports</p>
                        </div>
                    </button>
                    <button onclick="systemMaintenance()"
                            class="w-full flex items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                        <i class="fas fa-cog text-orange-600 text-xl mr-4"></i>
                        <div class="text-left">
                            <h4 class="font-semibold text-orange-900">System Maintenance</h4>
                            <p class="text-sm text-orange-700">Database cleanup and optimization</p>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="{% static 'assets/js/dashboard.js' %}"></script>
<script>
// Patient Search Functionality
function openPatientSearch() {
    document.getElementById('patient-search-modal').classList.remove('hidden');
    document.getElementById('quick-search-input').focus();
}

function closePatientSearch() {
    document.getElementById('patient-search-modal').classList.add('hidden');
    document.getElementById('quick-search-input').value = '';
    document.getElementById('search-results').innerHTML = `
        <div class="text-center text-gray-500 py-8">
            <i class="fas fa-search text-4xl mb-4"></i>
            <p>Start typing to search for patients</p>
        </div>
    `;
}

// Emergency Panel Functionality
function openEmergencyPanel() {
    document.getElementById('emergency-modal').classList.remove('hidden');
}

function closeEmergencyPanel() {
    document.getElementById('emergency-modal').classList.add('hidden');
}

// Bulk Actions Functionality
function openBulkActions() {
    document.getElementById('bulk-actions-modal').classList.remove('hidden');
}

function closeBulkActions() {
    document.getElementById('bulk-actions-modal').classList.add('hidden');
}

// Export Data Functionality
function exportData() {
    closeBulkActions();

    // Show export options
    const exportOptions = [
        { label: 'Patient Records (CSV)', action: () => window.open('/api/export/patients/csv/') },
        { label: 'Appointments (Excel)', action: () => window.open('/api/export/appointments/excel/') },
        { label: 'Medical Records (PDF)', action: () => window.open('/api/export/medical-records/pdf/') },
        { label: 'Analytics Report', action: () => window.open('/api/export/analytics/') }
    ];

    const optionsHTML = exportOptions.map((option, index) =>
        `<button onclick="exportOptions[${index}].action()" class="w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors">
            ${option.label}
        </button>`
    ).join('');

    // Create temporary modal for export options
    const exportModal = document.createElement('div');
    exportModal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50';
    exportModal.innerHTML = `
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">Export Data</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-2">
                        ${optionsHTML}
                    </div>
                </div>
                <div class="p-6 border-t">
                    <button onclick="this.closest('.fixed').remove()" class="w-full px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    `;

    // Store export options globally for onclick handlers
    window.exportOptions = exportOptions;

    document.body.appendChild(exportModal);
}

// System Maintenance Functionality
function systemMaintenance() {
    closeBulkActions();

    if (confirm('Are you sure you want to perform system maintenance? This may take a few minutes.')) {
        // Show maintenance progress
        const maintenanceModal = document.createElement('div');
        maintenanceModal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50';
        maintenanceModal.innerHTML = `
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                    <div class="p-6 text-center">
                        <i class="fas fa-cog fa-spin text-4xl text-orange-600 mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">System Maintenance</h3>
                        <p class="text-gray-600">Optimizing database and cleaning up temporary files...</p>
                        <div class="mt-4 bg-gray-200 rounded-full h-2">
                            <div class="bg-orange-600 h-2 rounded-full transition-all duration-1000" style="width: 0%" id="maintenance-progress"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(maintenanceModal);

        // Simulate maintenance progress
        let progress = 0;
        const progressBar = maintenanceModal.querySelector('#maintenance-progress');
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                setTimeout(() => {
                    maintenanceModal.remove();
                    alert('System maintenance completed successfully!');
                }, 1000);
            }
            progressBar.style.width = progress + '%';
        }, 500);
    }
}

// QR Scanner Functionality
function openQRScanner() {
    closeEmergencyPanel();

    // Check if camera is available
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        showNotification('Camera not available on this device', 'error');
        return;
    }

    // For now, simulate QR scanning with a demo
    showNotification('QR Scanner activated. Demo mode: scanning for patient...', 'info');

    // Simulate scanning process
    setTimeout(() => {
        const demoPatientId = 'ZH-2024-001';
        showNotification(`QR Code detected: ${demoPatientId}`, 'success');

        // Auto-open patient search with the scanned ID
        openPatientSearch();
        document.getElementById('quick-search-input').value = demoPatientId;
        performQuickSearch();
    }, 2000);
}

// Patient Search with AJAX
let searchTimeout;
document.getElementById('quick-search-input')?.addEventListener('input', function(e) {
    const query = e.target.value.trim();

    clearTimeout(searchTimeout);

    if (query.length < 2) {
        document.getElementById('search-results').innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-search text-4xl mb-4"></i>
                <p>Start typing to search for patients</p>
            </div>
        `;
        return;
    }

    // Show loading state
    document.getElementById('search-results').innerHTML = `
        <div class="text-center text-gray-500 py-8">
            <i class="fas fa-spinner fa-spin text-4xl mb-4"></i>
            <p>Searching patients...</p>
        </div>
    `;

    // Debounce search
    searchTimeout = setTimeout(() => {
        fetch(`{% url 'api:search_patients_ajax' %}?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data.patients);
            })
            .catch(error => {
                console.error('Search error:', error);
                document.getElementById('search-results').innerHTML = `
                    <div class="text-center text-red-500 py-8">
                        <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                        <p>Search failed. Please try again.</p>
                    </div>
                `;
            });
    }, 300);
});

function displaySearchResults(patients) {
    const resultsContainer = document.getElementById('search-results');

    if (patients.length === 0) {
        resultsContainer.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-user-slash text-4xl mb-4"></i>
                <p>No patients found matching your search</p>
            </div>
        `;
        return;
    }

    const resultsHTML = patients.map(patient => `
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg mb-3 hover:bg-gray-50 transition-colors">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-medical-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-medical-600"></i>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900">${patient.full_name}</h4>
                    <p class="text-sm text-gray-600">ID: ${patient.zimhealth_id}</p>
                    <p class="text-sm text-gray-500">Age: ${patient.age} | Phone: ${patient.phone_number}</p>
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="/api/patients/${patient.zimhealth_id}/"
                   class="px-3 py-1 bg-medical-600 text-white text-sm rounded hover:bg-medical-700 transition-colors">
                    View
                </a>
                <a href="/api/patients/${patient.zimhealth_id}/medical-records/new/"
                   class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors">
                    Record
                </a>
            </div>
        </div>
    `).join('');

    resultsContainer.innerHTML = resultsHTML;
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.id === 'patient-search-modal') {
        closePatientSearch();
    }
    if (e.target.id === 'emergency-modal') {
        closeEmergencyPanel();
    }
    if (e.target.id === 'bulk-actions-modal') {
        closeBulkActions();
    }
});

// Modal close on Escape key only
document.addEventListener('keydown', function(e) {
    // Escape to close modals
    if (e.key === 'Escape') {
        closePatientSearch();
        closeEmergencyPanel();
        closeBulkActions();
    }
});

// Update command center notification badge
function updateCommandNotificationBadge() {
    fetch('{% url "api:unread_notifications_count_ajax" %}')
        .then(response => response.json())
        .then(data => {
            const badge = document.getElementById('command-notification-badge');
            if (data.unread_count > 0) {
                badge.textContent = data.unread_count;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        })
        .catch(error => console.error('Error updating notification badge:', error));
}

// Update notification badge on page load and periodically
updateCommandNotificationBadge();
setInterval(updateCommandNotificationBadge, 30000);

// Ensure quick action links work properly
document.addEventListener('DOMContentLoaded', function() {
    console.log('Setting up command center links...');

    // Make sure all quick-action links are properly styled and clickable
    document.querySelectorAll('a.quick-action').forEach(function(link, index) {
        console.log(`Setting up link ${index}: ${link.href}`);

        // Ensure the link is clickable
        link.style.position = 'relative';
        link.style.zIndex = '10';
        link.style.cursor = 'pointer';

        // Remove any existing event listeners that might interfere
        link.onclick = null;

        // Add click handler to ensure navigation works
        link.addEventListener('click', function(e) {
            console.log('Command center link clicked:', this.href);

            // Add visual feedback
            this.style.opacity = '0.7';

            // Ensure we have a valid URL
            if (this.href && this.href !== '#') {
                console.log('Navigating to:', this.href);

                // Show a brief loading indicator
                const originalText = this.querySelector('span').textContent;
                const span = this.querySelector('span');
                span.textContent = 'Loading...';

                // Reset after a short delay (in case navigation is slow)
                setTimeout(() => {
                    span.textContent = originalText;
                    this.style.opacity = '1';
                }, 1000);

                // Let the browser handle the navigation naturally
                return true;
            } else {
                console.error('Invalid link:', this.href);
                e.preventDefault();
                return false;
            }
        });

        // Add visual feedback on hover
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    console.log('Command center setup complete');
});

// Test Command Center Links Function
function testCommandCenterLinks() {
    const links = [
        { name: 'Register Patient', url: '{% url "api:patient_create" %}' },
        { name: 'Schedule Visit', url: '{% url "api:appointment_create" %}' },
        { name: 'Medical Record', url: '{% url "api:medical_record_create" %}' },
        { name: 'Prescribe', url: '{% url "api:prescription_create" %}' },
        { name: 'Analytics', url: '{% url "api:analytics" %}' },
        { name: 'Notifications', url: '{% url "api:notifications" %}' }
    ];

    let results = [];
    let testCount = 0;

    showNotification('Testing Command Center links...', 'info');

    links.forEach((link, index) => {
        setTimeout(() => {
            fetch(link.url, { method: 'HEAD' })
                .then(response => {
                    const status = response.ok ? '✅ OK' : `❌ Error (${response.status})`;
                    results.push(`${link.name}: ${status}`);
                    testCount++;

                    if (testCount === links.length) {
                        // Show results
                        const resultText = results.join('\n');
                        console.log('Command Center Test Results:\n' + resultText);

                        const allPassed = results.every(r => r.includes('✅'));
                        const message = allPassed ?
                            'All Command Center links are working!' :
                            'Some Command Center links have issues. Check console for details.';
                        const type = allPassed ? 'success' : 'warning';

                        showNotification(message, type);
                    }
                })
                .catch(error => {
                    results.push(`${link.name}: ❌ Network Error`);
                    testCount++;

                    if (testCount === links.length) {
                        console.log('Command Center Test Results:\n' + results.join('\n'));
                        showNotification('Command Center test completed with errors. Check console.', 'error');
                    }
                });
        }, index * 200); // Stagger requests
    });
}
</script>
{% endblock %}
{% endblock %}
