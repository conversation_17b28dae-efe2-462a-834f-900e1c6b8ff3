{% extends 'base.html' %}

{% block title %}Access Denied - ZimHealth-ID{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-yellow-50 to-medical-50">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto h-32 w-32 bg-yellow-100 rounded-full flex items-center justify-center mb-8">
            <i class="fas fa-shield-alt text-yellow-600 text-5xl"></i>
        </div>

        <!-- Error Message -->
        <div>
            <h1 class="text-6xl font-bold text-gray-900 mb-4">403</h1>
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p class="text-gray-600 mb-8">
                You don't have permission to access this resource. This could be due to insufficient privileges or security restrictions.
            </p>
        </div>

        <!-- Error Card -->
        <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
            <div class="space-y-6">
                <!-- Security Notice -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-lock text-yellow-500 mr-3"></i>
                        <div class="text-left">
                            <h3 class="text-sm font-medium text-yellow-800">Protected Resource</h3>
                            <p class="text-sm text-yellow-700 mt-1">
                                This page is restricted to authorized users only.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Possible Reasons -->
                <div class="text-left">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Possible reasons:</h3>
                    <ul class="space-y-3 text-sm text-gray-600">
                        <li class="flex items-start">
                            <i class="fas fa-user-times text-red-500 mr-3 mt-0.5"></i>
                            <span>You're not logged in to your account</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-user-lock text-red-500 mr-3 mt-0.5"></i>
                            <span>Your account doesn't have the required permissions</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-clock text-red-500 mr-3 mt-0.5"></i>
                            <span>Your session has expired</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-ban text-red-500 mr-3 mt-0.5"></i>
                            <span>Access to this resource has been restricted</span>
                        </li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-4">
                    {% if not user.is_authenticated %}
                        <a href="{% url 'zhid_auth:login' %}?next={{ request.get_full_path|urlencode }}" 
                           class="w-full bg-medical-600 text-white py-3 px-4 rounded-lg hover:bg-medical-700 focus:ring-2 focus:ring-medical-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Sign In</span>
                        </a>
                    {% else %}
                        <button onclick="location.reload()" 
                                class="w-full bg-yellow-600 text-white py-3 px-4 rounded-lg hover:bg-yellow-700 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                            <i class="fas fa-redo"></i>
                            <span>Try Again</span>
                        </button>
                    {% endif %}

                    <button onclick="history.back()" 
                            class="w-full bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-arrow-left"></i>
                        <span>Go Back</span>
                    </button>

                    <a href="{% if user.is_authenticated %}{% url 'zhid_auth:profile' %}{% else %}{% url 'zhid_auth:login' %}{% endif %}" 
                       class="w-full bg-health-600 text-white py-3 px-4 rounded-lg hover:bg-health-700 focus:ring-2 focus:ring-health-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center space-x-2">
                        <i class="fas fa-home"></i>
                        <span>Go to Homepage</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Help Section -->
        {% if user.is_authenticated %}
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mr-3 mt-0.5"></i>
                    <div class="text-left">
                        <h3 class="text-sm font-medium text-blue-800">Need access to this resource?</h3>
                        <p class="text-sm text-blue-700 mt-1">
                            Contact your system administrator to request the necessary permissions for your account.
                        </p>
                        <div class="mt-3">
                            <a href="#" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                                Request Access →
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Additional Info -->
        <div class="text-center text-sm text-gray-500">
            <p>Error Code: 403 | User: {{ user.username|default:"Anonymous" }}</p>
            <p class="mt-2">
                If you believe you should have access, please 
                <a href="#" class="text-medical-600 hover:text-medical-700 font-medium">contact support</a>
            </p>
        </div>
    </div>
</div>
{% endblock %}
