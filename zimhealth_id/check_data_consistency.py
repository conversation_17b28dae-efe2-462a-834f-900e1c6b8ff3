#!/usr/bin/env python
"""
Quick script to check and fix data consistency issues in ZimHealth-ID
Run this from the zimhealth_id directory: python check_data_consistency.py
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zimhealth_id.settings')
django.setup()

from django.core.management import call_command
from django.utils import timezone
from api.models import Patient, MedicalRecord, Prescription, Appointment

def main():
    print("=" * 60)
    print("ZimHealth-ID Data Consistency Check")
    print("=" * 60)
    
    # Show current statistics
    print("\n📊 Current Database Statistics:")
    print(f"  • Total Patients: {Patient.objects.filter(is_active=True).count()}")
    print(f"  • Total Appointments: {Appointment.objects.count()}")
    print(f"  • Total Medical Records: {MedicalRecord.objects.count()}")
    print(f"  • Total Prescriptions: {Prescription.objects.count()}")
    
    # Check for basic relationship issues
    print("\n🔍 Quick Relationship Check:")
    
    # Check appointments
    orphaned_appointments = Appointment.objects.filter(patient__isnull=True).count()
    inactive_patient_appointments = Appointment.objects.filter(
        patient__is_active=False, 
        status='scheduled'
    ).count()
    
    print(f"  • Orphaned Appointments: {orphaned_appointments}")
    print(f"  • Scheduled Appointments for Inactive Patients: {inactive_patient_appointments}")
    
    # Check medical records
    orphaned_records = MedicalRecord.objects.filter(patient__isnull=True).count()
    print(f"  • Orphaned Medical Records: {orphaned_records}")
    
    # Check prescriptions
    orphaned_prescriptions = Prescription.objects.filter(medical_record__isnull=True).count()
    expired_active_prescriptions = Prescription.objects.filter(
        status='active',
        end_date__lt=timezone.now().date()
    ).count()
    
    print(f"  • Orphaned Prescriptions: {orphaned_prescriptions}")
    print(f"  • Expired Active Prescriptions: {expired_active_prescriptions}")
    
    # Check patients
    patients_without_qr = Patient.objects.filter(qr_code__isnull=True).count()
    print(f"  • Patients Without QR Codes: {patients_without_qr}")
    
    total_issues = (orphaned_appointments + inactive_patient_appointments + 
                   orphaned_records + orphaned_prescriptions + 
                   expired_active_prescriptions + patients_without_qr)
    
    if total_issues == 0:
        print("\n✅ No data consistency issues found!")
        print("All relationships are properly maintained.")
    else:
        print(f"\n⚠️  Found {total_issues} potential issues")
        print("\nRunning detailed consistency check...")
        
        # Run the management command for detailed check
        try:
            call_command('ensure_data_consistency', verbosity=1)
        except Exception as e:
            print(f"Error running consistency check: {e}")
            print("You can run it manually with:")
            print("python manage.py ensure_data_consistency --verbose")
            print("python manage.py ensure_data_consistency --fix --verbose")
    
    print("\n" + "=" * 60)
    print("Data consistency check complete!")
    print("=" * 60)

if __name__ == "__main__":
    main()