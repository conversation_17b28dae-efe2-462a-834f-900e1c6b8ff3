#!/usr/bin/env python
"""
Test the medical records table alignment fix with high specificity CSS
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append('C:/Users/<USER>/PycharmProjects/ZimHealth-ID/zimhealth_id')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zimhealth_id.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User

def test_medical_records_alignment_fix():
    """Test the medical records table alignment fix"""
    
    print("🔧 TESTING MEDICAL RECORDS TABLE ALIGNMENT FIX")
    print("=" * 60)
    
    # Create test user
    user, created = User.objects.get_or_create(
        username='alignmentfix',
        defaults={'email': '<EMAIL>', 'first_name': 'Alignment', 'last_name': 'Fix'}
    )
    if created:
        user.set_password('testpass123')
        user.save()
    
    # Create client and login
    client = Client()
    login_success = client.login(username='alignmentfix', password='testpass123')
    
    if not login_success:
        print("❌ Login failed")
        return
    
    print("✅ User authentication successful")
    
    # Test 1: Check Updated CSS with High Specificity
    print("\n1. 🎨 Testing High Specificity CSS Rules...")
    
    try:
        import requests
        response = requests.get('http://127.0.0.1:8000/static/assets/css/medical_records.css?v=**********', timeout=5)
        if response.status_code == 200:
            print("   ✅ Updated medical records CSS loads successfully")
            
            css_content = response.text
            
            # Check for high specificity rules
            high_specificity_checks = [
                ('table-layout: fixed !important', 'Fixed table layout with !important'),
                ('width: 22% !important', 'Patient column width with !important'),
                ('width: 14% !important', 'Date column width with !important'),
                ('width: 16% !important', 'Provider column width with !important'),
                ('width: 12% !important', 'Type column width with !important'),
                ('width: 24% !important', 'Diagnosis column width with !important'),
                ('width: 8% !important', 'Status column width with !important'),
                ('width: 4% !important', 'Actions column width with !important'),
                ('text-align: left !important', 'Left alignment with !important'),
                ('text-align: center !important', 'Center alignment with !important'),
                ('vertical-align: middle !important', 'Vertical alignment with !important'),
                ('table.medical-records-table', 'High specificity table selector'),
                ('th:nth-child(1)', 'First column nth-child selector'),
                ('td:nth-child(1)', 'First column data nth-child selector'),
                ('th:nth-child(7)', 'Last column nth-child selector'),
                ('td:nth-child(7)', 'Last column data nth-child selector')
            ]
            
            for check, description in high_specificity_checks:
                if check in css_content:
                    print(f"   ✅ {description} - Present")
                else:
                    print(f"   ❌ {description} - Missing")
                    
        else:
            print(f"   ❌ CSS file failed to load (Status: {response.status_code})")
            
    except Exception as e:
        print(f"   ❌ Error loading CSS file: {e}")
    
    # Test 2: Check Template Structure
    print("\n2. 📋 Testing Template Structure...")
    
    response = client.get('/api/medical-records/')
    if response.status_code == 200:
        print("   ✅ Medical records page loads successfully")
        
        content = response.content.decode()
        
        # Check for proper column classes in template
        template_checks = [
            ('class="patient-column"', 'Patient column class in template'),
            ('class="date-column"', 'Date column class in template'),
            ('class="provider-column"', 'Provider column class in template'),
            ('class="type-column"', 'Type column class in template'),
            ('class="diagnosis-column"', 'Diagnosis column class in template'),
            ('class="status-column"', 'Status column class in template'),
            ('class="actions-column"', 'Actions column class in template'),
            ('medical_records.css?v=**********', 'Updated CSS version'),
            ('<colgroup>', 'Column group definition'),
            ('width: 22%', 'Patient column width in colgroup'),
            ('width: 14%', 'Date column width in colgroup'),
            ('width: 16%', 'Provider column width in colgroup'),
            ('width: 12%', 'Type column width in colgroup'),
            ('width: 24%', 'Diagnosis column width in colgroup'),
            ('width: 8%', 'Status column width in colgroup'),
            ('width: 4%', 'Actions column width in colgroup')
        ]
        
        for check, description in template_checks:
            if check in content:
                print(f"   ✅ {description} - Present")
            else:
                print(f"   ❌ {description} - Missing")
                
    else:
        print(f"   ❌ Medical records page failed to load (Status: {response.status_code})")
    
    print("\n" + "=" * 60)
    print("🎯 MEDICAL RECORDS ALIGNMENT FIX TEST COMPLETE!")
    
    print(f"\n🔧 HIGH SPECIFICITY FIXES APPLIED:")
    print(f"1. ✅ Added !important to all width declarations")
    print(f"2. ✅ Added !important to all text-align declarations")
    print(f"3. ✅ Added !important to table-layout: fixed")
    print(f"4. ✅ Added !important to vertical-align: middle")
    print(f"5. ✅ Used table.medical-records-table for higher specificity")
    print(f"6. ✅ Added nth-child selectors as backup enforcement")
    print(f"7. ✅ Updated cache-busting version to v=**********")
    
    print(f"\n📊 COLUMN ALIGNMENT ENFORCEMENT:")
    print(f"• Patient (Column 1): 22% width, left-aligned")
    print(f"• Date & Time (Column 2): 14% width, center-aligned")
    print(f"• Healthcare Provider (Column 3): 16% width, center-aligned")
    print(f"• Record Type (Column 4): 12% width, center-aligned")
    print(f"• Diagnosis (Column 5): 24% width, left-aligned")
    print(f"• Status (Column 6): 8% width, center-aligned")
    print(f"• Actions (Column 7): 4% width, center-aligned")
    
    print(f"\n🚀 TO SEE THE FIXED ALIGNMENT:")
    print(f"1. Go to: http://127.0.0.1:8000/api/medical-records/")
    print(f"2. Hard refresh (Ctrl+Shift+R) to clear cache completely")
    print(f"3. Check that:")
    print(f"   - 'Consultation' appears under 'Record Type' column")
    print(f"   - 'Active' appears under 'Status' column")
    print(f"   - Action buttons appear under 'Actions' column")
    print(f"   - All data aligns perfectly with headers")
    print(f"4. If still misaligned, try:")
    print(f"   - Clear browser cache completely")
    print(f"   - Open in incognito/private mode")
    print(f"   - Check browser developer tools for CSS conflicts")

if __name__ == '__main__':
    test_medical_records_alignment_fix()
