# ZimHealth-ID 🏥

**A comprehensive healthcare identity management system for Zimbabwe**

[![Django](https://img.shields.io/badge/Django-5.2.4-green.svg)](https://www.djangoproject.com/)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.0+-06B6D4.svg)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

ZimHealth-ID is a modern, secure, and comprehensive healthcare identity management platform designed specifically for Zimbabwe's healthcare system. It provides healthcare providers with reliable patient identification, medical record management, appointment scheduling, and prescription tracking capabilities.

## 🌟 Features

### 🔐 **Authentication & Security**
- **Secure User Registration & Login** with email verification
- **Password Reset** functionality with secure token-based system
- **User Profile Management** with avatar upload
- **Session Management** with automatic logout
- **CSRF Protection** and security best practices
- **Login Attempt Tracking** for enhanced security

### 👥 **Patient Management**
- **Unique ZimHealth ID Generation** (Format: ZH-YYYY-XXXXXX)
- **Comprehensive Patient Profiles** with personal and medical information
- **QR Code Generation** for quick patient identification
- **National ID Validation** for Zimbabwean citizens
- **Blood Type & Allergy Tracking**
- **Emergency Contact Management**
- **Patient Search & Filtering** capabilities

### 📋 **Medical Records**
- **Digital Medical Records** with comprehensive visit documentation
- **Vital Signs Tracking** (Temperature, Blood Pressure, Heart Rate, Weight, Height)
- **BMI Calculation** and health metrics
- **Diagnosis & Treatment Documentation**
- **Doctor & Facility Information** tracking
- **Medical History Timeline** for each patient
- **Search & Filter** by patient, diagnosis, doctor, or facility

### 💊 **Prescription Management**
- **Digital Prescription Creation** and management
- **Medication Tracking** with dosage and frequency
- **Prescription Status Management** (Active, Completed, Discontinued)
- **Refill Management** and quantity tracking
- **Expiration Alerts** for active prescriptions
- **Drug Interaction Warnings** (planned feature)

### 📅 **Appointment Scheduling**
- **Comprehensive Appointment Management**
- **Multiple Appointment Types** (Consultation, Follow-up, Check-up, Screening)
- **Doctor & Facility Assignment**
- **Appointment Status Tracking** (Scheduled, Completed, Cancelled, No-show)
- **Priority Levels** for urgent appointments
- **Conflict Prevention** with unique scheduling constraints
- **Appointment Reminders** (planned feature)

### 📊 **Analytics & Reporting**
- **Real-time Dashboard** with key performance indicators
- **Patient Registration Trends**
- **Appointment Statistics** and completion rates
- **Prescription Analytics** and medication trends
- **Healthcare Facility Performance** metrics
- **System Performance Monitoring**
- **Top Diagnoses** and health trend analysis

### 🎨 **Modern User Interface**
- **Responsive Design** optimized for mobile and desktop
- **Tailwind CSS** for modern, clean aesthetics
- **Medical-themed Color Palette** (Medical Blues & Health Greens)
- **Intuitive Navigation** with role-based access
- **Interactive Components** with smooth transitions
- **Accessibility Features** following WCAG guidelines

## 🏗️ **System Architecture**

### **Backend Framework**
- **Django 5.2.4** - Robust Python web framework
- **PostgreSQL/SQLite** - Reliable database management
- **Django ORM** - Object-relational mapping
- **Django Admin** - Administrative interface

### **Frontend Technologies**
- **Tailwind CSS 3.0+** - Utility-first CSS framework
- **Font Awesome 6.4** - Icon library
- **Inter Font Family** - Modern typography
- **Vanilla JavaScript** - Interactive functionality
- **Responsive Grid System** - Mobile-first design

### **Security Features**
- **CSRF Protection** - Cross-site request forgery prevention
- **SQL Injection Prevention** - Parameterized queries
- **XSS Protection** - Cross-site scripting prevention
- **Secure Password Hashing** - Django's built-in authentication
- **Session Security** - Secure session management

## 📱 **User Interface**

### **Dashboard Overview**
- **Statistics Cards** - Total patients, appointments, prescriptions
- **Recent Activity Feed** - Latest system activities
- **Quick Actions** - Fast access to common tasks
- **Upcoming Appointments** - Schedule overview
- **Performance Metrics** - System health indicators

### **Patient Management Interface**
- **Patient List View** - Searchable and filterable patient directory
- **Patient Detail View** - Comprehensive patient information
- **Patient Registration Form** - New patient onboarding
- **Medical History Timeline** - Chronological health records
- **QR Code Display** - Quick identification system

### **Medical Records Interface**
- **Record Creation Form** - Comprehensive visit documentation
- **Vital Signs Input** - Structured health metrics
- **Diagnosis & Treatment** - Medical assessment tools
- **Prescription Integration** - Seamless medication management
- **Print & Export** - Record sharing capabilities

### **Appointment Management**
- **Calendar View** - Visual appointment scheduling
- **List View** - Detailed appointment information
- **Status Management** - Appointment lifecycle tracking
- **Conflict Detection** - Scheduling optimization
- **Bulk Operations** - Efficient appointment management

## 🚀 **Getting Started**

### **Prerequisites**
- Python 3.8 or higher
- pip (Python package installer)
- Virtual environment (recommended)
- Git

### **Installation**

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/ZimHealth-ID.git
   cd ZimHealth-ID
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv .zhid_venv

   # On Windows
   .zhid_venv\Scripts\activate

   # On macOS/Linux
   source .zhid_venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Navigate to project directory**
   ```bash
   cd zimhealth_id
   ```

5. **Run database migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

6. **Create superuser account**
   ```bash
   python manage.py createsuperuser
   ```

7. **Create sample data (optional)**
   ```bash
   python manage.py create_sample_data --patients 5
   ```

8. **Start development server**
   ```bash
   python manage.py runserver
   ```

9. **Access the application**
   - **Main Application**: http://127.0.0.1:8000/
   - **Admin Interface**: http://127.0.0.1:8000/admin/
   - **API Dashboard**: http://127.0.0.1:8000/api/

### **Default Login Credentials**
- **Admin User**: Use the superuser credentials you created
- **Sample Users**: Created through the registration process

## 📁 **Project Structure**

```
ZimHealth-ID/
├── zimhealth_id/                 # Main Django project
│   ├── zimhealth_id/            # Project settings
│   │   ├── settings.py          # Django configuration
│   │   ├── urls.py              # URL routing
│   │   └── wsgi.py              # WSGI configuration
│   ├── zhid_auth/               # Authentication app
│   │   ├── models.py            # User profile models
│   │   ├── views.py             # Authentication views
│   │   ├── forms.py             # Authentication forms
│   │   └── urls.py              # Auth URL patterns
│   ├── api/                     # Core healthcare app
│   │   ├── models.py            # Patient, MedicalRecord, etc.
│   │   ├── views.py             # Healthcare views
│   │   ├── admin.py             # Admin configuration
│   │   └── urls.py              # API URL patterns
│   ├── templates/               # HTML templates
│   │   ├── base.html            # Base template
│   │   ├── zhid_auth/           # Authentication templates
│   │   ├── api/                 # Healthcare templates
│   │   └── dashboard/           # Dashboard templates
│   ├── static/                  # Static files
│   └── media/                   # User uploads
├── requirements.txt             # Python dependencies
├── README.md                    # Project documentation
└── .gitignore                   # Git ignore rules
```

## 🗄️ **Database Models**

### **Patient Model**
- **ZimHealth ID**: Unique identifier (ZH-YYYY-XXXXXX)
- **Personal Information**: Name, National ID, Date of Birth, Gender
- **Contact Details**: Phone, Address, Emergency Contact
- **Medical Information**: Blood Type, Allergies
- **System Fields**: Registration Date, Active Status

### **MedicalRecord Model**
- **Patient Reference**: Foreign key to Patient
- **Visit Information**: Date, Facility, Doctor
- **Vital Signs**: Temperature, Blood Pressure, Heart Rate, Weight, Height
- **Medical Details**: Diagnosis, Treatment, Notes
- **Calculated Fields**: BMI, Age at visit

### **Prescription Model**
- **Medical Record Reference**: Foreign key to MedicalRecord
- **Medication Details**: Name, Dosage, Frequency, Duration
- **Management**: Start/End dates, Refills, Quantity
- **Status Tracking**: Active, Completed, Discontinued
- **Instructions**: Special administration notes

### **Appointment Model**
- **Patient Reference**: Foreign key to Patient
- **Scheduling**: Date, Time, Duration
- **Healthcare Provider**: Doctor, Facility
- **Appointment Details**: Type, Priority, Status
- **Additional Information**: Reason, Notes

### **UserProfile Model (Authentication)**
- **User Extension**: Django User model extension
- **Profile Information**: Bio, Avatar, Phone, Date of Birth
- **Verification**: Email verification status
- **System Fields**: Creation date, Last login tracking

## 🔧 **Management Commands**

### **Create Sample Data**
```bash
python manage.py create_sample_data --patients 10 --records 5 --appointments 3
```
- Creates sample patients with medical records and appointments
- Useful for development and testing
- Generates realistic Zimbabwean data

### **Database Management**
```bash
# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Reset database (development only)
python manage.py flush
```

## 🎯 **Usage Guide**

### **For Healthcare Providers**

1. **Patient Registration**
   - Navigate to Patients → Add New Patient
   - Fill in patient information including National ID
   - System generates unique ZimHealth ID
   - QR code created for quick identification

2. **Medical Record Creation**
   - Select patient from patient list
   - Click "New Medical Record"
   - Document visit details, vital signs, diagnosis
   - Add prescriptions if needed

3. **Appointment Scheduling**
   - Go to Appointments → Schedule New
   - Select patient and appointment type
   - Set date, time, and healthcare provider
   - System prevents scheduling conflicts

4. **Prescription Management**
   - Access through Medical Records or direct Prescriptions page
   - Track medication status and refills
   - Monitor expiring prescriptions

### **For System Administrators**

1. **User Management**
   - Access Django Admin at `/admin/`
   - Create healthcare provider accounts
   - Manage user permissions and roles

2. **System Monitoring**
   - Use Analytics Dashboard for insights
   - Monitor system performance metrics
   - Track usage statistics

3. **Data Management**
   - Export patient data for reporting
   - Backup database regularly
   - Monitor data integrity

## 🔒 **Security Considerations**

### **Data Protection**
- **Patient Data Encryption**: Sensitive information encrypted at rest
- **Secure Transmission**: HTTPS enforced in production
- **Access Control**: Role-based permissions
- **Audit Logging**: Track all data access and modifications

### **Compliance**
- **HIPAA Considerations**: Healthcare data privacy standards
- **Local Regulations**: Zimbabwe healthcare data protection
- **Data Retention**: Configurable retention policies
- **Patient Consent**: Consent management system

### **Authentication Security**
- **Strong Password Requirements**: Enforced password complexity
- **Session Management**: Secure session handling
- **Failed Login Protection**: Account lockout after failed attempts
- **Two-Factor Authentication**: Planned future enhancement

## 🚀 **Deployment**

### **Production Deployment**

1. **Environment Setup**
   ```bash
   # Set environment variables
   export DEBUG=False
   export SECRET_KEY='your-secret-key'
   export DATABASE_URL='your-database-url'
   ```

2. **Static Files**
   ```bash
   python manage.py collectstatic
   ```

3. **Database Migration**
   ```bash
   python manage.py migrate
   ```

4. **Web Server Configuration**
   - Use Gunicorn or uWSGI for Python application
   - Configure Nginx for static files and reverse proxy
   - Set up SSL certificates for HTTPS

### **Docker Deployment** (Planned)
```dockerfile
# Dockerfile configuration for containerized deployment
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "zimhealth_id.wsgi:application"]
```

## 🧪 **Testing**

### **Running Tests**
```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test zhid_auth
python manage.py test api

# Run with coverage
coverage run --source='.' manage.py test
coverage report
```

### **Test Coverage**
- **Model Tests**: Database model validation
- **View Tests**: HTTP response and template rendering
- **Form Tests**: Form validation and processing
- **Integration Tests**: End-to-end functionality

## 📈 **Performance Optimization**

### **Database Optimization**
- **Indexing**: Optimized database indexes for common queries
- **Query Optimization**: Efficient ORM queries with select_related
- **Connection Pooling**: Database connection management
- **Caching**: Redis/Memcached for session and query caching

### **Frontend Optimization**
- **Asset Compression**: Minified CSS and JavaScript
- **Image Optimization**: Compressed images and WebP format
- **CDN Integration**: Content delivery network for static assets
- **Lazy Loading**: Progressive content loading

## 🔄 **API Integration** (Future Enhancement)

### **RESTful API**
- **Patient API**: CRUD operations for patient data
- **Medical Records API**: Electronic health record integration
- **Appointment API**: Scheduling system integration
- **Authentication API**: Token-based authentication

### **Third-party Integrations**
- **Laboratory Systems**: Lab result integration
- **Pharmacy Systems**: Prescription fulfillment
- **Insurance Systems**: Claims processing
- **Government Health Systems**: National health database sync

## 🤝 **Contributing**

We welcome contributions to ZimHealth-ID! Please follow these guidelines:

### **Development Setup**
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

### **Code Standards**
- Follow PEP 8 Python style guide
- Write comprehensive tests for new features
- Update documentation for API changes
- Use meaningful commit messages

### **Issue Reporting**
- Use GitHub Issues for bug reports and feature requests
- Provide detailed reproduction steps for bugs
- Include system information and error logs

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 **Team**

- **Lead Developer**: [Your Name]
- **UI/UX Designer**: [Designer Name]
- **Healthcare Consultant**: [Consultant Name]

## 🙏 **Acknowledgments**

- **Django Community** for the excellent web framework
- **Tailwind CSS** for the utility-first CSS framework
- **Zimbabwe Ministry of Health** for healthcare system insights
- **Healthcare Providers** for requirements and feedback
- **Open Source Community** for various libraries and tools

## 📞 **Support**

For support and questions:

- **Email**: <EMAIL>
- **Documentation**: [Project Wiki](https://github.com/yourusername/ZimHealth-ID/wiki)
- **Issues**: [GitHub Issues](https://github.com/yourusername/ZimHealth-ID/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/ZimHealth-ID/discussions)

## 🗺️ **Roadmap**

### **Version 2.0** (Planned)
- [ ] Mobile Application (React Native)
- [ ] Advanced Analytics and Reporting
- [ ] Telemedicine Integration
- [ ] Multi-language Support (Shona, Ndebele, English)
- [ ] Offline Capability for Remote Areas

### **Version 3.0** (Future)
- [ ] AI-powered Diagnosis Assistance
- [ ] Blockchain for Medical Records
- [ ] IoT Device Integration
- [ ] Advanced Security Features (2FA, Biometrics)

---

**ZimHealth-ID** - Empowering Zimbabwe's Healthcare System with Modern Technology 🇿🇼

*Built with ❤️ for Zimbabwe's Healthcare Community*
