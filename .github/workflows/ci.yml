name: CI

on:
  pull_request:
    branches: ["main", "develop"]
  push:
    branches: ["main", "develop"]

jobs:
  lint-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install black isort flake8 pytest

      - name: Black formatting check
        run: black --check .

      - name: isort import ordering check
        run: isort --profile black --check-only .

      - name: flake8 linting
        run: flake8 .

      - name: Run tests
        run: pytest -q
